# DyFlow v3.4 Phase 1 完整測試計劃

**測試目標**: 確保所有 Agent 和組件達到 100% 正常運行狀態
**測試範圍**: 使用真實數據，僅交易執行使用模擬
**測試框架**: Agno Framework Workflow

## 📋 **測試流程清單**

### **階段 1: 環境配置檢查** (預計 5 分鐘)
- [ ] 1.1 檢查 .env 配置文件
- [ ] 1.2 驗證必要的環境變量
- [ ] 1.3 檢查 Ollama 服務狀態
- [ ] 1.4 驗證 RPC 端點連接
- [ ] 1.5 檢查依賴包安裝

### **階段 2: Agno Framework 初始化** (預計 3 分鐘)
- [ ] 2.1 初始化 Agno Workflow
- [ ] 2.2 創建 Agent Team
- [ ] 2.3 驗證 Ollama 模型連接
- [ ] 2.4 測試 Agent 通訊機制

### **階段 3: 錢包管理器測試** (預計 5 分鐘)
- [ ] 3.1 UnifiedWalletManager 初始化
- [ ] 3.2 私鑰格式驗證
- [ ] 3.3 錢包連接測試 (BSC + Solana)
- [ ] 3.4 MPC 簽名測試
- [ ] 3.5 Nonce 管理測試

### **階段 4: 交易構建器測試** (預計 5 分鐘)
- [ ] 4.1 BSCTransactionBuilder 初始化
- [ ] 4.2 SolanaTransactionBuilder 初始化
- [ ] 4.3 Web3 連接測試
- [ ] 4.4 合約地址驗證
- [ ] 4.5 Gas 估算測試

### **階段 5: 市場情報 Agent 測試** (預計 10 分鐘)
- [ ] 5.1 EnhancedMarketIntelAgent 初始化
- [ ] 5.2 BSC 池子掃描 (真實數據)
- [ ] 5.3 Solana 池子掃描 (真實數據)
- [ ] 5.4 價格追蹤器測試
- [ ] 5.5 6 因子評分算法測試
- [ ] 5.6 事件發佈測試

### **階段 6: 策略執行器測試** (預計 8 分鐘)
- [ ] 6.1 LPStrategyExecutor 初始化
- [ ] 6.2 四種策略邏輯測試
- [ ] 6.3 價格範圍計算測試
- [ ] 6.4 風險評估測試
- [ ] 6.5 APR 估算測試

### **階段 7: 執行 Agent 測試** (預計 8 分鐘)
- [ ] 7.1 ExecutionAgent 初始化
- [ ] 7.2 狀態機測試
- [ ] 7.3 BSC 交易簽名 (模擬)
- [ ] 7.4 Solana 交易簽名 (模擬)
- [ ] 7.5 交易廣播 (模擬)
- [ ] 7.6 確認等待 (模擬)

### **階段 8: DCA 退出策略測試** (預計 5 分鐘)
- [ ] 8.1 DCAExitStrategy 初始化
- [ ] 8.2 退出計劃創建
- [ ] 8.3 批次計算邏輯
- [ ] 8.4 觸發條件測試
- [ ] 8.5 執行流程測試 (模擬)

### **階段 9: Agent 間通訊測試** (預計 8 分鐘)
- [ ] 9.1 Agno 事件發佈測試
- [ ] 9.2 Agent 訂閱測試
- [ ] 9.3 bus.pool 事件流
- [ ] 9.4 bus.lpplan 事件流
- [ ] 9.5 bus.tx 事件流
- [ ] 9.6 ExitRequest 事件流

### **階段 10: 端到端工作流程測試** (預計 15 分鐘)
- [ ] 10.1 完整的 8-phase 啟動序列
- [ ] 10.2 池子掃描 → 策略生成 → 執行 (模擬)
- [ ] 10.3 風險監控 → DCA 退出 (模擬)
- [ ] 10.4 錯誤處理和恢復
- [ ] 10.5 系統穩定性測試 (10 分鐘運行)

### **階段 11: 錯誤處理和恢復測試** (預計 5 分鐘)
- [ ] 11.1 網絡中斷恢復
- [ ] 11.2 API 限流處理
- [ ] 11.3 Agent 重啟恢復
- [ ] 11.4 異常狀態處理
- [ ] 11.5 日誌記錄驗證

## 🎯 **成功標準**

### **必須達成 (100%)**:
- ✅ 所有 Agent 成功初始化
- ✅ 真實數據獲取正常 (BSC + Solana)
- ✅ Agno 事件通訊正常
- ✅ 模擬交易執行完整
- ✅ 系統穩定運行 10 分鐘

### **性能指標**:
- 🎯 Agent ACK 延遲 ≤ 3 秒
- 🎯 池子掃描間隔: BSC 15s, Solana 12s
- 🎯 健康檢查分數 ≥ 90%
- 🎯 內存使用 ≤ 2GB
- 🎯 CPU 使用 ≤ 50%

### **數據質量**:
- 📊 BSC 池子數據 ≥ 10 個有效池子
- 📊 Solana 池子數據 ≥ 10 個有效池子
- 📊 價格數據更新頻率 ≤ 30 秒
- 📊 評分算法覆蓋率 100%

## 🔧 **測試工具和方法**

### **自動化測試**:
```python
# 使用 Agno Workflow 執行測試
workflow = DyFlowAgnoWorkflow()
for response in workflow.run():
    print(response.content)
```

### **手動驗證**:
- 檢查日誌輸出
- 監控系統資源
- 驗證數據準確性
- 測試用戶界面響應

### **測試數據**:
- 真實 BSC RPC 數據
- 真實 Solana RPC 數據
- 真實 PancakeSwap Subgraph 數據
- 真實 Meteora API 數據
- 模擬錢包私鑰 (測試用)

## 📝 **測試記錄格式**

每個測試步驟記錄:
```
✅/❌/⚠️ [階段.步驟] 測試名稱
   目的: 測試目的說明
   結果: 成功/失敗/部分成功
   耗時: X.X 秒
   詳情: 具體結果或錯誤信息
   修復: 如有問題的修復建議
```

## 🚀 **執行順序**

1. **準備階段** (5 分鐘): 環境檢查
2. **基礎測試** (20 分鐘): 組件單獨測試
3. **整合測試** (25 分鐘): Agent 間通訊和工作流
4. **穩定性測試** (10 分鐘): 持續運行驗證
5. **報告生成** (5 分鐘): 測試結果總結

**總預計時間**: 65 分鐘
**核心測試時間**: 45 分鐘
**穩定性驗證**: 10 分鐘
