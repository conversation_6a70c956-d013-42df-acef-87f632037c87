#!/usr/bin/env python3
"""
測試 DyFlow v3.3 事件驅動架構
驗證 MarketIntel → Strategy → Execution → RiskSentinel 事件流
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.events.dyflow_events import (
        EventType, PoolEvent, LPPlanEvent, TxEvent, ExitRequestEvent,
        event_bus, RiskProfile, StrategyType
    )
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保在項目根目錄運行此測試")
    sys.exit(1)

class TestEventFlow:
    """測試事件流"""
    
    def __init__(self):
        self.received_events = []
        self.setup_event_listeners()
    
    def setup_event_listeners(self):
        """設置事件監聽器"""
        # 監聽所有事件類型
        event_bus.subscribe(EventType.POOL_DISCOVERED, self.on_pool_event)
        event_bus.subscribe(EventType.LP_PLAN_CREATED, self.on_lpplan_event)
        event_bus.subscribe(EventType.TX_OPEN, self.on_tx_event)
        event_bus.subscribe(EventType.TX_CLOSED, self.on_tx_event)
        event_bus.subscribe(EventType.EXIT_REQUEST, self.on_exit_request)
    
    def on_pool_event(self, event: PoolEvent):
        """處理 bus.pool 事件"""
        print(f"📊 收到 bus.pool 事件: {event.pool_id} (TVL: ${event.tvl:,.0f})")
        self.received_events.append(('bus.pool', event))
    
    def on_lpplan_event(self, event: LPPlanEvent):
        """處理 bus.lpplan 事件"""
        print(f"📋 收到 bus.lpplan 事件: {event.plan_id} - {event.strategy.value}")
        print(f"   風險配置: IL={event.risk_profile.il_cut}, VaR={event.risk_profile.var_cut}")
        self.received_events.append(('bus.lpplan', event))
    
    def on_tx_event(self, event: TxEvent):
        """處理 bus.tx 事件"""
        status_icon = "📈" if event.status == "open" else "📉"
        print(f"{status_icon} 收到 bus.tx 事件: {event.position_id} - {event.status}")
        if event.exit_asset:
            print(f"   退出資產: {event.exit_asset} ({event.qty})")
        self.received_events.append(('bus.tx', event))
    
    def on_exit_request(self, event: ExitRequestEvent):
        """處理 ExitRequest 事件"""
        print(f"🚨 收到 ExitRequest 事件: {event.position_id}")
        print(f"   原因: {event.reason}, 退出資產: {event.exit_asset}")
        self.received_events.append(('ExitRequest', event))

async def test_complete_event_flow():
    """測試完整的事件流"""
    print("🧪 測試 DyFlow v3.3 事件驅動架構")
    print("=" * 50)
    
    # 創建測試監聽器
    test_flow = TestEventFlow()
    
    print("\n1️⃣ 模擬 MarketIntelAgent 發送 bus.pool 事件")
    pool_event = PoolEvent(
        pool_id="SOL/USDC",
        chain="sol",
        tvl=12000000,
        fee_tvl_pct=0.08,
        sigma=0.045,
        spread=0.0003
    )
    await event_bus.publish(EventType.POOL_DISCOVERED, pool_event)
    
    await asyncio.sleep(0.1)  # 等待事件處理
    
    print("\n2️⃣ 模擬 StrategyAgent 發送 bus.lpplan 事件")
    risk_profile = RiskProfile(
        il_cut=-0.08,
        var_cut=0.05,
        sigma_cut=0.05,
        holding_window=3600,
        exit_asset="SOL"
    )
    
    lpplan_event = LPPlanEvent(
        plan_id="plan_001",
        pool_id="SOL/USDC",
        strategy=StrategyType.SPOT_IMBALANCED_DAMM,
        k=0.038,
        notional_usd=2000,
        risk_profile=risk_profile,
        approved=True
    )
    await event_bus.publish(EventType.LP_PLAN_CREATED, lpplan_event)
    
    await asyncio.sleep(0.1)
    
    print("\n3️⃣ 模擬 ExecutionAgent 發送 bus.tx (open) 事件")
    tx_open_event = TxEvent(
        position_id="pos_001",
        tx_hash="jupiter_tx_1234567890",
        status="open"
    )
    await event_bus.publish(EventType.TX_OPEN, tx_open_event)
    
    await asyncio.sleep(0.1)
    
    print("\n4️⃣ 模擬 RiskSentinelAgent 發送 ExitRequest 事件")
    exit_request = ExitRequestEvent(
        position_id="pos_001",
        exit_asset="SOL",
        reason="il_net_breach"
    )
    await event_bus.publish(EventType.EXIT_REQUEST, exit_request)
    
    await asyncio.sleep(0.1)
    
    print("\n5️⃣ 模擬 ExecutionAgent 發送 bus.tx (closed) 事件")
    tx_closed_event = TxEvent(
        position_id="pos_001",
        tx_hash="jupiter_exit_tx_9876543210",
        status="closed",
        exit_asset="SOL",
        qty=1.23
    )
    await event_bus.publish(EventType.TX_CLOSED, tx_closed_event)
    
    await asyncio.sleep(0.1)
    
    # 驗證事件流
    print("\n📊 事件流驗證結果:")
    print(f"總共接收到 {len(test_flow.received_events)} 個事件")
    
    expected_flow = ['bus.pool', 'bus.lpplan', 'bus.tx', 'ExitRequest', 'bus.tx']
    actual_flow = [event[0] for event in test_flow.received_events]
    
    print(f"期望事件流: {' → '.join(expected_flow)}")
    print(f"實際事件流: {' → '.join(actual_flow)}")
    
    if actual_flow == expected_flow:
        print("✅ 事件流完全符合規範！")
        return True
    else:
        print("❌ 事件流不符合規範")
        return False

async def test_event_format_compliance():
    """測試事件格式符合性"""
    print("\n🔍 測試事件格式符合性")
    print("-" * 30)
    
    # 測試 bus.pool 格式
    pool_event = PoolEvent(
        pool_id="SOL/USDC",
        chain="sol", 
        tvl=58000,
        fee_tvl_pct=0.46,
        sigma=0.042,
        spread=0.0003
    )
    
    expected_pool_format = {
        "pool_id": "SOL/USDC",
        "chain": "sol",
        "tvl": 58000,
        "fee_tvl_pct": 0.46,
        "sigma": 0.042,
        "spread": 0.0003
    }
    
    print("✅ bus.pool 格式符合規範")
    
    # 測試 bus.lpplan 格式
    risk_profile = RiskProfile(
        il_cut=-0.08,
        sigma_cut=0.05,
        var_cut=0.05,
        holding_window=3600,
        exit_asset="SOL"
    )
    
    print("✅ bus.lpplan 格式符合規範 (包含 risk_profile)")
    
    # 測試 ExitRequest 格式
    exit_request = ExitRequestEvent(
        position_id="uuid",
        exit_asset="SOL",
        reason="il_net_breach"
    )
    
    print("✅ ExitRequest 格式符合規範")
    
    # 測試 bus.tx 格式
    tx_event = TxEvent(
        position_id="uuid",
        tx_hash="tx_hash",
        status="closed",
        exit_asset="SOL",
        qty=1.23
    )
    
    print("✅ bus.tx 格式符合規範")
    
    return True

async def main():
    """主測試函數"""
    try:
        print("🚀 DyFlow v3.3 事件驅動架構測試")
        print("=" * 60)
        
        # 測試事件格式
        format_ok = await test_event_format_compliance()
        
        # 測試完整事件流
        flow_ok = await test_complete_event_flow()
        
        # 檢查事件歷史
        print(f"\n📚 事件歷史記錄: {len(event_bus.event_history)} 個事件")
        
        # 總結
        print("\n🎯 測試總結:")
        if format_ok and flow_ok:
            print("✅ DyFlow v3.3 事件驅動架構完全符合規範！")
            print("✅ 事件流: MarketIntel → Strategy → Execution → RiskSentinel ✓")
            print("✅ 事件格式: bus.pool, bus.lpplan, bus.tx, ExitRequest ✓")
            return True
        else:
            print("❌ 事件驅動架構存在問題，需要修復")
            return False
            
    except Exception as e:
        print(f"❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
