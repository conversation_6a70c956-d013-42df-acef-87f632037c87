<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow React UI Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function LoadingSpinner() {
            return (
                <div className="flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <div className="loading-spinner w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                        <h1 className="text-2xl font-bold text-gray-800">DyFlow React UI</h1>
                        <p className="text-gray-600 mt-2">正在初始化現代化界面...</p>
                    </div>
                </div>
            );
        }

        function Dashboard() {
            const [pools, setPools] = useState([]);
            const [systemStatus, setSystemStatus] = useState({
                bsc_connected: true,
                solana_connected: true,
                total_pools: 12,
                total_tvl: 1500000
            });

            const mockPools = [
                {
                    id: 1,
                    pair: "USDT/BTCB",
                    network: "bsc",
                    tvl: 217267,
                    apr: 5113.5,
                    fees_24h: 30438.03,
                    risk_level: "medium",
                    url: "https://pancakeswap.finance/liquidity/pool/bsc/0x123"
                },
                {
                    id: 2,
                    pair: "SPX/SOL",
                    network: "solana",
                    tvl: 10191,
                    apr: 5.3,
                    fees_24h: 541.56,
                    risk_level: "low",
                    url: "https://meteora.ag/pools/456"
                }
            ];

            useEffect(() => {
                setTimeout(() => {
                    setPools(mockPools);
                }, 1000);
            }, []);

            const formatCurrency = (value) => {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2,
                }).format(value);
            };

            const formatPercentage = (value) => {
                return `${value.toFixed(2)}%`;
            };

            return (
                <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
                    <div className="max-w-7xl mx-auto space-y-6">
                        {/* Header */}
                        <div className="text-center mb-8">
                            <h1 className="text-4xl font-bold text-gray-900 mb-2">
                                DyFlow Dashboard
                            </h1>
                            <p className="text-gray-600">
                                24/7 自動化流動性挖礦策略系統
                            </p>
                        </div>

                        {/* System Status Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div className="bg-white rounded-lg border shadow-sm p-6">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm font-medium">BSC 連接</h3>
                                    <span className="text-xs">⚡</span>
                                </div>
                                <div className="mt-2">
                                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                                        systemStatus.bsc_connected 
                                            ? 'bg-green-500 text-white' 
                                            : 'bg-red-500 text-white'
                                    }`}>
                                        {systemStatus.bsc_connected ? "已連接" : "未連接"}
                                    </span>
                                </div>
                            </div>

                            <div className="bg-white rounded-lg border shadow-sm p-6">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm font-medium">Solana 連接</h3>
                                    <span className="text-xs">⚡</span>
                                </div>
                                <div className="mt-2">
                                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                                        systemStatus.solana_connected 
                                            ? 'bg-green-500 text-white' 
                                            : 'bg-red-500 text-white'
                                    }`}>
                                        {systemStatus.solana_connected ? "已連接" : "未連接"}
                                    </span>
                                </div>
                            </div>

                            <div className="bg-white rounded-lg border shadow-sm p-6">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm font-medium">總池子數</h3>
                                    <span className="text-xs">📈</span>
                                </div>
                                <div className="text-2xl font-bold mt-2">{systemStatus.total_pools}</div>
                            </div>

                            <div className="bg-white rounded-lg border shadow-sm p-6">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm font-medium">總 TVL</h3>
                                    <span className="text-xs">💰</span>
                                </div>
                                <div className="text-2xl font-bold mt-2">{formatCurrency(systemStatus.total_tvl)}</div>
                            </div>
                        </div>

                        {/* Pools Table */}
                        <div className="bg-white rounded-lg border shadow-sm">
                            <div className="p-6 border-b">
                                <h2 className="text-xl font-semibold">流動性池子</h2>
                                <p className="text-gray-600 mt-1">實時監控的高收益LP池子</p>
                            </div>
                            <div className="p-6">
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead>
                                            <tr className="border-b">
                                                <th className="text-left p-2">交易對</th>
                                                <th className="text-left p-2">網絡</th>
                                                <th className="text-left p-2">TVL</th>
                                                <th className="text-left p-2">APR</th>
                                                <th className="text-left p-2">24h 手續費</th>
                                                <th className="text-left p-2">風險等級</th>
                                                <th className="text-left p-2">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {pools.map((pool) => (
                                                <tr key={pool.id} className="border-b hover:bg-gray-50">
                                                    <td className="p-2 font-medium">{pool.pair}</td>
                                                    <td className="p-2">
                                                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                                                            pool.network === 'bsc' 
                                                                ? 'bg-yellow-500 text-white' 
                                                                : 'bg-blue-500 text-white'
                                                        }`}>
                                                            {pool.network.toUpperCase()}
                                                        </span>
                                                    </td>
                                                    <td className="p-2">{formatCurrency(pool.tvl)}</td>
                                                    <td className="p-2 text-green-600 font-semibold">
                                                        {formatPercentage(pool.apr)}
                                                    </td>
                                                    <td className="p-2">{formatCurrency(pool.fees_24h)}</td>
                                                    <td className="p-2">
                                                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                                                            pool.risk_level === 'low' ? 'bg-green-500 text-white' : 
                                                            pool.risk_level === 'medium' ? 'bg-yellow-500 text-white' : 'bg-red-500 text-white'
                                                        }`}>
                                                            {pool.risk_level === 'low' ? '低風險' : 
                                                             pool.risk_level === 'medium' ? '中風險' : '高風險'}
                                                        </span>
                                                    </td>
                                                    <td className="p-2">
                                                        <button 
                                                            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm bg-white hover:bg-gray-50"
                                                            onClick={() => window.open(pool.url, '_blank')}
                                                        >
                                                            🔗 查看
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        function App() {
            const [isLoading, setIsLoading] = useState(true);

            useEffect(() => {
                const timer = setTimeout(() => {
                    setIsLoading(false);
                }, 2000);

                return () => clearTimeout(timer);
            }, []);

            if (isLoading) {
                return <LoadingSpinner />;
            }

            return <Dashboard />;
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
