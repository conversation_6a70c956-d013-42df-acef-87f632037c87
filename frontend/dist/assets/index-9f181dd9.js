function Ig(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function yf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var vf={exports:{}},Yi={},wf={exports:{}},j={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var So=Symbol.for("react.element"),Fg=Symbol.for("react.portal"),zg=Symbol.for("react.fragment"),jg=Symbol.for("react.strict_mode"),Bg=Symbol.for("react.profiler"),Ug=Symbol.for("react.provider"),$g=Symbol.for("react.context"),Wg=Symbol.for("react.forward_ref"),Hg=Symbol.for("react.suspense"),Kg=Symbol.for("react.memo"),Gg=Symbol.for("react.lazy"),Gu=Symbol.iterator;function Qg(e){return e===null||typeof e!="object"?null:(e=Gu&&e[Gu]||e["@@iterator"],typeof e=="function"?e:null)}var xf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Sf=Object.assign,Cf={};function yr(e,t,n){this.props=e,this.context=t,this.refs=Cf,this.updater=n||xf}yr.prototype.isReactComponent={};yr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};yr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Pf(){}Pf.prototype=yr.prototype;function Pa(e,t,n){this.props=e,this.context=t,this.refs=Cf,this.updater=n||xf}var ka=Pa.prototype=new Pf;ka.constructor=Pa;Sf(ka,yr.prototype);ka.isPureReactComponent=!0;var Qu=Array.isArray,kf=Object.prototype.hasOwnProperty,Ta={current:null},Tf={key:!0,ref:!0,__self:!0,__source:!0};function Ef(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)kf.call(t,r)&&!Tf.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:So,type:e,key:i,ref:s,props:o,_owner:Ta.current}}function Yg(e,t){return{$$typeof:So,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ea(e){return typeof e=="object"&&e!==null&&e.$$typeof===So}function Xg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Yu=/\/+/g;function Cs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xg(""+e.key):t.toString(36)}function ni(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case So:case Fg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Cs(s,0):r,Qu(o)?(n="",e!=null&&(n=e.replace(Yu,"$&/")+"/"),ni(o,t,n,"",function(u){return u})):o!=null&&(Ea(o)&&(o=Yg(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Yu,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Qu(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+Cs(i,l);s+=ni(i,t,n,a,o)}else if(a=Qg(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+Cs(i,l++),s+=ni(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function _o(e,t,n){if(e==null)return e;var r=[],o=0;return ni(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Zg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee={current:null},ri={transition:null},qg={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:ri,ReactCurrentOwner:Ta};function Nf(){throw Error("act(...) is not supported in production builds of React.")}j.Children={map:_o,forEach:function(e,t,n){_o(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return _o(e,function(){t++}),t},toArray:function(e){return _o(e,function(t){return t})||[]},only:function(e){if(!Ea(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};j.Component=yr;j.Fragment=zg;j.Profiler=Bg;j.PureComponent=Pa;j.StrictMode=jg;j.Suspense=Hg;j.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=qg;j.act=Nf;j.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Sf({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Ta.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)kf.call(t,a)&&!Tf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:So,type:e.type,key:o,ref:i,props:r,_owner:s}};j.createContext=function(e){return e={$$typeof:$g,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ug,_context:e},e.Consumer=e};j.createElement=Ef;j.createFactory=function(e){var t=Ef.bind(null,e);return t.type=e,t};j.createRef=function(){return{current:null}};j.forwardRef=function(e){return{$$typeof:Wg,render:e}};j.isValidElement=Ea;j.lazy=function(e){return{$$typeof:Gg,_payload:{_status:-1,_result:e},_init:Zg}};j.memo=function(e,t){return{$$typeof:Kg,type:e,compare:t===void 0?null:t}};j.startTransition=function(e){var t=ri.transition;ri.transition={};try{e()}finally{ri.transition=t}};j.unstable_act=Nf;j.useCallback=function(e,t){return Ee.current.useCallback(e,t)};j.useContext=function(e){return Ee.current.useContext(e)};j.useDebugValue=function(){};j.useDeferredValue=function(e){return Ee.current.useDeferredValue(e)};j.useEffect=function(e,t){return Ee.current.useEffect(e,t)};j.useId=function(){return Ee.current.useId()};j.useImperativeHandle=function(e,t,n){return Ee.current.useImperativeHandle(e,t,n)};j.useInsertionEffect=function(e,t){return Ee.current.useInsertionEffect(e,t)};j.useLayoutEffect=function(e,t){return Ee.current.useLayoutEffect(e,t)};j.useMemo=function(e,t){return Ee.current.useMemo(e,t)};j.useReducer=function(e,t,n){return Ee.current.useReducer(e,t,n)};j.useRef=function(e){return Ee.current.useRef(e)};j.useState=function(e){return Ee.current.useState(e)};j.useSyncExternalStore=function(e,t,n){return Ee.current.useSyncExternalStore(e,t,n)};j.useTransition=function(){return Ee.current.useTransition()};j.version="18.3.1";wf.exports=j;var w=wf.exports;const tt=yf(w),Jg=Ig({__proto__:null,default:tt},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey=w,ty=Symbol.for("react.element"),ny=Symbol.for("react.fragment"),ry=Object.prototype.hasOwnProperty,oy=ey.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,iy={key:!0,ref:!0,__self:!0,__source:!0};function Rf(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)ry.call(t,r)&&!iy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ty,type:e,key:i,ref:s,props:o,_owner:oy.current}}Yi.Fragment=ny;Yi.jsx=Rf;Yi.jsxs=Rf;vf.exports=Yi;var Na=vf.exports;const Af=Na.Fragment,C=Na.jsx,O=Na.jsxs;var fl={},Mf={exports:{}},Ue={},Lf={exports:{}},_f={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,D){var F=R.length;R.push(D);e:for(;0<F;){var V=F-1>>>1,H=R[V];if(0<o(H,D))R[V]=D,R[F]=H,F=V;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var D=R[0],F=R.pop();if(F!==D){R[0]=F;e:for(var V=0,H=R.length,We=H>>>1;V<We;){var Ve=2*(V+1)-1,In=R[Ve],be=Ve+1,fn=R[be];if(0>o(In,F))be<H&&0>o(fn,In)?(R[V]=fn,R[be]=F,V=be):(R[V]=In,R[Ve]=F,V=Ve);else if(be<H&&0>o(fn,F))R[V]=fn,R[be]=F,V=be;else break e}}return D}function o(R,D){var F=R.sortIndex-D.sortIndex;return F!==0?F:R.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,d=null,f=3,m=!1,y=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(R){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=R)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function x(R){if(v=!1,h(R),!y)if(n(a)!==null)y=!0,B(P);else{var D=n(u);D!==null&&se(x,D.startTime-R)}}function P(R,D){y=!1,v&&(v=!1,g(T),T=-1),m=!0;var F=f;try{for(h(D),d=n(a);d!==null&&(!(d.expirationTime>D)||R&&!$());){var V=d.callback;if(typeof V=="function"){d.callback=null,f=d.priorityLevel;var H=V(d.expirationTime<=D);D=e.unstable_now(),typeof H=="function"?d.callback=H:d===n(a)&&r(a),h(D)}else r(a);d=n(a)}if(d!==null)var We=!0;else{var Ve=n(u);Ve!==null&&se(x,Ve.startTime-D),We=!1}return We}finally{d=null,f=F,m=!1}}var E=!1,k=null,T=-1,A=5,L=-1;function $(){return!(e.unstable_now()-L<A)}function b(){if(k!==null){var R=e.unstable_now();L=R;var D=!0;try{D=k(!0,R)}finally{D?J():(E=!1,k=null)}}else E=!1}var J;if(typeof p=="function")J=function(){p(b)};else if(typeof MessageChannel<"u"){var _=new MessageChannel,Z=_.port2;_.port1.onmessage=b,J=function(){Z.postMessage(null)}}else J=function(){S(b,0)};function B(R){k=R,E||(E=!0,J())}function se(R,D){T=S(function(){R(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,B(P))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(R){switch(f){case 1:case 2:case 3:var D=3;break;default:D=f}var F=f;f=D;try{return R()}finally{f=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,D){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var F=f;f=R;try{return D()}finally{f=F}},e.unstable_scheduleCallback=function(R,D,F){var V=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?V+F:V):F=V,R){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=F+H,R={id:c++,callback:D,priorityLevel:R,startTime:F,expirationTime:H,sortIndex:-1},F>V?(R.sortIndex=F,t(u,R),n(a)===null&&R===n(u)&&(v?(g(T),T=-1):v=!0,se(x,F-V))):(R.sortIndex=H,t(a,R),y||m||(y=!0,B(P))),R},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(R){var D=f;return function(){var F=f;f=D;try{return R.apply(this,arguments)}finally{f=F}}}})(_f);Lf.exports=_f;var sy=Lf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ly=w,je=sy;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Df=new Set,Jr={};function Dn(e,t){ar(e,t),ar(e+"Capture",t)}function ar(e,t){for(Jr[e]=t,e=0;e<t.length;e++)Df.add(t[e])}var Mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),pl=Object.prototype.hasOwnProperty,ay=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Xu={},Zu={};function uy(e){return pl.call(Zu,e)?!0:pl.call(Xu,e)?!1:ay.test(e)?Zu[e]=!0:(Xu[e]=!0,!1)}function cy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function dy(e,t,n,r){if(t===null||typeof t>"u"||cy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ne(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new Ne(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new Ne(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new Ne(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new Ne(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new Ne(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new Ne(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new Ne(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new Ne(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new Ne(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ra=/[\-:]([a-z])/g;function Aa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ra,Aa);ye[t]=new Ne(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ra,Aa);ye[t]=new Ne(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ra,Aa);ye[t]=new Ne(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new Ne(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new Ne("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new Ne(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ma(e,t,n,r){var o=ye.hasOwnProperty(t)?ye[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(dy(t,n,o,r)&&(n=null),r||o===null?uy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var bt=ly.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Do=Symbol.for("react.element"),zn=Symbol.for("react.portal"),jn=Symbol.for("react.fragment"),La=Symbol.for("react.strict_mode"),hl=Symbol.for("react.profiler"),Vf=Symbol.for("react.provider"),bf=Symbol.for("react.context"),_a=Symbol.for("react.forward_ref"),ml=Symbol.for("react.suspense"),gl=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),jt=Symbol.for("react.lazy"),Of=Symbol.for("react.offscreen"),qu=Symbol.iterator;function Sr(e){return e===null||typeof e!="object"?null:(e=qu&&e[qu]||e["@@iterator"],typeof e=="function"?e:null)}var re=Object.assign,Ps;function Dr(e){if(Ps===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ps=t&&t[1]||""}return`
`+Ps+e}var ks=!1;function Ts(e,t){if(!e||ks)return"";ks=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{ks=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Dr(e):""}function fy(e){switch(e.tag){case 5:return Dr(e.type);case 16:return Dr("Lazy");case 13:return Dr("Suspense");case 19:return Dr("SuspenseList");case 0:case 2:case 15:return e=Ts(e.type,!1),e;case 11:return e=Ts(e.type.render,!1),e;case 1:return e=Ts(e.type,!0),e;default:return""}}function yl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case jn:return"Fragment";case zn:return"Portal";case hl:return"Profiler";case La:return"StrictMode";case ml:return"Suspense";case gl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case bf:return(e.displayName||"Context")+".Consumer";case Vf:return(e._context.displayName||"Context")+".Provider";case _a:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Da:return t=e.displayName||null,t!==null?t:yl(e.type)||"Memo";case jt:t=e._payload,e=e._init;try{return yl(e(t))}catch{}}return null}function py(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return yl(t);case 8:return t===La?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function rn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function If(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function hy(e){var t=If(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vo(e){e._valueTracker||(e._valueTracker=hy(e))}function Ff(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=If(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function gi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function vl(e,t){var n=t.checked;return re({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ju(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=rn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function zf(e,t){t=t.checked,t!=null&&Ma(e,"checked",t,!1)}function wl(e,t){zf(e,t);var n=rn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?xl(e,t.type,n):t.hasOwnProperty("defaultValue")&&xl(e,t.type,rn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ec(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function xl(e,t,n){(t!=="number"||gi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Vr=Array.isArray;function tr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+rn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Sl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return re({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function tc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(Vr(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:rn(n)}}function jf(e,t){var n=rn(t.value),r=rn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function nc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Bf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Cl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Bf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var bo,Uf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(bo=bo||document.createElement("div"),bo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=bo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function eo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var jr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},my=["Webkit","ms","Moz","O"];Object.keys(jr).forEach(function(e){my.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),jr[t]=jr[e]})});function $f(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||jr.hasOwnProperty(e)&&jr[e]?(""+t).trim():t+"px"}function Wf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=$f(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var gy=re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Pl(e,t){if(t){if(gy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function kl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tl=null;function Va(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var El=null,nr=null,rr=null;function rc(e){if(e=ko(e)){if(typeof El!="function")throw Error(N(280));var t=e.stateNode;t&&(t=es(t),El(e.stateNode,e.type,t))}}function Hf(e){nr?rr?rr.push(e):rr=[e]:nr=e}function Kf(){if(nr){var e=nr,t=rr;if(rr=nr=null,rc(e),t)for(e=0;e<t.length;e++)rc(t[e])}}function Gf(e,t){return e(t)}function Qf(){}var Es=!1;function Yf(e,t,n){if(Es)return e(t,n);Es=!0;try{return Gf(e,t,n)}finally{Es=!1,(nr!==null||rr!==null)&&(Qf(),Kf())}}function to(e,t){var n=e.stateNode;if(n===null)return null;var r=es(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var Nl=!1;if(Mt)try{var Cr={};Object.defineProperty(Cr,"passive",{get:function(){Nl=!0}}),window.addEventListener("test",Cr,Cr),window.removeEventListener("test",Cr,Cr)}catch{Nl=!1}function yy(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Br=!1,yi=null,vi=!1,Rl=null,vy={onError:function(e){Br=!0,yi=e}};function wy(e,t,n,r,o,i,s,l,a){Br=!1,yi=null,yy.apply(vy,arguments)}function xy(e,t,n,r,o,i,s,l,a){if(wy.apply(this,arguments),Br){if(Br){var u=yi;Br=!1,yi=null}else throw Error(N(198));vi||(vi=!0,Rl=u)}}function Vn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function oc(e){if(Vn(e)!==e)throw Error(N(188))}function Sy(e){var t=e.alternate;if(!t){if(t=Vn(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return oc(o),e;if(i===r)return oc(o),t;i=i.sibling}throw Error(N(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function Zf(e){return e=Sy(e),e!==null?qf(e):null}function qf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=qf(e);if(t!==null)return t;e=e.sibling}return null}var Jf=je.unstable_scheduleCallback,ic=je.unstable_cancelCallback,Cy=je.unstable_shouldYield,Py=je.unstable_requestPaint,le=je.unstable_now,ky=je.unstable_getCurrentPriorityLevel,ba=je.unstable_ImmediatePriority,ep=je.unstable_UserBlockingPriority,wi=je.unstable_NormalPriority,Ty=je.unstable_LowPriority,tp=je.unstable_IdlePriority,Xi=null,mt=null;function Ey(e){if(mt&&typeof mt.onCommitFiberRoot=="function")try{mt.onCommitFiberRoot(Xi,e,void 0,(e.current.flags&128)===128)}catch{}}var it=Math.clz32?Math.clz32:Ay,Ny=Math.log,Ry=Math.LN2;function Ay(e){return e>>>=0,e===0?32:31-(Ny(e)/Ry|0)|0}var Oo=64,Io=4194304;function br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function xi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=br(l):(i&=s,i!==0&&(r=br(i)))}else s=n&~o,s!==0?r=br(s):i!==0&&(r=br(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-it(t),o=1<<n,r|=e[n],t&=~o;return r}function My(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ly(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-it(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=My(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function Al(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function np(){var e=Oo;return Oo<<=1,!(Oo&4194240)&&(Oo=64),e}function Ns(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Co(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-it(t),e[t]=n}function _y(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Oa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var W=0;function rp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var op,Ia,ip,sp,lp,Ml=!1,Fo=[],Gt=null,Qt=null,Yt=null,no=new Map,ro=new Map,$t=[],Dy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function sc(e,t){switch(e){case"focusin":case"focusout":Gt=null;break;case"dragenter":case"dragleave":Qt=null;break;case"mouseover":case"mouseout":Yt=null;break;case"pointerover":case"pointerout":no.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ro.delete(t.pointerId)}}function Pr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=ko(t),t!==null&&Ia(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Vy(e,t,n,r,o){switch(t){case"focusin":return Gt=Pr(Gt,e,t,n,r,o),!0;case"dragenter":return Qt=Pr(Qt,e,t,n,r,o),!0;case"mouseover":return Yt=Pr(Yt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return no.set(i,Pr(no.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,ro.set(i,Pr(ro.get(i)||null,e,t,n,r,o)),!0}return!1}function ap(e){var t=xn(e.target);if(t!==null){var n=Vn(t);if(n!==null){if(t=n.tag,t===13){if(t=Xf(n),t!==null){e.blockedOn=t,lp(e.priority,function(){ip(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function oi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ll(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Tl=r,n.target.dispatchEvent(r),Tl=null}else return t=ko(n),t!==null&&Ia(t),e.blockedOn=n,!1;t.shift()}return!0}function lc(e,t,n){oi(e)&&n.delete(t)}function by(){Ml=!1,Gt!==null&&oi(Gt)&&(Gt=null),Qt!==null&&oi(Qt)&&(Qt=null),Yt!==null&&oi(Yt)&&(Yt=null),no.forEach(lc),ro.forEach(lc)}function kr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ml||(Ml=!0,je.unstable_scheduleCallback(je.unstable_NormalPriority,by)))}function oo(e){function t(o){return kr(o,e)}if(0<Fo.length){kr(Fo[0],e);for(var n=1;n<Fo.length;n++){var r=Fo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Gt!==null&&kr(Gt,e),Qt!==null&&kr(Qt,e),Yt!==null&&kr(Yt,e),no.forEach(t),ro.forEach(t),n=0;n<$t.length;n++)r=$t[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<$t.length&&(n=$t[0],n.blockedOn===null);)ap(n),n.blockedOn===null&&$t.shift()}var or=bt.ReactCurrentBatchConfig,Si=!0;function Oy(e,t,n,r){var o=W,i=or.transition;or.transition=null;try{W=1,Fa(e,t,n,r)}finally{W=o,or.transition=i}}function Iy(e,t,n,r){var o=W,i=or.transition;or.transition=null;try{W=4,Fa(e,t,n,r)}finally{W=o,or.transition=i}}function Fa(e,t,n,r){if(Si){var o=Ll(e,t,n,r);if(o===null)Is(e,t,r,Ci,n),sc(e,r);else if(Vy(o,e,t,n,r))r.stopPropagation();else if(sc(e,r),t&4&&-1<Dy.indexOf(e)){for(;o!==null;){var i=ko(o);if(i!==null&&op(i),i=Ll(e,t,n,r),i===null&&Is(e,t,r,Ci,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Is(e,t,r,null,n)}}var Ci=null;function Ll(e,t,n,r){if(Ci=null,e=Va(r),e=xn(e),e!==null)if(t=Vn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ci=e,null}function up(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ky()){case ba:return 1;case ep:return 4;case wi:case Ty:return 16;case tp:return 536870912;default:return 16}default:return 16}}var Ht=null,za=null,ii=null;function cp(){if(ii)return ii;var e,t=za,n=t.length,r,o="value"in Ht?Ht.value:Ht.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ii=o.slice(e,1<r?1-r:void 0)}function si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function zo(){return!0}function ac(){return!1}function $e(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?zo:ac,this.isPropagationStopped=ac,this}return re(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=zo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=zo)},persist:function(){},isPersistent:zo}),t}var vr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ja=$e(vr),Po=re({},vr,{view:0,detail:0}),Fy=$e(Po),Rs,As,Tr,Zi=re({},Po,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ba,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Tr&&(Tr&&e.type==="mousemove"?(Rs=e.screenX-Tr.screenX,As=e.screenY-Tr.screenY):As=Rs=0,Tr=e),Rs)},movementY:function(e){return"movementY"in e?e.movementY:As}}),uc=$e(Zi),zy=re({},Zi,{dataTransfer:0}),jy=$e(zy),By=re({},Po,{relatedTarget:0}),Ms=$e(By),Uy=re({},vr,{animationName:0,elapsedTime:0,pseudoElement:0}),$y=$e(Uy),Wy=re({},vr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hy=$e(Wy),Ky=re({},vr,{data:0}),cc=$e(Ky),Gy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Qy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Yy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Yy[e])?!!t[e]:!1}function Ba(){return Xy}var Zy=re({},Po,{key:function(e){if(e.key){var t=Gy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Qy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ba,charCode:function(e){return e.type==="keypress"?si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qy=$e(Zy),Jy=re({},Zi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),dc=$e(Jy),ev=re({},Po,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ba}),tv=$e(ev),nv=re({},vr,{propertyName:0,elapsedTime:0,pseudoElement:0}),rv=$e(nv),ov=re({},Zi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),iv=$e(ov),sv=[9,13,27,32],Ua=Mt&&"CompositionEvent"in window,Ur=null;Mt&&"documentMode"in document&&(Ur=document.documentMode);var lv=Mt&&"TextEvent"in window&&!Ur,dp=Mt&&(!Ua||Ur&&8<Ur&&11>=Ur),fc=String.fromCharCode(32),pc=!1;function fp(e,t){switch(e){case"keyup":return sv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Bn=!1;function av(e,t){switch(e){case"compositionend":return pp(t);case"keypress":return t.which!==32?null:(pc=!0,fc);case"textInput":return e=t.data,e===fc&&pc?null:e;default:return null}}function uv(e,t){if(Bn)return e==="compositionend"||!Ua&&fp(e,t)?(e=cp(),ii=za=Ht=null,Bn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dp&&t.locale!=="ko"?null:t.data;default:return null}}var cv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!cv[e.type]:t==="textarea"}function hp(e,t,n,r){Hf(r),t=Pi(t,"onChange"),0<t.length&&(n=new ja("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $r=null,io=null;function dv(e){Tp(e,0)}function qi(e){var t=Wn(e);if(Ff(t))return e}function fv(e,t){if(e==="change")return t}var mp=!1;if(Mt){var Ls;if(Mt){var _s="oninput"in document;if(!_s){var mc=document.createElement("div");mc.setAttribute("oninput","return;"),_s=typeof mc.oninput=="function"}Ls=_s}else Ls=!1;mp=Ls&&(!document.documentMode||9<document.documentMode)}function gc(){$r&&($r.detachEvent("onpropertychange",gp),io=$r=null)}function gp(e){if(e.propertyName==="value"&&qi(io)){var t=[];hp(t,io,e,Va(e)),Yf(dv,t)}}function pv(e,t,n){e==="focusin"?(gc(),$r=t,io=n,$r.attachEvent("onpropertychange",gp)):e==="focusout"&&gc()}function hv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qi(io)}function mv(e,t){if(e==="click")return qi(t)}function gv(e,t){if(e==="input"||e==="change")return qi(t)}function yv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var lt=typeof Object.is=="function"?Object.is:yv;function so(e,t){if(lt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!pl.call(t,o)||!lt(e[o],t[o]))return!1}return!0}function yc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function vc(e,t){var n=yc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=yc(n)}}function yp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?yp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function vp(){for(var e=window,t=gi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=gi(e.document)}return t}function $a(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function vv(e){var t=vp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&yp(n.ownerDocument.documentElement,n)){if(r!==null&&$a(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=vc(n,i);var s=vc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var wv=Mt&&"documentMode"in document&&11>=document.documentMode,Un=null,_l=null,Wr=null,Dl=!1;function wc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Dl||Un==null||Un!==gi(r)||(r=Un,"selectionStart"in r&&$a(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Wr&&so(Wr,r)||(Wr=r,r=Pi(_l,"onSelect"),0<r.length&&(t=new ja("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Un)))}function jo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $n={animationend:jo("Animation","AnimationEnd"),animationiteration:jo("Animation","AnimationIteration"),animationstart:jo("Animation","AnimationStart"),transitionend:jo("Transition","TransitionEnd")},Ds={},wp={};Mt&&(wp=document.createElement("div").style,"AnimationEvent"in window||(delete $n.animationend.animation,delete $n.animationiteration.animation,delete $n.animationstart.animation),"TransitionEvent"in window||delete $n.transitionend.transition);function Ji(e){if(Ds[e])return Ds[e];if(!$n[e])return e;var t=$n[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wp)return Ds[e]=t[n];return e}var xp=Ji("animationend"),Sp=Ji("animationiteration"),Cp=Ji("animationstart"),Pp=Ji("transitionend"),kp=new Map,xc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function an(e,t){kp.set(e,t),Dn(t,[e])}for(var Vs=0;Vs<xc.length;Vs++){var bs=xc[Vs],xv=bs.toLowerCase(),Sv=bs[0].toUpperCase()+bs.slice(1);an(xv,"on"+Sv)}an(xp,"onAnimationEnd");an(Sp,"onAnimationIteration");an(Cp,"onAnimationStart");an("dblclick","onDoubleClick");an("focusin","onFocus");an("focusout","onBlur");an(Pp,"onTransitionEnd");ar("onMouseEnter",["mouseout","mouseover"]);ar("onMouseLeave",["mouseout","mouseover"]);ar("onPointerEnter",["pointerout","pointerover"]);ar("onPointerLeave",["pointerout","pointerover"]);Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function Sc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,xy(r,t,void 0,e),e.currentTarget=null}function Tp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Sc(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Sc(o,l,u),i=a}}}if(vi)throw e=Rl,vi=!1,Rl=null,e}function Y(e,t){var n=t[Fl];n===void 0&&(n=t[Fl]=new Set);var r=e+"__bubble";n.has(r)||(Ep(t,e,2,!1),n.add(r))}function Os(e,t,n){var r=0;t&&(r|=4),Ep(n,e,r,t)}var Bo="_reactListening"+Math.random().toString(36).slice(2);function lo(e){if(!e[Bo]){e[Bo]=!0,Df.forEach(function(n){n!=="selectionchange"&&(Cv.has(n)||Os(n,!1,e),Os(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Bo]||(t[Bo]=!0,Os("selectionchange",!1,t))}}function Ep(e,t,n,r){switch(up(t)){case 1:var o=Oy;break;case 4:o=Iy;break;default:o=Fa}n=o.bind(null,t,n,e),o=void 0,!Nl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Is(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=xn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Yf(function(){var u=i,c=Va(n),d=[];e:{var f=kp.get(e);if(f!==void 0){var m=ja,y=e;switch(e){case"keypress":if(si(n)===0)break e;case"keydown":case"keyup":m=qy;break;case"focusin":y="focus",m=Ms;break;case"focusout":y="blur",m=Ms;break;case"beforeblur":case"afterblur":m=Ms;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=uc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=jy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=tv;break;case xp:case Sp:case Cp:m=$y;break;case Pp:m=rv;break;case"scroll":m=Fy;break;case"wheel":m=iv;break;case"copy":case"cut":case"paste":m=Hy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=dc}var v=(t&4)!==0,S=!v&&e==="scroll",g=v?f!==null?f+"Capture":null:f;v=[];for(var p=u,h;p!==null;){h=p;var x=h.stateNode;if(h.tag===5&&x!==null&&(h=x,g!==null&&(x=to(p,g),x!=null&&v.push(ao(p,x,h)))),S)break;p=p.return}0<v.length&&(f=new m(f,y,null,n,c),d.push({event:f,listeners:v}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==Tl&&(y=n.relatedTarget||n.fromElement)&&(xn(y)||y[Lt]))break e;if((m||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?xn(y):null,y!==null&&(S=Vn(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(v=uc,x="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=dc,x="onPointerLeave",g="onPointerEnter",p="pointer"),S=m==null?f:Wn(m),h=y==null?f:Wn(y),f=new v(x,p+"leave",m,n,c),f.target=S,f.relatedTarget=h,x=null,xn(c)===u&&(v=new v(g,p+"enter",y,n,c),v.target=h,v.relatedTarget=S,x=v),S=x,m&&y)t:{for(v=m,g=y,p=0,h=v;h;h=Fn(h))p++;for(h=0,x=g;x;x=Fn(x))h++;for(;0<p-h;)v=Fn(v),p--;for(;0<h-p;)g=Fn(g),h--;for(;p--;){if(v===g||g!==null&&v===g.alternate)break t;v=Fn(v),g=Fn(g)}v=null}else v=null;m!==null&&Cc(d,f,m,v,!1),y!==null&&S!==null&&Cc(d,S,y,v,!0)}}e:{if(f=u?Wn(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var P=fv;else if(hc(f))if(mp)P=gv;else{P=hv;var E=pv}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(P=mv);if(P&&(P=P(e,u))){hp(d,P,n,c);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&xl(f,"number",f.value)}switch(E=u?Wn(u):window,e){case"focusin":(hc(E)||E.contentEditable==="true")&&(Un=E,_l=u,Wr=null);break;case"focusout":Wr=_l=Un=null;break;case"mousedown":Dl=!0;break;case"contextmenu":case"mouseup":case"dragend":Dl=!1,wc(d,n,c);break;case"selectionchange":if(wv)break;case"keydown":case"keyup":wc(d,n,c)}var k;if(Ua)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else Bn?fp(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(dp&&n.locale!=="ko"&&(Bn||T!=="onCompositionStart"?T==="onCompositionEnd"&&Bn&&(k=cp()):(Ht=c,za="value"in Ht?Ht.value:Ht.textContent,Bn=!0)),E=Pi(u,T),0<E.length&&(T=new cc(T,e,null,n,c),d.push({event:T,listeners:E}),k?T.data=k:(k=pp(n),k!==null&&(T.data=k)))),(k=lv?av(e,n):uv(e,n))&&(u=Pi(u,"onBeforeInput"),0<u.length&&(c=new cc("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=k))}Tp(d,t)})}function ao(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Pi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=to(e,n),i!=null&&r.unshift(ao(e,i,o)),i=to(e,t),i!=null&&r.push(ao(e,i,o))),e=e.return}return r}function Fn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Cc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=to(n,i),a!=null&&s.unshift(ao(n,a,l))):o||(a=to(n,i),a!=null&&s.push(ao(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Pv=/\r\n?/g,kv=/\u0000|\uFFFD/g;function Pc(e){return(typeof e=="string"?e:""+e).replace(Pv,`
`).replace(kv,"")}function Uo(e,t,n){if(t=Pc(t),Pc(e)!==t&&n)throw Error(N(425))}function ki(){}var Vl=null,bl=null;function Ol(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Il=typeof setTimeout=="function"?setTimeout:void 0,Tv=typeof clearTimeout=="function"?clearTimeout:void 0,kc=typeof Promise=="function"?Promise:void 0,Ev=typeof queueMicrotask=="function"?queueMicrotask:typeof kc<"u"?function(e){return kc.resolve(null).then(e).catch(Nv)}:Il;function Nv(e){setTimeout(function(){throw e})}function Fs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),oo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);oo(t)}function Xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Tc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var wr=Math.random().toString(36).slice(2),ht="__reactFiber$"+wr,uo="__reactProps$"+wr,Lt="__reactContainer$"+wr,Fl="__reactEvents$"+wr,Rv="__reactListeners$"+wr,Av="__reactHandles$"+wr;function xn(e){var t=e[ht];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Lt]||n[ht]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Tc(e);e!==null;){if(n=e[ht])return n;e=Tc(e)}return t}e=n,n=e.parentNode}return null}function ko(e){return e=e[ht]||e[Lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Wn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function es(e){return e[uo]||null}var zl=[],Hn=-1;function un(e){return{current:e}}function X(e){0>Hn||(e.current=zl[Hn],zl[Hn]=null,Hn--)}function K(e,t){Hn++,zl[Hn]=e.current,e.current=t}var on={},Ce=un(on),Me=un(!1),En=on;function ur(e,t){var n=e.type.contextTypes;if(!n)return on;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Le(e){return e=e.childContextTypes,e!=null}function Ti(){X(Me),X(Ce)}function Ec(e,t,n){if(Ce.current!==on)throw Error(N(168));K(Ce,t),K(Me,n)}function Np(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(N(108,py(e)||"Unknown",o));return re({},n,r)}function Ei(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||on,En=Ce.current,K(Ce,e),K(Me,Me.current),!0}function Nc(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=Np(e,t,En),r.__reactInternalMemoizedMergedChildContext=e,X(Me),X(Ce),K(Ce,e)):X(Me),K(Me,n)}var Pt=null,ts=!1,zs=!1;function Rp(e){Pt===null?Pt=[e]:Pt.push(e)}function Mv(e){ts=!0,Rp(e)}function cn(){if(!zs&&Pt!==null){zs=!0;var e=0,t=W;try{var n=Pt;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Pt=null,ts=!1}catch(o){throw Pt!==null&&(Pt=Pt.slice(e+1)),Jf(ba,cn),o}finally{W=t,zs=!1}}return null}var Kn=[],Gn=0,Ni=null,Ri=0,Ge=[],Qe=0,Nn=null,kt=1,Tt="";function mn(e,t){Kn[Gn++]=Ri,Kn[Gn++]=Ni,Ni=e,Ri=t}function Ap(e,t,n){Ge[Qe++]=kt,Ge[Qe++]=Tt,Ge[Qe++]=Nn,Nn=e;var r=kt;e=Tt;var o=32-it(r)-1;r&=~(1<<o),n+=1;var i=32-it(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,kt=1<<32-it(t)+o|n<<o|r,Tt=i+e}else kt=1<<i|n<<o|r,Tt=e}function Wa(e){e.return!==null&&(mn(e,1),Ap(e,1,0))}function Ha(e){for(;e===Ni;)Ni=Kn[--Gn],Kn[Gn]=null,Ri=Kn[--Gn],Kn[Gn]=null;for(;e===Nn;)Nn=Ge[--Qe],Ge[Qe]=null,Tt=Ge[--Qe],Ge[Qe]=null,kt=Ge[--Qe],Ge[Qe]=null}var ze=null,Fe=null,q=!1,rt=null;function Mp(e,t){var n=Ye(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Rc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ze=e,Fe=Xt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ze=e,Fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Nn!==null?{id:kt,overflow:Tt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ye(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ze=e,Fe=null,!0):!1;default:return!1}}function jl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bl(e){if(q){var t=Fe;if(t){var n=t;if(!Rc(e,t)){if(jl(e))throw Error(N(418));t=Xt(n.nextSibling);var r=ze;t&&Rc(e,t)?Mp(r,n):(e.flags=e.flags&-4097|2,q=!1,ze=e)}}else{if(jl(e))throw Error(N(418));e.flags=e.flags&-4097|2,q=!1,ze=e}}}function Ac(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ze=e}function $o(e){if(e!==ze)return!1;if(!q)return Ac(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ol(e.type,e.memoizedProps)),t&&(t=Fe)){if(jl(e))throw Lp(),Error(N(418));for(;t;)Mp(e,t),t=Xt(t.nextSibling)}if(Ac(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Fe=Xt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Fe=null}}else Fe=ze?Xt(e.stateNode.nextSibling):null;return!0}function Lp(){for(var e=Fe;e;)e=Xt(e.nextSibling)}function cr(){Fe=ze=null,q=!1}function Ka(e){rt===null?rt=[e]:rt.push(e)}var Lv=bt.ReactCurrentBatchConfig;function Er(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function Wo(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Mc(e){var t=e._init;return t(e._payload)}function _p(e){function t(g,p){if(e){var h=g.deletions;h===null?(g.deletions=[p],g.flags|=16):h.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function o(g,p){return g=en(g,p),g.index=0,g.sibling=null,g}function i(g,p,h){return g.index=h,e?(h=g.alternate,h!==null?(h=h.index,h<p?(g.flags|=2,p):h):(g.flags|=2,p)):(g.flags|=1048576,p)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,p,h,x){return p===null||p.tag!==6?(p=Ks(h,g.mode,x),p.return=g,p):(p=o(p,h),p.return=g,p)}function a(g,p,h,x){var P=h.type;return P===jn?c(g,p,h.props.children,x,h.key):p!==null&&(p.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===jt&&Mc(P)===p.type)?(x=o(p,h.props),x.ref=Er(g,p,h),x.return=g,x):(x=pi(h.type,h.key,h.props,null,g.mode,x),x.ref=Er(g,p,h),x.return=g,x)}function u(g,p,h,x){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=Gs(h,g.mode,x),p.return=g,p):(p=o(p,h.children||[]),p.return=g,p)}function c(g,p,h,x,P){return p===null||p.tag!==7?(p=Tn(h,g.mode,x,P),p.return=g,p):(p=o(p,h),p.return=g,p)}function d(g,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Ks(""+p,g.mode,h),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Do:return h=pi(p.type,p.key,p.props,null,g.mode,h),h.ref=Er(g,null,p),h.return=g,h;case zn:return p=Gs(p,g.mode,h),p.return=g,p;case jt:var x=p._init;return d(g,x(p._payload),h)}if(Vr(p)||Sr(p))return p=Tn(p,g.mode,h,null),p.return=g,p;Wo(g,p)}return null}function f(g,p,h,x){var P=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return P!==null?null:l(g,p,""+h,x);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Do:return h.key===P?a(g,p,h,x):null;case zn:return h.key===P?u(g,p,h,x):null;case jt:return P=h._init,f(g,p,P(h._payload),x)}if(Vr(h)||Sr(h))return P!==null?null:c(g,p,h,x,null);Wo(g,h)}return null}function m(g,p,h,x,P){if(typeof x=="string"&&x!==""||typeof x=="number")return g=g.get(h)||null,l(p,g,""+x,P);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Do:return g=g.get(x.key===null?h:x.key)||null,a(p,g,x,P);case zn:return g=g.get(x.key===null?h:x.key)||null,u(p,g,x,P);case jt:var E=x._init;return m(g,p,h,E(x._payload),P)}if(Vr(x)||Sr(x))return g=g.get(h)||null,c(p,g,x,P,null);Wo(p,x)}return null}function y(g,p,h,x){for(var P=null,E=null,k=p,T=p=0,A=null;k!==null&&T<h.length;T++){k.index>T?(A=k,k=null):A=k.sibling;var L=f(g,k,h[T],x);if(L===null){k===null&&(k=A);break}e&&k&&L.alternate===null&&t(g,k),p=i(L,p,T),E===null?P=L:E.sibling=L,E=L,k=A}if(T===h.length)return n(g,k),q&&mn(g,T),P;if(k===null){for(;T<h.length;T++)k=d(g,h[T],x),k!==null&&(p=i(k,p,T),E===null?P=k:E.sibling=k,E=k);return q&&mn(g,T),P}for(k=r(g,k);T<h.length;T++)A=m(k,g,T,h[T],x),A!==null&&(e&&A.alternate!==null&&k.delete(A.key===null?T:A.key),p=i(A,p,T),E===null?P=A:E.sibling=A,E=A);return e&&k.forEach(function($){return t(g,$)}),q&&mn(g,T),P}function v(g,p,h,x){var P=Sr(h);if(typeof P!="function")throw Error(N(150));if(h=P.call(h),h==null)throw Error(N(151));for(var E=P=null,k=p,T=p=0,A=null,L=h.next();k!==null&&!L.done;T++,L=h.next()){k.index>T?(A=k,k=null):A=k.sibling;var $=f(g,k,L.value,x);if($===null){k===null&&(k=A);break}e&&k&&$.alternate===null&&t(g,k),p=i($,p,T),E===null?P=$:E.sibling=$,E=$,k=A}if(L.done)return n(g,k),q&&mn(g,T),P;if(k===null){for(;!L.done;T++,L=h.next())L=d(g,L.value,x),L!==null&&(p=i(L,p,T),E===null?P=L:E.sibling=L,E=L);return q&&mn(g,T),P}for(k=r(g,k);!L.done;T++,L=h.next())L=m(k,g,T,L.value,x),L!==null&&(e&&L.alternate!==null&&k.delete(L.key===null?T:L.key),p=i(L,p,T),E===null?P=L:E.sibling=L,E=L);return e&&k.forEach(function(b){return t(g,b)}),q&&mn(g,T),P}function S(g,p,h,x){if(typeof h=="object"&&h!==null&&h.type===jn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Do:e:{for(var P=h.key,E=p;E!==null;){if(E.key===P){if(P=h.type,P===jn){if(E.tag===7){n(g,E.sibling),p=o(E,h.props.children),p.return=g,g=p;break e}}else if(E.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===jt&&Mc(P)===E.type){n(g,E.sibling),p=o(E,h.props),p.ref=Er(g,E,h),p.return=g,g=p;break e}n(g,E);break}else t(g,E);E=E.sibling}h.type===jn?(p=Tn(h.props.children,g.mode,x,h.key),p.return=g,g=p):(x=pi(h.type,h.key,h.props,null,g.mode,x),x.ref=Er(g,p,h),x.return=g,g=x)}return s(g);case zn:e:{for(E=h.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(g,p.sibling),p=o(p,h.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=Gs(h,g.mode,x),p.return=g,g=p}return s(g);case jt:return E=h._init,S(g,p,E(h._payload),x)}if(Vr(h))return y(g,p,h,x);if(Sr(h))return v(g,p,h,x);Wo(g,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(g,p.sibling),p=o(p,h),p.return=g,g=p):(n(g,p),p=Ks(h,g.mode,x),p.return=g,g=p),s(g)):n(g,p)}return S}var dr=_p(!0),Dp=_p(!1),Ai=un(null),Mi=null,Qn=null,Ga=null;function Qa(){Ga=Qn=Mi=null}function Ya(e){var t=Ai.current;X(Ai),e._currentValue=t}function Ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ir(e,t){Mi=e,Ga=Qn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ae=!0),e.firstContext=null)}function Ze(e){var t=e._currentValue;if(Ga!==e)if(e={context:e,memoizedValue:t,next:null},Qn===null){if(Mi===null)throw Error(N(308));Qn=e,Mi.dependencies={lanes:0,firstContext:e}}else Qn=Qn.next=e;return t}var Sn=null;function Xa(e){Sn===null?Sn=[e]:Sn.push(e)}function Vp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Xa(t)):(n.next=o.next,o.next=n),t.interleaved=n,_t(e,r)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Bt=!1;function Za(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function bp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Nt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Zt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,_t(e,n)}return o=r.interleaved,o===null?(t.next=t,Xa(r)):(t.next=o.next,o.next=t),r.interleaved=t,_t(e,n)}function li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Oa(e,n)}}function Lc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Li(e,t,n,r){var o=e.updateQueue;Bt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(i!==null){var d=o.baseState;s=0,c=u=a=null,l=i;do{var f=l.lane,m=l.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,v=l;switch(f=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){d=y.call(m,d,f);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,f=typeof y=="function"?y.call(m,d,f):y,f==null)break e;d=re({},d,f);break e;case 2:Bt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[l]:f.push(l))}else m={eventTime:m,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=m,a=d):c=c.next=m,s|=f;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;f=l,l=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(1);if(c===null&&(a=d),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);An|=s,e.lanes=s,e.memoizedState=d}}function _c(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(N(191,o));o.call(r)}}}var To={},gt=un(To),co=un(To),fo=un(To);function Cn(e){if(e===To)throw Error(N(174));return e}function qa(e,t){switch(K(fo,t),K(co,e),K(gt,To),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Cl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Cl(t,e)}X(gt),K(gt,t)}function fr(){X(gt),X(co),X(fo)}function Op(e){Cn(fo.current);var t=Cn(gt.current),n=Cl(t,e.type);t!==n&&(K(co,e),K(gt,n))}function Ja(e){co.current===e&&(X(gt),X(co))}var ee=un(0);function _i(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var js=[];function eu(){for(var e=0;e<js.length;e++)js[e]._workInProgressVersionPrimary=null;js.length=0}var ai=bt.ReactCurrentDispatcher,Bs=bt.ReactCurrentBatchConfig,Rn=0,ne=null,ce=null,fe=null,Di=!1,Hr=!1,po=0,_v=0;function ve(){throw Error(N(321))}function tu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lt(e[n],t[n]))return!1;return!0}function nu(e,t,n,r,o,i){if(Rn=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=e===null||e.memoizedState===null?Ov:Iv,e=n(r,o),Hr){i=0;do{if(Hr=!1,po=0,25<=i)throw Error(N(301));i+=1,fe=ce=null,t.updateQueue=null,ai.current=Fv,e=n(r,o)}while(Hr)}if(ai.current=Vi,t=ce!==null&&ce.next!==null,Rn=0,fe=ce=ne=null,Di=!1,t)throw Error(N(300));return e}function ru(){var e=po!==0;return po=0,e}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?ne.memoizedState=fe=e:fe=fe.next=e,fe}function qe(){if(ce===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=ce.next;var t=fe===null?ne.memoizedState:fe.next;if(t!==null)fe=t,ce=e;else{if(e===null)throw Error(N(310));ce=e,e={memoizedState:ce.memoizedState,baseState:ce.baseState,baseQueue:ce.baseQueue,queue:ce.queue,next:null},fe===null?ne.memoizedState=fe=e:fe=fe.next=e}return fe}function ho(e,t){return typeof t=="function"?t(e):t}function Us(e){var t=qe(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=ce,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var c=u.lane;if((Rn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,ne.lanes|=c,An|=c}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,lt(r,t.memoizedState)||(Ae=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ne.lanes|=i,An|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function $s(e){var t=qe(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);lt(i,t.memoizedState)||(Ae=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Ip(){}function Fp(e,t){var n=ne,r=qe(),o=t(),i=!lt(r.memoizedState,o);if(i&&(r.memoizedState=o,Ae=!0),r=r.queue,ou(Bp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||fe!==null&&fe.memoizedState.tag&1){if(n.flags|=2048,mo(9,jp.bind(null,n,r,o,t),void 0,null),pe===null)throw Error(N(349));Rn&30||zp(n,t,o)}return o}function zp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function jp(e,t,n,r){t.value=n,t.getSnapshot=r,Up(t)&&$p(e)}function Bp(e,t,n){return n(function(){Up(t)&&$p(e)})}function Up(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lt(e,n)}catch{return!0}}function $p(e){var t=_t(e,1);t!==null&&st(t,e,1,-1)}function Dc(e){var t=pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ho,lastRenderedState:e},t.queue=e,e=e.dispatch=bv.bind(null,ne,e),[t.memoizedState,e]}function mo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wp(){return qe().memoizedState}function ui(e,t,n,r){var o=pt();ne.flags|=e,o.memoizedState=mo(1|t,n,void 0,r===void 0?null:r)}function ns(e,t,n,r){var o=qe();r=r===void 0?null:r;var i=void 0;if(ce!==null){var s=ce.memoizedState;if(i=s.destroy,r!==null&&tu(r,s.deps)){o.memoizedState=mo(t,n,i,r);return}}ne.flags|=e,o.memoizedState=mo(1|t,n,i,r)}function Vc(e,t){return ui(8390656,8,e,t)}function ou(e,t){return ns(2048,8,e,t)}function Hp(e,t){return ns(4,2,e,t)}function Kp(e,t){return ns(4,4,e,t)}function Gp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qp(e,t,n){return n=n!=null?n.concat([e]):null,ns(4,4,Gp.bind(null,t,e),n)}function iu(){}function Yp(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&tu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xp(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&tu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zp(e,t,n){return Rn&21?(lt(n,t)||(n=np(),ne.lanes|=n,An|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ae=!0),e.memoizedState=n)}function Dv(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=Bs.transition;Bs.transition={};try{e(!1),t()}finally{W=n,Bs.transition=r}}function qp(){return qe().memoizedState}function Vv(e,t,n){var r=Jt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jp(e))eh(t,n);else if(n=Vp(e,t,n,r),n!==null){var o=Te();st(n,e,r,o),th(n,t,r)}}function bv(e,t,n){var r=Jt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jp(e))eh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,lt(l,s)){var a=t.interleaved;a===null?(o.next=o,Xa(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Vp(e,t,o,r),n!==null&&(o=Te(),st(n,e,r,o),th(n,t,r))}}function Jp(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function eh(e,t){Hr=Di=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function th(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Oa(e,n)}}var Vi={readContext:Ze,useCallback:ve,useContext:ve,useEffect:ve,useImperativeHandle:ve,useInsertionEffect:ve,useLayoutEffect:ve,useMemo:ve,useReducer:ve,useRef:ve,useState:ve,useDebugValue:ve,useDeferredValue:ve,useTransition:ve,useMutableSource:ve,useSyncExternalStore:ve,useId:ve,unstable_isNewReconciler:!1},Ov={readContext:Ze,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:Ze,useEffect:Vc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ui(4194308,4,Gp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ui(4194308,4,e,t)},useInsertionEffect:function(e,t){return ui(4,2,e,t)},useMemo:function(e,t){var n=pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Vv.bind(null,ne,e),[r.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:Dc,useDebugValue:iu,useDeferredValue:function(e){return pt().memoizedState=e},useTransition:function(){var e=Dc(!1),t=e[0];return e=Dv.bind(null,e[1]),pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ne,o=pt();if(q){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),pe===null)throw Error(N(349));Rn&30||zp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Vc(Bp.bind(null,r,i,e),[e]),r.flags|=2048,mo(9,jp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=pt(),t=pe.identifierPrefix;if(q){var n=Tt,r=kt;n=(r&~(1<<32-it(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=po++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=_v++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Iv={readContext:Ze,useCallback:Yp,useContext:Ze,useEffect:ou,useImperativeHandle:Qp,useInsertionEffect:Hp,useLayoutEffect:Kp,useMemo:Xp,useReducer:Us,useRef:Wp,useState:function(){return Us(ho)},useDebugValue:iu,useDeferredValue:function(e){var t=qe();return Zp(t,ce.memoizedState,e)},useTransition:function(){var e=Us(ho)[0],t=qe().memoizedState;return[e,t]},useMutableSource:Ip,useSyncExternalStore:Fp,useId:qp,unstable_isNewReconciler:!1},Fv={readContext:Ze,useCallback:Yp,useContext:Ze,useEffect:ou,useImperativeHandle:Qp,useInsertionEffect:Hp,useLayoutEffect:Kp,useMemo:Xp,useReducer:$s,useRef:Wp,useState:function(){return $s(ho)},useDebugValue:iu,useDeferredValue:function(e){var t=qe();return ce===null?t.memoizedState=e:Zp(t,ce.memoizedState,e)},useTransition:function(){var e=$s(ho)[0],t=qe().memoizedState;return[e,t]},useMutableSource:Ip,useSyncExternalStore:Fp,useId:qp,unstable_isNewReconciler:!1};function et(e,t){if(e&&e.defaultProps){t=re({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function $l(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:re({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var rs={isMounted:function(e){return(e=e._reactInternals)?Vn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Te(),o=Jt(e),i=Nt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Zt(e,i,o),t!==null&&(st(t,e,o,r),li(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Te(),o=Jt(e),i=Nt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Zt(e,i,o),t!==null&&(st(t,e,o,r),li(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Te(),r=Jt(e),o=Nt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Zt(e,o,r),t!==null&&(st(t,e,r,n),li(t,e,r))}};function bc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!so(n,r)||!so(o,i):!0}function nh(e,t,n){var r=!1,o=on,i=t.contextType;return typeof i=="object"&&i!==null?i=Ze(i):(o=Le(t)?En:Ce.current,r=t.contextTypes,i=(r=r!=null)?ur(e,o):on),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=rs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Oc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&rs.enqueueReplaceState(t,t.state,null)}function Wl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Za(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Ze(i):(i=Le(t)?En:Ce.current,o.context=ur(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&($l(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&rs.enqueueReplaceState(o,o.state,null),Li(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function pr(e,t){try{var n="",r=t;do n+=fy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ws(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var zv=typeof WeakMap=="function"?WeakMap:Map;function rh(e,t,n){n=Nt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Oi||(Oi=!0,ta=r),Hl(e,t)},n}function oh(e,t,n){n=Nt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Hl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Hl(e,t),typeof r!="function"&&(qt===null?qt=new Set([this]):qt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Ic(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new zv;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Jv.bind(null,e,t,n),t.then(e,e))}function Fc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function zc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Nt(-1,1),t.tag=2,Zt(n,t,1))),n.lanes|=1),e)}var jv=bt.ReactCurrentOwner,Ae=!1;function ke(e,t,n,r){t.child=e===null?Dp(t,null,n,r):dr(t,e.child,n,r)}function jc(e,t,n,r,o){n=n.render;var i=t.ref;return ir(t,o),r=nu(e,t,n,r,i,o),n=ru(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Dt(e,t,o)):(q&&n&&Wa(t),t.flags|=1,ke(e,t,r,o),t.child)}function Bc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!pu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,ih(e,t,i,r,o)):(e=pi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:so,n(s,r)&&e.ref===t.ref)return Dt(e,t,o)}return t.flags|=1,e=en(i,r),e.ref=t.ref,e.return=t,t.child=e}function ih(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(so(i,r)&&e.ref===t.ref)if(Ae=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ae=!0);else return t.lanes=e.lanes,Dt(e,t,o)}return Kl(e,t,n,r,o)}function sh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},K(Xn,Oe),Oe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,K(Xn,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,K(Xn,Oe),Oe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,K(Xn,Oe),Oe|=r;return ke(e,t,o,n),t.child}function lh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Kl(e,t,n,r,o){var i=Le(n)?En:Ce.current;return i=ur(t,i),ir(t,o),n=nu(e,t,n,r,i,o),r=ru(),e!==null&&!Ae?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Dt(e,t,o)):(q&&r&&Wa(t),t.flags|=1,ke(e,t,n,o),t.child)}function Uc(e,t,n,r,o){if(Le(n)){var i=!0;Ei(t)}else i=!1;if(ir(t,o),t.stateNode===null)ci(e,t),nh(t,n,r),Wl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ze(u):(u=Le(n)?En:Ce.current,u=ur(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Oc(t,s,r,u),Bt=!1;var f=t.memoizedState;s.state=f,Li(t,r,s,o),a=t.memoizedState,l!==r||f!==a||Me.current||Bt?(typeof c=="function"&&($l(t,n,c,r),a=t.memoizedState),(l=Bt||bc(t,n,l,r,f,a,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,bp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:et(t.type,l),s.props=u,d=t.pendingProps,f=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ze(a):(a=Le(n)?En:Ce.current,a=ur(t,a));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||f!==a)&&Oc(t,s,r,a),Bt=!1,f=t.memoizedState,s.state=f,Li(t,r,s,o);var y=t.memoizedState;l!==d||f!==y||Me.current||Bt?(typeof m=="function"&&($l(t,n,m,r),y=t.memoizedState),(u=Bt||bc(t,n,u,r,f,y,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Gl(e,t,n,r,i,o)}function Gl(e,t,n,r,o,i){lh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Nc(t,n,!1),Dt(e,t,i);r=t.stateNode,jv.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=dr(t,e.child,null,i),t.child=dr(t,null,l,i)):ke(e,t,l,i),t.memoizedState=r.state,o&&Nc(t,n,!0),t.child}function ah(e){var t=e.stateNode;t.pendingContext?Ec(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ec(e,t.context,!1),qa(e,t.containerInfo)}function $c(e,t,n,r,o){return cr(),Ka(o),t.flags|=256,ke(e,t,n,r),t.child}var Ql={dehydrated:null,treeContext:null,retryLane:0};function Yl(e){return{baseLanes:e,cachePool:null,transitions:null}}function uh(e,t,n){var r=t.pendingProps,o=ee.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),K(ee,o&1),e===null)return Bl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=ss(s,r,0,null),e=Tn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Yl(n),t.memoizedState=Ql,e):su(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return Bv(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=en(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=en(l,i):(i=Tn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Yl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Ql,r}return i=e.child,e=i.sibling,r=en(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function su(e,t){return t=ss({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ho(e,t,n,r){return r!==null&&Ka(r),dr(t,e.child,null,n),e=su(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Bv(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ws(Error(N(422))),Ho(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ss({mode:"visible",children:r.children},o,0,null),i=Tn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&dr(t,e.child,null,s),t.child.memoizedState=Yl(s),t.memoizedState=Ql,i);if(!(t.mode&1))return Ho(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(N(419)),r=Ws(i,r,void 0),Ho(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ae||l){if(r=pe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,_t(e,o),st(r,e,o,-1))}return fu(),r=Ws(Error(N(421))),Ho(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=e0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Fe=Xt(o.nextSibling),ze=t,q=!0,rt=null,e!==null&&(Ge[Qe++]=kt,Ge[Qe++]=Tt,Ge[Qe++]=Nn,kt=e.id,Tt=e.overflow,Nn=t),t=su(t,r.children),t.flags|=4096,t)}function Wc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ul(e.return,t,n)}function Hs(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function ch(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ke(e,t,r.children,n),r=ee.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Wc(e,n,t);else if(e.tag===19)Wc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(K(ee,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&_i(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Hs(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&_i(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Hs(t,!0,n,null,i);break;case"together":Hs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ci(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Dt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),An|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=en(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=en(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Uv(e,t,n){switch(t.tag){case 3:ah(t),cr();break;case 5:Op(t);break;case 1:Le(t.type)&&Ei(t);break;case 4:qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;K(Ai,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(K(ee,ee.current&1),t.flags|=128,null):n&t.child.childLanes?uh(e,t,n):(K(ee,ee.current&1),e=Dt(e,t,n),e!==null?e.sibling:null);K(ee,ee.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ch(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),K(ee,ee.current),r)break;return null;case 22:case 23:return t.lanes=0,sh(e,t,n)}return Dt(e,t,n)}var dh,Xl,fh,ph;dh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xl=function(){};fh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Cn(gt.current);var i=null;switch(n){case"input":o=vl(e,o),r=vl(e,r),i=[];break;case"select":o=re({},o,{value:void 0}),r=re({},r,{value:void 0}),i=[];break;case"textarea":o=Sl(e,o),r=Sl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ki)}Pl(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Jr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Jr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&Y("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};ph=function(e,t,n,r){n!==r&&(t.flags|=4)};function Nr(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function $v(e,t,n){var r=t.pendingProps;switch(Ha(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return Le(t.type)&&Ti(),we(t),null;case 3:return r=t.stateNode,fr(),X(Me),X(Ce),eu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&($o(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,rt!==null&&(oa(rt),rt=null))),Xl(e,t),we(t),null;case 5:Ja(t);var o=Cn(fo.current);if(n=t.type,e!==null&&t.stateNode!=null)fh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return we(t),null}if(e=Cn(gt.current),$o(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ht]=t,r[uo]=i,e=(t.mode&1)!==0,n){case"dialog":Y("cancel",r),Y("close",r);break;case"iframe":case"object":case"embed":Y("load",r);break;case"video":case"audio":for(o=0;o<Or.length;o++)Y(Or[o],r);break;case"source":Y("error",r);break;case"img":case"image":case"link":Y("error",r),Y("load",r);break;case"details":Y("toggle",r);break;case"input":Ju(r,i),Y("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Y("invalid",r);break;case"textarea":tc(r,i),Y("invalid",r)}Pl(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Uo(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Uo(r.textContent,l,e),o=["children",""+l]):Jr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&Y("scroll",r)}switch(n){case"input":Vo(r),ec(r,i,!0);break;case"textarea":Vo(r),nc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ki)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Bf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[ht]=t,e[uo]=r,dh(e,t,!1,!1),t.stateNode=e;e:{switch(s=kl(n,r),n){case"dialog":Y("cancel",e),Y("close",e),o=r;break;case"iframe":case"object":case"embed":Y("load",e),o=r;break;case"video":case"audio":for(o=0;o<Or.length;o++)Y(Or[o],e);o=r;break;case"source":Y("error",e),o=r;break;case"img":case"image":case"link":Y("error",e),Y("load",e),o=r;break;case"details":Y("toggle",e),o=r;break;case"input":Ju(e,r),o=vl(e,r),Y("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=re({},r,{value:void 0}),Y("invalid",e);break;case"textarea":tc(e,r),o=Sl(e,r),Y("invalid",e);break;default:o=r}Pl(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Wf(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Uf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&eo(e,a):typeof a=="number"&&eo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Jr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&Y("scroll",e):a!=null&&Ma(e,i,a,s))}switch(n){case"input":Vo(e),ec(e,r,!1);break;case"textarea":Vo(e),nc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+rn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?tr(e,!!r.multiple,i,!1):r.defaultValue!=null&&tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return we(t),null;case 6:if(e&&t.stateNode!=null)ph(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=Cn(fo.current),Cn(gt.current),$o(t)){if(r=t.stateNode,n=t.memoizedProps,r[ht]=t,(i=r.nodeValue!==n)&&(e=ze,e!==null))switch(e.tag){case 3:Uo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Uo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ht]=t,t.stateNode=r}return we(t),null;case 13:if(X(ee),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Fe!==null&&t.mode&1&&!(t.flags&128))Lp(),cr(),t.flags|=98560,i=!1;else if(i=$o(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[ht]=t}else cr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;we(t),i=!1}else rt!==null&&(oa(rt),rt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ee.current&1?de===0&&(de=3):fu())),t.updateQueue!==null&&(t.flags|=4),we(t),null);case 4:return fr(),Xl(e,t),e===null&&lo(t.stateNode.containerInfo),we(t),null;case 10:return Ya(t.type._context),we(t),null;case 17:return Le(t.type)&&Ti(),we(t),null;case 19:if(X(ee),i=t.memoizedState,i===null)return we(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Nr(i,!1);else{if(de!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=_i(e),s!==null){for(t.flags|=128,Nr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return K(ee,ee.current&1|2),t.child}e=e.sibling}i.tail!==null&&le()>hr&&(t.flags|=128,r=!0,Nr(i,!1),t.lanes=4194304)}else{if(!r)if(e=_i(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Nr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!q)return we(t),null}else 2*le()-i.renderingStartTime>hr&&n!==1073741824&&(t.flags|=128,r=!0,Nr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=le(),t.sibling=null,n=ee.current,K(ee,r?n&1|2:n&1),t):(we(t),null);case 22:case 23:return du(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function Wv(e,t){switch(Ha(t),t.tag){case 1:return Le(t.type)&&Ti(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fr(),X(Me),X(Ce),eu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ja(t),null;case 13:if(X(ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));cr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(ee),null;case 4:return fr(),null;case 10:return Ya(t.type._context),null;case 22:case 23:return du(),null;case 24:return null;default:return null}}var Ko=!1,Se=!1,Hv=typeof WeakSet=="function"?WeakSet:Set,M=null;function Yn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(e,t,r)}else n.current=null}function Zl(e,t,n){try{n()}catch(r){oe(e,t,r)}}var Hc=!1;function Kv(e,t){if(Vl=Si,e=vp(),$a(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var m;d!==n||o!==0&&d.nodeType!==3||(l=s+o),d!==i||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)f=d,d=m;for(;;){if(d===e)break t;if(f===n&&++u===o&&(l=s),f===i&&++c===r&&(a=s),(m=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(bl={focusedElem:e,selectionRange:n},Si=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?v:et(t.type,v),S);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(x){oe(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return y=Hc,Hc=!1,y}function Kr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Zl(t,n,i)}o=o.next}while(o!==r)}}function os(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ql(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function hh(e){var t=e.alternate;t!==null&&(e.alternate=null,hh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ht],delete t[uo],delete t[Fl],delete t[Rv],delete t[Av])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mh(e){return e.tag===5||e.tag===3||e.tag===4}function Kc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Jl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ki));else if(r!==4&&(e=e.child,e!==null))for(Jl(e,t,n),e=e.sibling;e!==null;)Jl(e,t,n),e=e.sibling}function ea(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ea(e,t,n),e=e.sibling;e!==null;)ea(e,t,n),e=e.sibling}var me=null,nt=!1;function Ot(e,t,n){for(n=n.child;n!==null;)gh(e,t,n),n=n.sibling}function gh(e,t,n){if(mt&&typeof mt.onCommitFiberUnmount=="function")try{mt.onCommitFiberUnmount(Xi,n)}catch{}switch(n.tag){case 5:Se||Yn(n,t);case 6:var r=me,o=nt;me=null,Ot(e,t,n),me=r,nt=o,me!==null&&(nt?(e=me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):me.removeChild(n.stateNode));break;case 18:me!==null&&(nt?(e=me,n=n.stateNode,e.nodeType===8?Fs(e.parentNode,n):e.nodeType===1&&Fs(e,n),oo(e)):Fs(me,n.stateNode));break;case 4:r=me,o=nt,me=n.stateNode.containerInfo,nt=!0,Ot(e,t,n),me=r,nt=o;break;case 0:case 11:case 14:case 15:if(!Se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Zl(n,t,s),o=o.next}while(o!==r)}Ot(e,t,n);break;case 1:if(!Se&&(Yn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){oe(n,t,l)}Ot(e,t,n);break;case 21:Ot(e,t,n);break;case 22:n.mode&1?(Se=(r=Se)||n.memoizedState!==null,Ot(e,t,n),Se=r):Ot(e,t,n);break;default:Ot(e,t,n)}}function Gc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Hv),t.forEach(function(r){var o=t0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:me=l.stateNode,nt=!1;break e;case 3:me=l.stateNode.containerInfo,nt=!0;break e;case 4:me=l.stateNode.containerInfo,nt=!0;break e}l=l.return}if(me===null)throw Error(N(160));gh(i,s,o),me=null,nt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){oe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)yh(t,e),t=t.sibling}function yh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),at(e),r&4){try{Kr(3,e,e.return),os(3,e)}catch(v){oe(e,e.return,v)}try{Kr(5,e,e.return)}catch(v){oe(e,e.return,v)}}break;case 1:Je(t,e),at(e),r&512&&n!==null&&Yn(n,n.return);break;case 5:if(Je(t,e),at(e),r&512&&n!==null&&Yn(n,n.return),e.flags&32){var o=e.stateNode;try{eo(o,"")}catch(v){oe(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&zf(o,i),kl(l,s);var u=kl(l,i);for(s=0;s<a.length;s+=2){var c=a[s],d=a[s+1];c==="style"?Wf(o,d):c==="dangerouslySetInnerHTML"?Uf(o,d):c==="children"?eo(o,d):Ma(o,c,d,u)}switch(l){case"input":wl(o,i);break;case"textarea":jf(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?tr(o,!!i.multiple,m,!1):f!==!!i.multiple&&(i.defaultValue!=null?tr(o,!!i.multiple,i.defaultValue,!0):tr(o,!!i.multiple,i.multiple?[]:"",!1))}o[uo]=i}catch(v){oe(e,e.return,v)}}break;case 6:if(Je(t,e),at(e),r&4){if(e.stateNode===null)throw Error(N(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){oe(e,e.return,v)}}break;case 3:if(Je(t,e),at(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{oo(t.containerInfo)}catch(v){oe(e,e.return,v)}break;case 4:Je(t,e),at(e);break;case 13:Je(t,e),at(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(uu=le())),r&4&&Gc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Se=(u=Se)||c,Je(t,e),Se=u):Je(t,e),at(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(d=M=c;M!==null;){switch(f=M,m=f.child,f.tag){case 0:case 11:case 14:case 15:Kr(4,f,f.return);break;case 1:Yn(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){oe(r,n,v)}}break;case 5:Yn(f,f.return);break;case 22:if(f.memoizedState!==null){Yc(d);continue}}m!==null?(m.return=f,M=m):Yc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=$f("display",s))}catch(v){oe(e,e.return,v)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(v){oe(e,e.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Je(t,e),at(e),r&4&&Gc(e);break;case 21:break;default:Je(t,e),at(e)}}function at(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mh(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(eo(o,""),r.flags&=-33);var i=Kc(e);ea(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Kc(e);Jl(e,l,s);break;default:throw Error(N(161))}}catch(a){oe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Gv(e,t,n){M=e,vh(e)}function vh(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var o=M,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ko;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Se;l=Ko;var u=Se;if(Ko=s,(Se=a)&&!u)for(M=o;M!==null;)s=M,a=s.child,s.tag===22&&s.memoizedState!==null?Xc(o):a!==null?(a.return=s,M=a):Xc(o);for(;i!==null;)M=i,vh(i),i=i.sibling;M=o,Ko=l,Se=u}Qc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,M=i):Qc(e)}}function Qc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Se||os(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Se)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:et(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&_c(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}_c(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&oo(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}Se||t.flags&512&&ql(t)}catch(f){oe(t,t.return,f)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Yc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Xc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{os(4,t)}catch(a){oe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){oe(t,o,a)}}var i=t.return;try{ql(t)}catch(a){oe(t,i,a)}break;case 5:var s=t.return;try{ql(t)}catch(a){oe(t,s,a)}}}catch(a){oe(t,t.return,a)}if(t===e){M=null;break}var l=t.sibling;if(l!==null){l.return=t.return,M=l;break}M=t.return}}var Qv=Math.ceil,bi=bt.ReactCurrentDispatcher,lu=bt.ReactCurrentOwner,Xe=bt.ReactCurrentBatchConfig,U=0,pe=null,ue=null,ge=0,Oe=0,Xn=un(0),de=0,go=null,An=0,is=0,au=0,Gr=null,Re=null,uu=0,hr=1/0,Ct=null,Oi=!1,ta=null,qt=null,Go=!1,Kt=null,Ii=0,Qr=0,na=null,di=-1,fi=0;function Te(){return U&6?le():di!==-1?di:di=le()}function Jt(e){return e.mode&1?U&2&&ge!==0?ge&-ge:Lv.transition!==null?(fi===0&&(fi=np()),fi):(e=W,e!==0||(e=window.event,e=e===void 0?16:up(e.type)),e):1}function st(e,t,n,r){if(50<Qr)throw Qr=0,na=null,Error(N(185));Co(e,n,r),(!(U&2)||e!==pe)&&(e===pe&&(!(U&2)&&(is|=n),de===4&&Wt(e,ge)),_e(e,r),n===1&&U===0&&!(t.mode&1)&&(hr=le()+500,ts&&cn()))}function _e(e,t){var n=e.callbackNode;Ly(e,t);var r=xi(e,e===pe?ge:0);if(r===0)n!==null&&ic(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ic(n),t===1)e.tag===0?Mv(Zc.bind(null,e)):Rp(Zc.bind(null,e)),Ev(function(){!(U&6)&&cn()}),n=null;else{switch(rp(r)){case 1:n=ba;break;case 4:n=ep;break;case 16:n=wi;break;case 536870912:n=tp;break;default:n=wi}n=Eh(n,wh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wh(e,t){if(di=-1,fi=0,U&6)throw Error(N(327));var n=e.callbackNode;if(sr()&&e.callbackNode!==n)return null;var r=xi(e,e===pe?ge:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Fi(e,r);else{t=r;var o=U;U|=2;var i=Sh();(pe!==e||ge!==t)&&(Ct=null,hr=le()+500,kn(e,t));do try{Zv();break}catch(l){xh(e,l)}while(1);Qa(),bi.current=i,U=o,ue!==null?t=0:(pe=null,ge=0,t=de)}if(t!==0){if(t===2&&(o=Al(e),o!==0&&(r=o,t=ra(e,o))),t===1)throw n=go,kn(e,0),Wt(e,r),_e(e,le()),n;if(t===6)Wt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Yv(o)&&(t=Fi(e,r),t===2&&(i=Al(e),i!==0&&(r=i,t=ra(e,i))),t===1))throw n=go,kn(e,0),Wt(e,r),_e(e,le()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:gn(e,Re,Ct);break;case 3:if(Wt(e,r),(r&130023424)===r&&(t=uu+500-le(),10<t)){if(xi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Te(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Il(gn.bind(null,e,Re,Ct),t);break}gn(e,Re,Ct);break;case 4:if(Wt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-it(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Qv(r/1960))-r,10<r){e.timeoutHandle=Il(gn.bind(null,e,Re,Ct),r);break}gn(e,Re,Ct);break;case 5:gn(e,Re,Ct);break;default:throw Error(N(329))}}}return _e(e,le()),e.callbackNode===n?wh.bind(null,e):null}function ra(e,t){var n=Gr;return e.current.memoizedState.isDehydrated&&(kn(e,t).flags|=256),e=Fi(e,t),e!==2&&(t=Re,Re=n,t!==null&&oa(t)),e}function oa(e){Re===null?Re=e:Re.push.apply(Re,e)}function Yv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!lt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Wt(e,t){for(t&=~au,t&=~is,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function Zc(e){if(U&6)throw Error(N(327));sr();var t=xi(e,0);if(!(t&1))return _e(e,le()),null;var n=Fi(e,t);if(e.tag!==0&&n===2){var r=Al(e);r!==0&&(t=r,n=ra(e,r))}if(n===1)throw n=go,kn(e,0),Wt(e,t),_e(e,le()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,gn(e,Re,Ct),_e(e,le()),null}function cu(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(hr=le()+500,ts&&cn())}}function Mn(e){Kt!==null&&Kt.tag===0&&!(U&6)&&sr();var t=U;U|=1;var n=Xe.transition,r=W;try{if(Xe.transition=null,W=1,e)return e()}finally{W=r,Xe.transition=n,U=t,!(U&6)&&cn()}}function du(){Oe=Xn.current,X(Xn)}function kn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Tv(n)),ue!==null)for(n=ue.return;n!==null;){var r=n;switch(Ha(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ti();break;case 3:fr(),X(Me),X(Ce),eu();break;case 5:Ja(r);break;case 4:fr();break;case 13:X(ee);break;case 19:X(ee);break;case 10:Ya(r.type._context);break;case 22:case 23:du()}n=n.return}if(pe=e,ue=e=en(e.current,null),ge=Oe=t,de=0,go=null,au=is=An=0,Re=Gr=null,Sn!==null){for(t=0;t<Sn.length;t++)if(n=Sn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Sn=null}return e}function xh(e,t){do{var n=ue;try{if(Qa(),ai.current=Vi,Di){for(var r=ne.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Di=!1}if(Rn=0,fe=ce=ne=null,Hr=!1,po=0,lu.current=null,n===null||n.return===null){de=1,go=t,ue=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=ge,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=Fc(s);if(m!==null){m.flags&=-257,zc(m,s,l,i,t),m.mode&1&&Ic(i,u,t),t=m,a=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){Ic(i,u,t),fu();break e}a=Error(N(426))}}else if(q&&l.mode&1){var S=Fc(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),zc(S,s,l,i,t),Ka(pr(a,l));break e}}i=a=pr(a,l),de!==4&&(de=2),Gr===null?Gr=[i]:Gr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=rh(i,a,t);Lc(i,g);break e;case 1:l=a;var p=i.type,h=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(qt===null||!qt.has(h)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=oh(i,l,t);Lc(i,x);break e}}i=i.return}while(i!==null)}Ph(n)}catch(P){t=P,ue===n&&n!==null&&(ue=n=n.return);continue}break}while(1)}function Sh(){var e=bi.current;return bi.current=Vi,e===null?Vi:e}function fu(){(de===0||de===3||de===2)&&(de=4),pe===null||!(An&268435455)&&!(is&268435455)||Wt(pe,ge)}function Fi(e,t){var n=U;U|=2;var r=Sh();(pe!==e||ge!==t)&&(Ct=null,kn(e,t));do try{Xv();break}catch(o){xh(e,o)}while(1);if(Qa(),U=n,bi.current=r,ue!==null)throw Error(N(261));return pe=null,ge=0,de}function Xv(){for(;ue!==null;)Ch(ue)}function Zv(){for(;ue!==null&&!Cy();)Ch(ue)}function Ch(e){var t=Th(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?Ph(e):ue=t,lu.current=null}function Ph(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Wv(n,t),n!==null){n.flags&=32767,ue=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{de=6,ue=null;return}}else if(n=$v(n,t,Oe),n!==null){ue=n;return}if(t=t.sibling,t!==null){ue=t;return}ue=t=e}while(t!==null);de===0&&(de=5)}function gn(e,t,n){var r=W,o=Xe.transition;try{Xe.transition=null,W=1,qv(e,t,n,r)}finally{Xe.transition=o,W=r}return null}function qv(e,t,n,r){do sr();while(Kt!==null);if(U&6)throw Error(N(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(_y(e,i),e===pe&&(ue=pe=null,ge=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Go||(Go=!0,Eh(wi,function(){return sr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Xe.transition,Xe.transition=null;var s=W;W=1;var l=U;U|=4,lu.current=null,Kv(e,n),yh(n,e),vv(bl),Si=!!Vl,bl=Vl=null,e.current=n,Gv(n),Py(),U=l,W=s,Xe.transition=i}else e.current=n;if(Go&&(Go=!1,Kt=e,Ii=o),i=e.pendingLanes,i===0&&(qt=null),Ey(n.stateNode),_e(e,le()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Oi)throw Oi=!1,e=ta,ta=null,e;return Ii&1&&e.tag!==0&&sr(),i=e.pendingLanes,i&1?e===na?Qr++:(Qr=0,na=e):Qr=0,cn(),null}function sr(){if(Kt!==null){var e=rp(Ii),t=Xe.transition,n=W;try{if(Xe.transition=null,W=16>e?16:e,Kt===null)var r=!1;else{if(e=Kt,Kt=null,Ii=0,U&6)throw Error(N(331));var o=U;for(U|=4,M=e.current;M!==null;){var i=M,s=i.child;if(M.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:Kr(8,c,i)}var d=c.child;if(d!==null)d.return=c,M=d;else for(;M!==null;){c=M;var f=c.sibling,m=c.return;if(hh(c),c===u){M=null;break}if(f!==null){f.return=m,M=f;break}M=m}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}M=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,M=s;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Kr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,M=g;break e}M=i.return}}var p=e.current;for(M=p;M!==null;){s=M;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,M=h;else e:for(s=p;M!==null;){if(l=M,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:os(9,l)}}catch(P){oe(l,l.return,P)}if(l===s){M=null;break e}var x=l.sibling;if(x!==null){x.return=l.return,M=x;break e}M=l.return}}if(U=o,cn(),mt&&typeof mt.onPostCommitFiberRoot=="function")try{mt.onPostCommitFiberRoot(Xi,e)}catch{}r=!0}return r}finally{W=n,Xe.transition=t}}return!1}function qc(e,t,n){t=pr(n,t),t=rh(e,t,1),e=Zt(e,t,1),t=Te(),e!==null&&(Co(e,1,t),_e(e,t))}function oe(e,t,n){if(e.tag===3)qc(e,e,n);else for(;t!==null;){if(t.tag===3){qc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(qt===null||!qt.has(r))){e=pr(n,e),e=oh(t,e,1),t=Zt(t,e,1),e=Te(),t!==null&&(Co(t,1,e),_e(t,e));break}}t=t.return}}function Jv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Te(),e.pingedLanes|=e.suspendedLanes&n,pe===e&&(ge&n)===n&&(de===4||de===3&&(ge&130023424)===ge&&500>le()-uu?kn(e,0):au|=n),_e(e,t)}function kh(e,t){t===0&&(e.mode&1?(t=Io,Io<<=1,!(Io&130023424)&&(Io=4194304)):t=1);var n=Te();e=_t(e,t),e!==null&&(Co(e,t,n),_e(e,n))}function e0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),kh(e,n)}function t0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),kh(e,n)}var Th;Th=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Me.current)Ae=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ae=!1,Uv(e,t,n);Ae=!!(e.flags&131072)}else Ae=!1,q&&t.flags&1048576&&Ap(t,Ri,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ci(e,t),e=t.pendingProps;var o=ur(t,Ce.current);ir(t,n),o=nu(null,t,r,e,o,n);var i=ru();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Le(r)?(i=!0,Ei(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Za(t),o.updater=rs,t.stateNode=o,o._reactInternals=t,Wl(t,r,e,n),t=Gl(null,t,r,!0,i,n)):(t.tag=0,q&&i&&Wa(t),ke(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ci(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=r0(r),e=et(r,e),o){case 0:t=Kl(null,t,r,e,n);break e;case 1:t=Uc(null,t,r,e,n);break e;case 11:t=jc(null,t,r,e,n);break e;case 14:t=Bc(null,t,r,et(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),Kl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),Uc(e,t,r,o,n);case 3:e:{if(ah(t),e===null)throw Error(N(387));r=t.pendingProps,i=t.memoizedState,o=i.element,bp(e,t),Li(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=pr(Error(N(423)),t),t=$c(e,t,r,n,o);break e}else if(r!==o){o=pr(Error(N(424)),t),t=$c(e,t,r,n,o);break e}else for(Fe=Xt(t.stateNode.containerInfo.firstChild),ze=t,q=!0,rt=null,n=Dp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cr(),r===o){t=Dt(e,t,n);break e}ke(e,t,r,n)}t=t.child}return t;case 5:return Op(t),e===null&&Bl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ol(r,o)?s=null:i!==null&&Ol(r,i)&&(t.flags|=32),lh(e,t),ke(e,t,s,n),t.child;case 6:return e===null&&Bl(t),null;case 13:return uh(e,t,n);case 4:return qa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=dr(t,null,r,n):ke(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),jc(e,t,r,o,n);case 7:return ke(e,t,t.pendingProps,n),t.child;case 8:return ke(e,t,t.pendingProps.children,n),t.child;case 12:return ke(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,K(Ai,r._currentValue),r._currentValue=s,i!==null)if(lt(i.value,s)){if(i.children===o.children&&!Me.current){t=Dt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Nt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ul(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(N(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ul(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}ke(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ir(t,n),o=Ze(o),r=r(o),t.flags|=1,ke(e,t,r,n),t.child;case 14:return r=t.type,o=et(r,t.pendingProps),o=et(r.type,o),Bc(e,t,r,o,n);case 15:return ih(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:et(r,o),ci(e,t),t.tag=1,Le(r)?(e=!0,Ei(t)):e=!1,ir(t,n),nh(t,r,o),Wl(t,r,o,n),Gl(null,t,r,!0,e,n);case 19:return ch(e,t,n);case 22:return sh(e,t,n)}throw Error(N(156,t.tag))};function Eh(e,t){return Jf(e,t)}function n0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ye(e,t,n,r){return new n0(e,t,n,r)}function pu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function r0(e){if(typeof e=="function")return pu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===_a)return 11;if(e===Da)return 14}return 2}function en(e,t){var n=e.alternate;return n===null?(n=Ye(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function pi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")pu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case jn:return Tn(n.children,o,i,t);case La:s=8,o|=8;break;case hl:return e=Ye(12,n,t,o|2),e.elementType=hl,e.lanes=i,e;case ml:return e=Ye(13,n,t,o),e.elementType=ml,e.lanes=i,e;case gl:return e=Ye(19,n,t,o),e.elementType=gl,e.lanes=i,e;case Of:return ss(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Vf:s=10;break e;case bf:s=9;break e;case _a:s=11;break e;case Da:s=14;break e;case jt:s=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=Ye(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Tn(e,t,n,r){return e=Ye(7,e,r,t),e.lanes=n,e}function ss(e,t,n,r){return e=Ye(22,e,r,t),e.elementType=Of,e.lanes=n,e.stateNode={isHidden:!1},e}function Ks(e,t,n){return e=Ye(6,e,null,t),e.lanes=n,e}function Gs(e,t,n){return t=Ye(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function o0(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ns(0),this.expirationTimes=Ns(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ns(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function hu(e,t,n,r,o,i,s,l,a){return e=new o0(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ye(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Za(i),e}function i0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:zn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Nh(e){if(!e)return on;e=e._reactInternals;e:{if(Vn(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Le(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(Le(n))return Np(e,n,t)}return t}function Rh(e,t,n,r,o,i,s,l,a){return e=hu(n,r,!0,e,o,i,s,l,a),e.context=Nh(null),n=e.current,r=Te(),o=Jt(n),i=Nt(r,o),i.callback=t??null,Zt(n,i,o),e.current.lanes=o,Co(e,o,r),_e(e,r),e}function ls(e,t,n,r){var o=t.current,i=Te(),s=Jt(o);return n=Nh(n),t.context===null?t.context=n:t.pendingContext=n,t=Nt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Zt(o,t,s),e!==null&&(st(e,o,s,i),li(e,o,s)),s}function zi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Jc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function mu(e,t){Jc(e,t),(e=e.alternate)&&Jc(e,t)}function s0(){return null}var Ah=typeof reportError=="function"?reportError:function(e){console.error(e)};function gu(e){this._internalRoot=e}as.prototype.render=gu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));ls(e,t,null,null)};as.prototype.unmount=gu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Mn(function(){ls(null,e,null,null)}),t[Lt]=null}};function as(e){this._internalRoot=e}as.prototype.unstable_scheduleHydration=function(e){if(e){var t=sp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$t.length&&t!==0&&t<$t[n].priority;n++);$t.splice(n,0,e),n===0&&ap(e)}};function yu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ed(){}function l0(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=zi(s);i.call(u)}}var s=Rh(t,r,e,0,null,!1,!1,"",ed);return e._reactRootContainer=s,e[Lt]=s.current,lo(e.nodeType===8?e.parentNode:e),Mn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=zi(a);l.call(u)}}var a=hu(e,0,!1,null,null,!1,!1,"",ed);return e._reactRootContainer=a,e[Lt]=a.current,lo(e.nodeType===8?e.parentNode:e),Mn(function(){ls(t,a,n,r)}),a}function cs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=zi(s);l.call(a)}}ls(t,s,e,o)}else s=l0(n,t,e,o,r);return zi(s)}op=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=br(t.pendingLanes);n!==0&&(Oa(t,n|1),_e(t,le()),!(U&6)&&(hr=le()+500,cn()))}break;case 13:Mn(function(){var r=_t(e,1);if(r!==null){var o=Te();st(r,e,1,o)}}),mu(e,1)}};Ia=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=Te();st(t,e,134217728,n)}mu(e,134217728)}};ip=function(e){if(e.tag===13){var t=Jt(e),n=_t(e,t);if(n!==null){var r=Te();st(n,e,t,r)}mu(e,t)}};sp=function(){return W};lp=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};El=function(e,t,n){switch(t){case"input":if(wl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=es(r);if(!o)throw Error(N(90));Ff(r),wl(r,o)}}}break;case"textarea":jf(e,n);break;case"select":t=n.value,t!=null&&tr(e,!!n.multiple,t,!1)}};Gf=cu;Qf=Mn;var a0={usingClientEntryPoint:!1,Events:[ko,Wn,es,Hf,Kf,cu]},Rr={findFiberByHostInstance:xn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},u0={bundleType:Rr.bundleType,version:Rr.version,rendererPackageName:Rr.rendererPackageName,rendererConfig:Rr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Zf(e),e===null?null:e.stateNode},findFiberByHostInstance:Rr.findFiberByHostInstance||s0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Qo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Qo.isDisabled&&Qo.supportsFiber)try{Xi=Qo.inject(u0),mt=Qo}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=a0;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!yu(t))throw Error(N(200));return i0(e,t,null,n)};Ue.createRoot=function(e,t){if(!yu(e))throw Error(N(299));var n=!1,r="",o=Ah;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=hu(e,1,!1,null,null,n,!1,r,o),e[Lt]=t.current,lo(e.nodeType===8?e.parentNode:e),new gu(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=Zf(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return Mn(e)};Ue.hydrate=function(e,t,n){if(!us(t))throw Error(N(200));return cs(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!yu(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Ah;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Rh(t,null,e,1,n??null,o,!1,i,s),e[Lt]=t.current,lo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new as(t)};Ue.render=function(e,t,n){if(!us(t))throw Error(N(200));return cs(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!us(e))throw Error(N(40));return e._reactRootContainer?(Mn(function(){cs(null,null,e,!1,function(){e._reactRootContainer=null,e[Lt]=null})}),!0):!1};Ue.unstable_batchedUpdates=cu;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!us(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return cs(e,t,n,!1,r)};Ue.version="18.3.1-next-f1338f8080-20240426";function Mh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Mh)}catch(e){console.error(e)}}Mh(),Mf.exports=Ue;var ds=Mf.exports;const c0=yf(ds);var td=ds;fl.createRoot=td.createRoot,fl.hydrateRoot=td.hydrateRoot;const Lh=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),fs=w.createContext({}),ps=w.createContext(null),hs=typeof document<"u",vu=hs?w.useLayoutEffect:w.useEffect,_h=w.createContext({strict:!1}),wu=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),d0="framerAppearId",Dh="data-"+wu(d0);function f0(e,t,n,r){const{visualElement:o}=w.useContext(fs),i=w.useContext(_h),s=w.useContext(ps),l=w.useContext(Lh).reducedMotion,a=w.useRef();r=r||i.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:o,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;w.useInsertionEffect(()=>{u&&u.update(n,s)});const c=w.useRef(!!(n[Dh]&&!window.HandoffComplete));return vu(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),w.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function Zn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function p0(e,t,n){return w.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Zn(n)&&(n.current=r))},[t])}function yo(e){return typeof e=="string"||Array.isArray(e)}function ms(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const xu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Su=["initial",...xu];function gs(e){return ms(e.animate)||Su.some(t=>yo(e[t]))}function Vh(e){return!!(gs(e)||e.variants)}function h0(e,t){if(gs(e)){const{initial:n,animate:r}=e;return{initial:n===!1||yo(n)?n:void 0,animate:yo(r)?r:void 0}}return e.inherit!==!1?t:{}}function m0(e){const{initial:t,animate:n}=h0(e,w.useContext(fs));return w.useMemo(()=>({initial:t,animate:n}),[nd(t),nd(n)])}function nd(e){return Array.isArray(e)?e.join(" "):e}const rd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},vo={};for(const e in rd)vo[e]={isEnabled:t=>rd[e].some(n=>!!t[n])};function g0(e){for(const t in e)vo[t]={...vo[t],...e[t]}}const Cu=w.createContext({}),bh=w.createContext({}),y0=Symbol.for("motionComponentSymbol");function v0({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&g0(e);function i(l,a){let u;const c={...w.useContext(Lh),...l,layoutId:w0(l)},{isStatic:d}=c,f=m0(l),m=r(l,d);if(!d&&hs){f.visualElement=f0(o,m,c,t);const y=w.useContext(bh),v=w.useContext(_h).strict;f.visualElement&&(u=f.visualElement.loadFeatures(c,v,e,y))}return w.createElement(fs.Provider,{value:f},u&&f.visualElement?w.createElement(u,{visualElement:f.visualElement,...c}):null,n(o,l,p0(m,f.visualElement,a),m,d,f.visualElement))}const s=w.forwardRef(i);return s[y0]=o,s}function w0({layoutId:e}){const t=w.useContext(Cu).id;return t&&e!==void 0?t+"-"+e:e}function x0(e){function t(r,o={}){return v0(e(r,o))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,o)=>(n.has(o)||n.set(o,t(o)),n.get(o))})}const S0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Pu(e){return typeof e!="string"||e.includes("-")?!1:!!(S0.indexOf(e)>-1||/[A-Z]/.test(e))}const ji={};function C0(e){Object.assign(ji,e)}const Eo=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],bn=new Set(Eo);function Oh(e,{layout:t,layoutId:n}){return bn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!ji[e]||e==="opacity")}const De=e=>!!(e&&e.getVelocity),P0={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},k0=Eo.length;function T0(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,o){let i="";for(let s=0;s<k0;s++){const l=Eo[s];if(e[l]!==void 0){const a=P0[l]||l;i+=`${a}(${e[l]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),o?i=o(e,r?"":i):n&&r&&(i="none"),i}const Ih=e=>t=>typeof t=="string"&&t.startsWith(e),Fh=Ih("--"),ia=Ih("var(--"),E0=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,N0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,sn=(e,t,n)=>Math.min(Math.max(n,e),t),On={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Yr={...On,transform:e=>sn(0,1,e)},Yo={...On,default:1},Xr=e=>Math.round(e*1e5)/1e5,ys=/(-)?([\d]*\.?[\d])+/g,zh=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,R0=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function No(e){return typeof e=="string"}const Ro=e=>({test:t=>No(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),zt=Ro("deg"),yt=Ro("%"),I=Ro("px"),A0=Ro("vh"),M0=Ro("vw"),od={...yt,parse:e=>yt.parse(e)/100,transform:e=>yt.transform(e*100)},id={...On,transform:Math.round},jh={borderWidth:I,borderTopWidth:I,borderRightWidth:I,borderBottomWidth:I,borderLeftWidth:I,borderRadius:I,radius:I,borderTopLeftRadius:I,borderTopRightRadius:I,borderBottomRightRadius:I,borderBottomLeftRadius:I,width:I,maxWidth:I,height:I,maxHeight:I,size:I,top:I,right:I,bottom:I,left:I,padding:I,paddingTop:I,paddingRight:I,paddingBottom:I,paddingLeft:I,margin:I,marginTop:I,marginRight:I,marginBottom:I,marginLeft:I,rotate:zt,rotateX:zt,rotateY:zt,rotateZ:zt,scale:Yo,scaleX:Yo,scaleY:Yo,scaleZ:Yo,skew:zt,skewX:zt,skewY:zt,distance:I,translateX:I,translateY:I,translateZ:I,x:I,y:I,z:I,perspective:I,transformPerspective:I,opacity:Yr,originX:od,originY:od,originZ:I,zIndex:id,fillOpacity:Yr,strokeOpacity:Yr,numOctaves:id};function ku(e,t,n,r){const{style:o,vars:i,transform:s,transformOrigin:l}=e;let a=!1,u=!1,c=!0;for(const d in t){const f=t[d];if(Fh(d)){i[d]=f;continue}const m=jh[d],y=N0(f,m);if(bn.has(d)){if(a=!0,s[d]=y,!c)continue;f!==(m.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,l[d]=y):o[d]=y}if(t.transform||(a||r?o.transform=T0(e.transform,n,c,r):o.transform&&(o.transform="none")),u){const{originX:d="50%",originY:f="50%",originZ:m=0}=l;o.transformOrigin=`${d} ${f} ${m}`}}const Tu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Bh(e,t,n){for(const r in t)!De(t[r])&&!Oh(r,n)&&(e[r]=t[r])}function L0({transformTemplate:e},t,n){return w.useMemo(()=>{const r=Tu();return ku(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function _0(e,t,n){const r=e.style||{},o={};return Bh(o,r,e),Object.assign(o,L0(e,t,n)),e.transformValues?e.transformValues(o):o}function D0(e,t,n){const r={},o=_0(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=o,r}const V0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Bi(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||V0.has(e)}let Uh=e=>!Bi(e);function b0(e){e&&(Uh=t=>t.startsWith("on")?!Bi(t):e(t))}try{b0(require("@emotion/is-prop-valid").default)}catch{}function O0(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Uh(o)||n===!0&&Bi(o)||!t&&!Bi(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function sd(e,t,n){return typeof e=="string"?e:I.transform(t+n*e)}function I0(e,t,n){const r=sd(t,e.x,e.width),o=sd(n,e.y,e.height);return`${r} ${o}`}const F0={offset:"stroke-dashoffset",array:"stroke-dasharray"},z0={offset:"strokeDashoffset",array:"strokeDasharray"};function j0(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?F0:z0;e[i.offset]=I.transform(-r);const s=I.transform(t),l=I.transform(n);e[i.array]=`${s} ${l}`}function Eu(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,d,f){if(ku(e,u,c,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:y,dimensions:v}=e;m.transform&&(v&&(y.transform=m.transform),delete m.transform),v&&(o!==void 0||i!==void 0||y.transform)&&(y.transformOrigin=I0(v,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&j0(m,s,l,a,!1)}const $h=()=>({...Tu(),attrs:{}}),Nu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function B0(e,t,n,r){const o=w.useMemo(()=>{const i=$h();return Eu(i,t,{enableHardwareAcceleration:!1},Nu(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Bh(i,e.style,e),o.style={...i,...o.style}}return o}function U0(e=!1){return(n,r,o,{latestValues:i},s)=>{const a=(Pu(n)?B0:D0)(r,i,s,n),c={...O0(r,typeof n=="string",e),...a,ref:o},{children:d}=r,f=w.useMemo(()=>De(d)?d.get():d,[d]);return w.createElement(n,{...c,children:f})}}function Wh(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Hh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Kh(e,t,n,r){Wh(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Hh.has(o)?o:wu(o),t.attrs[o])}function Ru(e,t){const{style:n}=e,r={};for(const o in n)(De(n[o])||t.style&&De(t.style[o])||Oh(o,e))&&(r[o]=n[o]);return r}function Gh(e,t){const n=Ru(e,t);for(const r in e)if(De(e[r])||De(t[r])){const o=Eo.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=e[r]}return n}function Au(e,t,n,r={},o={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,o)),t}function Qh(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}const Ui=e=>Array.isArray(e),$0=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),W0=e=>Ui(e)?e[e.length-1]||0:e;function hi(e){const t=De(e)?e.get():e;return $0(t)?t.toValue():t}function H0({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,i){const s={latestValues:K0(r,o,i,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Yh=e=>(t,n)=>{const r=w.useContext(fs),o=w.useContext(ps),i=()=>H0(e,t,r,o);return n?i():Qh(i)};function K0(e,t,n,r){const o={},i=r(e,{});for(const f in i)o[f]=hi(i[f]);let{initial:s,animate:l}=e;const a=gs(e),u=Vh(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const d=c?l:s;return d&&typeof d!="boolean"&&!ms(d)&&(Array.isArray(d)?d:[d]).forEach(m=>{const y=Au(e,m);if(!y)return;const{transitionEnd:v,transition:S,...g}=y;for(const p in g){let h=g[p];if(Array.isArray(h)){const x=c?h.length-1:0;h=h[x]}h!==null&&(o[p]=h)}for(const p in v)o[p]=v[p]}),o}const ie=e=>e;class ld{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function G0(e){let t=new ld,n=new ld,r=0,o=!1,i=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const d=c&&o,f=d?t:n;return u&&s.add(a),f.add(a)&&d&&o&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(a),s.has(c)&&(l.schedule(c),e())}o=!1,i&&(i=!1,l.process(a))}};return l}const Xo=["prepare","read","update","preRender","render","postRender"],Q0=40;function Y0(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=Xo.reduce((d,f)=>(d[f]=G0(()=>n=!0),d),{}),s=d=>i[d].process(o),l=()=>{const d=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(d-o.timestamp,Q0),1),o.timestamp=d,o.isProcessing=!0,Xo.forEach(s),o.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,o.isProcessing||e(l)};return{schedule:Xo.reduce((d,f)=>{const m=i[f];return d[f]=(y,v=!1,S=!1)=>(n||a(),m.schedule(y,v,S)),d},{}),cancel:d=>Xo.forEach(f=>i[f].cancel(d)),state:o,steps:i}}const{schedule:G,cancel:Vt,state:xe,steps:Qs}=Y0(typeof requestAnimationFrame<"u"?requestAnimationFrame:ie,!0),X0={useVisualState:Yh({scrapeMotionValuesFromProps:Gh,createRenderState:$h,onMount:(e,t,{renderState:n,latestValues:r})=>{G.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),G.render(()=>{Eu(n,r,{enableHardwareAcceleration:!1},Nu(t.tagName),e.transformTemplate),Kh(t,n)})}})},Z0={useVisualState:Yh({scrapeMotionValuesFromProps:Ru,createRenderState:Tu})};function q0(e,{forwardMotionProps:t=!1},n,r){return{...Pu(e)?X0:Z0,preloadedFeatures:n,useRender:U0(t),createVisualElement:r,Component:e}}function Et(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Xh=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function vs(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const J0=e=>t=>Xh(t)&&e(t,vs(t));function Rt(e,t,n,r){return Et(e,t,J0(n),r)}const e1=(e,t)=>n=>t(e(n)),tn=(...e)=>e.reduce(e1);function Zh(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const ad=Zh("dragHorizontal"),ud=Zh("dragVertical");function qh(e){let t=!1;if(e==="y")t=ud();else if(e==="x")t=ad();else{const n=ad(),r=ud();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Jh(){const e=qh(!0);return e?(e(),!1):!0}class dn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function cd(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),o=(i,s)=>{if(i.pointerType==="touch"||Jh())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&G.update(()=>l[r](i,s))};return Rt(e.current,n,o,{passive:!e.getProps()[r]})}class t1 extends dn{mount(){this.unmount=tn(cd(this.node,!0),cd(this.node,!1))}unmount(){}}class n1 extends dn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tn(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const em=(e,t)=>t?e===t?!0:em(e,t.parentElement):!1;function Ys(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,vs(n))}class r1 extends dn{constructor(){super(...arguments),this.removeStartListeners=ie,this.removeEndListeners=ie,this.removeAccessibleListeners=ie,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=Rt(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();G.update(()=>{!d&&!em(this.node.current,l.target)?c&&c(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=Rt(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tn(i,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||Ys("up",(a,u)=>{const{onTap:c}=this.node.getProps();c&&G.update(()=>c(a,u))})};this.removeEndListeners(),this.removeEndListeners=Et(this.node.current,"keyup",s),Ys("down",(l,a)=>{this.startPress(l,a)})},n=Et(this.node.current,"keydown",t),r=()=>{this.isPressing&&Ys("cancel",(i,s)=>this.cancelPress(i,s))},o=Et(this.node.current,"blur",r);this.removeAccessibleListeners=tn(n,o)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:o}=this.node.getProps();o&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&G.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Jh()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&G.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Rt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Et(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const sa=new WeakMap,Xs=new WeakMap,o1=e=>{const t=sa.get(e.target);t&&t(e)},i1=e=>{e.forEach(o1)};function s1({root:e,...t}){const n=e||document;Xs.has(n)||Xs.set(n,{});const r=Xs.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(i1,{root:e,...t})),r[o]}function l1(e,t,n){const r=s1(t);return sa.set(e,n),r.observe(e),()=>{sa.delete(e),r.unobserve(e)}}const a1={some:0,all:1};class u1 extends dn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:a1[o]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(a)};return l1(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(c1(t,n))&&this.startObserver()}unmount(){}}function c1({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const d1={inView:{Feature:u1},tap:{Feature:r1},focus:{Feature:n1},hover:{Feature:t1}};function tm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function f1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function p1(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function ws(e,t,n){const r=e.getProps();return Au(r,t,n!==void 0?n:r.custom,f1(e),p1(e))}let h1=ie,Mu=ie;const nn=e=>e*1e3,At=e=>e/1e3,m1={current:!1},nm=e=>Array.isArray(e)&&typeof e[0]=="number";function rm(e){return!!(!e||typeof e=="string"&&om[e]||nm(e)||Array.isArray(e)&&e.every(rm))}const Ir=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,om={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ir([0,.65,.55,1]),circOut:Ir([.55,0,1,.45]),backIn:Ir([.31,.01,.66,-.59]),backOut:Ir([.33,1.53,.69,.99])};function im(e){if(e)return nm(e)?Ir(e):Array.isArray(e)?e.map(im):om[e]}function g1(e,t,n,{delay:r=0,duration:o,repeat:i=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=im(l);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}function y1(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const sm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,v1=1e-7,w1=12;function x1(e,t,n,r,o){let i,s,l=0;do s=t+(n-t)/2,i=sm(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>v1&&++l<w1);return s}function Ao(e,t,n,r){if(e===t&&n===r)return ie;const o=i=>x1(i,0,1,e,n);return i=>i===0||i===1?i:sm(o(i),t,r)}const S1=Ao(.42,0,1,1),C1=Ao(0,0,.58,1),lm=Ao(.42,0,.58,1),P1=e=>Array.isArray(e)&&typeof e[0]!="number",am=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,um=e=>t=>1-e(1-t),Lu=e=>1-Math.sin(Math.acos(e)),cm=um(Lu),k1=am(Lu),dm=Ao(.33,1.53,.69,.99),_u=um(dm),T1=am(_u),E1=e=>(e*=2)<1?.5*_u(e):.5*(2-Math.pow(2,-10*(e-1))),N1={linear:ie,easeIn:S1,easeInOut:lm,easeOut:C1,circIn:Lu,circInOut:k1,circOut:cm,backIn:_u,backInOut:T1,backOut:dm,anticipate:E1},dd=e=>{if(Array.isArray(e)){Mu(e.length===4);const[t,n,r,o]=e;return Ao(t,n,r,o)}else if(typeof e=="string")return N1[e];return e},Du=(e,t)=>n=>!!(No(n)&&R0.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),fm=(e,t,n)=>r=>{if(!No(r))return r;const[o,i,s,l]=r.match(ys);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},R1=e=>sn(0,255,e),Zs={...On,transform:e=>Math.round(R1(e))},Pn={test:Du("rgb","red"),parse:fm("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Zs.transform(e)+", "+Zs.transform(t)+", "+Zs.transform(n)+", "+Xr(Yr.transform(r))+")"};function A1(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const la={test:Du("#"),parse:A1,transform:Pn.transform},qn={test:Du("hsl","hue"),parse:fm("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+yt.transform(Xr(t))+", "+yt.transform(Xr(n))+", "+Xr(Yr.transform(r))+")"},Pe={test:e=>Pn.test(e)||la.test(e)||qn.test(e),parse:e=>Pn.test(e)?Pn.parse(e):qn.test(e)?qn.parse(e):la.parse(e),transform:e=>No(e)?e:e.hasOwnProperty("red")?Pn.transform(e):qn.transform(e)},te=(e,t,n)=>-n*e+n*t+e;function qs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function M1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;o=qs(a,l,e+1/3),i=qs(a,l,e),s=qs(a,l,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}const Js=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},L1=[la,Pn,qn],_1=e=>L1.find(t=>t.test(e));function fd(e){const t=_1(e);let n=t.parse(e);return t===qn&&(n=M1(n)),n}const pm=(e,t)=>{const n=fd(e),r=fd(t),o={...n};return i=>(o.red=Js(n.red,r.red,i),o.green=Js(n.green,r.green,i),o.blue=Js(n.blue,r.blue,i),o.alpha=te(n.alpha,r.alpha,i),Pn.transform(o))};function D1(e){var t,n;return isNaN(e)&&No(e)&&(((t=e.match(ys))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(zh))===null||n===void 0?void 0:n.length)||0)>0}const hm={regex:E0,countKey:"Vars",token:"${v}",parse:ie},mm={regex:zh,countKey:"Colors",token:"${c}",parse:Pe.parse},gm={regex:ys,countKey:"Numbers",token:"${n}",parse:On.parse};function el(e,{regex:t,countKey:n,token:r,parse:o}){const i=e.tokenised.match(t);i&&(e["num"+n]=i.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...i.map(o)))}function $i(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&el(n,hm),el(n,mm),el(n,gm),n}function ym(e){return $i(e).values}function vm(e){const{values:t,numColors:n,numVars:r,tokenised:o}=$i(e),i=t.length;return s=>{let l=o;for(let a=0;a<i;a++)a<r?l=l.replace(hm.token,s[a]):a<r+n?l=l.replace(mm.token,Pe.transform(s[a])):l=l.replace(gm.token,Xr(s[a]));return l}}const V1=e=>typeof e=="number"?0:e;function b1(e){const t=ym(e);return vm(e)(t.map(V1))}const ln={test:D1,parse:ym,createTransformer:vm,getAnimatableNone:b1},wm=(e,t)=>n=>`${n>0?t:e}`;function xm(e,t){return typeof e=="number"?n=>te(e,t,n):Pe.test(e)?pm(e,t):e.startsWith("var(")?wm(e,t):Cm(e,t)}const Sm=(e,t)=>{const n=[...e],r=n.length,o=e.map((i,s)=>xm(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}},O1=(e,t)=>{const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=xm(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}},Cm=(e,t)=>{const n=ln.createTransformer(t),r=$i(e),o=$i(t);return r.numVars===o.numVars&&r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?tn(Sm(r.values,o.values),n):wm(e,t)},wo=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},pd=(e,t)=>n=>te(e,t,n);function I1(e){return typeof e=="number"?pd:typeof e=="string"?Pe.test(e)?pm:Cm:Array.isArray(e)?Sm:typeof e=="object"?O1:pd}function F1(e,t,n){const r=[],o=n||I1(e[0]),i=e.length-1;for(let s=0;s<i;s++){let l=o(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||ie:t;l=tn(a,l)}r.push(l)}return r}function Pm(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Mu(i===t.length),i===1)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=F1(t,r,o),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=wo(e[c],e[c+1],u);return s[c](d)};return n?u=>a(sn(e[0],e[i-1],u)):a}function z1(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=wo(0,t,r);e.push(te(n,1,o))}}function j1(e){const t=[0];return z1(t,e.length-1),t}function B1(e,t){return e.map(n=>n*t)}function U1(e,t){return e.map(()=>t||lm).splice(0,e.length-1)}function Wi({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=P1(r)?r.map(dd):dd(r),i={done:!1,value:t[0]},s=B1(n&&n.length===t.length?n:j1(t),e),l=Pm(s,t,{ease:Array.isArray(o)?o:U1(t,o)});return{calculatedDuration:e,next:a=>(i.value=l(a),i.done=a>=e,i)}}function km(e,t){return t?e*(1e3/t):0}const $1=5;function Tm(e,t,n){const r=Math.max(t-$1,0);return km(n-e(r),t-r)}const tl=.001,W1=.01,hd=10,H1=.05,K1=1;function G1({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i;h1(e<=nn(hd));let s=1-t;s=sn(H1,K1,s),e=sn(W1,hd,At(e)),s<1?(o=u=>{const c=u*s,d=c*e,f=c-n,m=aa(u,s),y=Math.exp(-d);return tl-f/m*y},i=u=>{const d=u*s*e,f=d*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,y=Math.exp(-d),v=aa(Math.pow(u,2),s);return(-o(u)+tl>0?-1:1)*((f-m)*y)/v}):(o=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-tl+c*d},i=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const l=5/e,a=Y1(o,i,l);if(e=nn(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Q1=12;function Y1(e,t,n){let r=n;for(let o=1;o<Q1;o++)r=r-e(r)/t(r);return r}function aa(e,t){return e*Math.sqrt(1-t*t)}const X1=["duration","bounce"],Z1=["stiffness","damping","mass"];function md(e,t){return t.some(n=>e[n]!==void 0)}function q1(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!md(e,Z1)&&md(e,X1)){const n=G1(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Em({keyframes:e,restDelta:t,restSpeed:n,...r}){const o=e[0],i=e[e.length-1],s={done:!1,value:o},{stiffness:l,damping:a,mass:u,duration:c,velocity:d,isResolvedFromDuration:f}=q1({...r,velocity:-At(r.velocity||0)}),m=d||0,y=a/(2*Math.sqrt(l*u)),v=i-o,S=At(Math.sqrt(l/u)),g=Math.abs(v)<5;n||(n=g?.01:2),t||(t=g?.005:.5);let p;if(y<1){const h=aa(S,y);p=x=>{const P=Math.exp(-y*S*x);return i-P*((m+y*S*v)/h*Math.sin(h*x)+v*Math.cos(h*x))}}else if(y===1)p=h=>i-Math.exp(-S*h)*(v+(m+S*v)*h);else{const h=S*Math.sqrt(y*y-1);p=x=>{const P=Math.exp(-y*S*x),E=Math.min(h*x,300);return i-P*((m+y*S*v)*Math.sinh(E)+h*v*Math.cosh(E))/h}}return{calculatedDuration:f&&c||null,next:h=>{const x=p(h);if(f)s.done=h>=c;else{let P=m;h!==0&&(y<1?P=Tm(p,h,x):P=0);const E=Math.abs(P)<=n,k=Math.abs(i-x)<=t;s.done=E&&k}return s.value=s.done?i:x,s}}}function gd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},m=T=>l!==void 0&&T<l||a!==void 0&&T>a,y=T=>l===void 0?a:a===void 0||Math.abs(l-T)<Math.abs(a-T)?l:a;let v=n*t;const S=d+v,g=s===void 0?S:s(S);g!==S&&(v=g-d);const p=T=>-v*Math.exp(-T/r),h=T=>g+p(T),x=T=>{const A=p(T),L=h(T);f.done=Math.abs(A)<=u,f.value=f.done?g:L};let P,E;const k=T=>{m(f.value)&&(P=T,E=Em({keyframes:[f.value,y(f.value)],velocity:Tm(h,T,f.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return k(0),{calculatedDuration:null,next:T=>{let A=!1;return!E&&P===void 0&&(A=!0,x(T),k(T)),P!==void 0&&T>P?E.next(T-P):(!A&&x(T),f)}}}const J1=e=>{const t=({timestamp:n})=>e(n);return{start:()=>G.update(t,!0),stop:()=>Vt(t),now:()=>xe.isProcessing?xe.timestamp:performance.now()}},yd=2e4;function vd(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<yd;)t+=n,r=e.next(t);return t>=yd?1/0:t}const ew={decay:gd,inertia:gd,tween:Wi,keyframes:Wi,spring:Em};function Hi({autoplay:e=!0,delay:t=0,driver:n=J1,keyframes:r,type:o="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:c,onUpdate:d,...f}){let m=1,y=!1,v,S;const g=()=>{S=new Promise(V=>{v=V})};g();let p;const h=ew[o]||Wi;let x;h!==Wi&&typeof r[0]!="number"&&(x=Pm([0,100],r,{clamp:!1}),r=[0,100]);const P=h({...f,keyframes:r});let E;l==="mirror"&&(E=h({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let k="idle",T=null,A=null,L=null;P.calculatedDuration===null&&i&&(P.calculatedDuration=vd(P));const{calculatedDuration:$}=P;let b=1/0,J=1/0;$!==null&&(b=$+s,J=b*(i+1)-s);let _=0;const Z=V=>{if(A===null)return;m>0&&(A=Math.min(A,V)),m<0&&(A=Math.min(V-J/m,A)),T!==null?_=T:_=Math.round(V-A)*m;const H=_-t*(m>=0?1:-1),We=m>=0?H<0:H>J;_=Math.max(H,0),k==="finished"&&T===null&&(_=J);let Ve=_,In=P;if(i){const Ss=Math.min(_,J)/b;let Lo=Math.floor(Ss),pn=Ss%1;!pn&&Ss>=1&&(pn=1),pn===1&&Lo--,Lo=Math.min(Lo,i+1),!!(Lo%2)&&(l==="reverse"?(pn=1-pn,s&&(pn-=s/b)):l==="mirror"&&(In=E)),Ve=sn(0,1,pn)*b}const be=We?{done:!1,value:r[0]}:In.next(Ve);x&&(be.value=x(be.value));let{done:fn}=be;!We&&$!==null&&(fn=m>=0?_>=J:_<=0);const Og=T===null&&(k==="finished"||k==="running"&&fn);return d&&d(be.value),Og&&R(),be},B=()=>{p&&p.stop(),p=void 0},se=()=>{k="idle",B(),v(),g(),A=L=null},R=()=>{k="finished",c&&c(),B(),v()},D=()=>{if(y)return;p||(p=n(Z));const V=p.now();a&&a(),T!==null?A=V-T:(!A||k==="finished")&&(A=V),k==="finished"&&g(),L=A,T=null,k="running",p.start()};e&&D();const F={then(V,H){return S.then(V,H)},get time(){return At(_)},set time(V){V=nn(V),_=V,T!==null||!p||m===0?T=V:A=p.now()-V/m},get duration(){const V=P.calculatedDuration===null?vd(P):P.calculatedDuration;return At(V)},get speed(){return m},set speed(V){V===m||!p||(m=V,F.time=At(_))},get state(){return k},play:D,pause:()=>{k="paused",T=_},stop:()=>{y=!0,k!=="idle"&&(k="idle",u&&u(),se())},cancel:()=>{L!==null&&Z(L),se()},complete:()=>{k="finished"},sample:V=>(A=0,Z(V))};return F}function tw(e){let t;return()=>(t===void 0&&(t=e()),t)}const nw=tw(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),rw=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Zo=10,ow=2e4,iw=(e,t)=>t.type==="spring"||e==="backgroundColor"||!rm(t.ease);function sw(e,t,{onUpdate:n,onComplete:r,...o}){if(!(nw()&&rw.has(t)&&!o.repeatDelay&&o.repeatType!=="mirror"&&o.damping!==0&&o.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const c=()=>{a=new Promise(h=>{l=h})};c();let{keyframes:d,duration:f=300,ease:m,times:y}=o;if(iw(t,o)){const h=Hi({...o,repeat:0,delay:0});let x={done:!1,value:d[0]};const P=[];let E=0;for(;!x.done&&E<ow;)x=h.sample(E),P.push(x.value),E+=Zo;y=void 0,d=P,f=E-Zo,m="linear"}const v=g1(e.owner.current,t,d,{...o,duration:f,ease:m,times:y}),S=()=>{u=!1,v.cancel()},g=()=>{u=!0,G.update(S),l(),c()};return v.onfinish=()=>{u||(e.set(y1(d,o)),r&&r(),g())},{then(h,x){return a.then(h,x)},attachTimeline(h){return v.timeline=h,v.onfinish=null,ie},get time(){return At(v.currentTime||0)},set time(h){v.currentTime=nn(h)},get speed(){return v.playbackRate},set speed(h){v.playbackRate=h},get duration(){return At(f)},play:()=>{s||(v.play(),Vt(S))},pause:()=>v.pause(),stop:()=>{if(s=!0,v.playState==="idle")return;const{currentTime:h}=v;if(h){const x=Hi({...o,autoplay:!1});e.setWithVelocity(x.sample(h-Zo).value,x.sample(h).value,Zo)}g()},complete:()=>{u||v.finish()},cancel:g}}function lw({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const o=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ie,pause:ie,stop:ie,then:i=>(i(),Promise.resolve()),cancel:ie,complete:ie});return t?Hi({keyframes:[0,1],duration:0,delay:t,onComplete:o}):o()}const aw={type:"spring",stiffness:500,damping:25,restSpeed:10},uw=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),cw={type:"keyframes",duration:.8},dw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},fw=(e,{keyframes:t})=>t.length>2?cw:bn.has(e)?e.startsWith("scale")?uw(t[1]):aw:dw,ua=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(ln.test(t)||t==="0")&&!t.startsWith("url(")),pw=new Set(["brightness","contrast","saturate","opacity"]);function hw(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(ys)||[];if(!r)return e;const o=n.replace(r,"");let i=pw.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const mw=/([a-z-]*)\(.*?\)/g,ca={...ln,getAnimatableNone:e=>{const t=e.match(mw);return t?t.map(hw).join(" "):e}},gw={...jh,color:Pe,backgroundColor:Pe,outlineColor:Pe,fill:Pe,stroke:Pe,borderColor:Pe,borderTopColor:Pe,borderRightColor:Pe,borderBottomColor:Pe,borderLeftColor:Pe,filter:ca,WebkitFilter:ca},Vu=e=>gw[e];function Nm(e,t){let n=Vu(e);return n!==ca&&(n=ln),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Rm=e=>/^0[^.\s]+$/.test(e);function yw(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Rm(e)}function vw(e,t,n,r){const o=ua(t,n);let i;Array.isArray(n)?i=[...n]:i=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<i.length;u++)i[u]===null&&(i[u]=u===0?s:i[u-1]),yw(i[u])&&a.push(u),typeof i[u]=="string"&&i[u]!=="none"&&i[u]!=="0"&&(l=i[u]);if(o&&a.length&&l)for(let u=0;u<a.length;u++){const c=a[u];i[c]=Nm(t,l)}return i}function ww({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}function bu(e,t){return e[t]||e.default||e}const xw={skipAnimations:!1},Ou=(e,t,n,r={})=>o=>{const i=bu(r,e)||{},s=i.delay||r.delay||0;let{elapsed:l=0}=r;l=l-nn(s);const a=vw(t,e,n,i),u=a[0],c=a[a.length-1],d=ua(e,u),f=ua(e,c);let m={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-l,onUpdate:y=>{t.set(y),i.onUpdate&&i.onUpdate(y)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(ww(i)||(m={...m,...fw(e,m)}),m.duration&&(m.duration=nn(m.duration)),m.repeatDelay&&(m.repeatDelay=nn(m.repeatDelay)),!d||!f||m1.current||i.type===!1||xw.skipAnimations)return lw(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const y=sw(t,e,m);if(y)return y}return Hi(m)};function Ki(e){return!!(De(e)&&e.add)}const Am=e=>/^\-?\d*\.?\d+$/.test(e);function Iu(e,t){e.indexOf(t)===-1&&e.push(t)}function Fu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class zu{constructor(){this.subscriptions=[]}add(t){return Iu(this.subscriptions,t),()=>Fu(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Sw=e=>!isNaN(parseFloat(e));class Cw{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,o=!0)=>{this.prev=this.current,this.current=r;const{delta:i,timestamp:s}=xe;this.lastUpdated!==s&&(this.timeDelta=i,this.lastUpdated=s,G.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>G.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Sw(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new zu);const r=this.events[t].add(n);return t==="change"?()=>{r(),G.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?km(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function mr(e,t){return new Cw(e,t)}const Mm=e=>t=>t.test(e),Pw={test:e=>e==="auto",parse:e=>e},Lm=[On,I,yt,zt,M0,A0,Pw],Ar=e=>Lm.find(Mm(e)),kw=[...Lm,Pe,ln],Tw=e=>kw.find(Mm(e));function Ew(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,mr(n))}function Nw(e,t){const n=ws(e,t);let{transitionEnd:r={},transition:o={},...i}=n?e.makeTargetAnimatable(n,!1):{};i={...i,...r};for(const s in i){const l=W0(i[s]);Ew(e,s,l)}}function Rw(e,t,n){var r,o;const i=Object.keys(t).filter(l=>!e.hasValue(l)),s=i.length;if(s)for(let l=0;l<s;l++){const a=i[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(o=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&o!==void 0?o:t[a]),c!=null&&(typeof c=="string"&&(Am(c)||Rm(c))?c=parseFloat(c):!Tw(c)&&ln.test(u)&&(c=Nm(a,u)),e.addValue(a,mr(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function Aw(e,t){return t?(t[e]||t.default||t).from:void 0}function Mw(e,t,n){const r={};for(const o in e){const i=Aw(o,t);if(i!==void 0)r[o]=i;else{const s=n.getValue(o);s&&(r[o]=s.get())}}return r}function Lw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function _w(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function _m(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(i=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const d in l){const f=e.getValue(d),m=l[d];if(!f||m===void 0||c&&Lw(c,d))continue;const y={delay:n,elapsed:0,...bu(i||{},d)};if(window.HandoffAppearAnimations){const g=e.getProps()[Dh];if(g){const p=window.HandoffAppearAnimations(g,d,f,G);p!==null&&(y.elapsed=p,y.isHandoff=!0)}}let v=!y.isHandoff&&!_w(f,m);if(y.type==="spring"&&(f.getVelocity()||y.velocity)&&(v=!1),f.animation&&(v=!1),v)continue;f.start(Ou(d,f,m,e.shouldReduceMotion&&bn.has(d)?{type:!1}:y));const S=f.animation;Ki(a)&&(a.add(d),S.then(()=>a.remove(d))),u.push(S)}return s&&Promise.all(u).then(()=>{s&&Nw(e,s)}),u}function da(e,t,n={}){const r=ws(e,t,n.custom);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const i=r?()=>Promise.all(_m(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=o;return Dw(e,t,u+a,c,d,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[a,u]=l==="beforeChildren"?[i,s]:[s,i];return a().then(()=>u())}else return Promise.all([i(),s(n.delay)])}function Dw(e,t,n=0,r=0,o=1,i){const s=[],l=(e.variantChildren.size-1)*r,a=o===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(Vw).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(da(u,t,{...i,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function Vw(e,t){return e.sortNodePosition(t)}function bw(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>da(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=da(e,t,n);else{const o=typeof t=="function"?ws(e,t,n.custom):t;r=Promise.all(_m(e,o,n))}return r.then(()=>e.notify("AnimationComplete",t))}const Ow=[...xu].reverse(),Iw=xu.length;function Fw(e){return t=>Promise.all(t.map(({animation:n,options:r})=>bw(e,n,r)))}function zw(e){let t=Fw(e);const n=Bw();let r=!0;const o=(a,u)=>{const c=ws(e,u);if(c){const{transition:d,transitionEnd:f,...m}=c;a={...a,...m,...f}}return a};function i(a){t=a(e)}function s(a,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},f=[],m=new Set;let y={},v=1/0;for(let g=0;g<Iw;g++){const p=Ow[g],h=n[p],x=c[p]!==void 0?c[p]:d[p],P=yo(x),E=p===u?h.isActive:null;E===!1&&(v=g);let k=x===d[p]&&x!==c[p]&&P;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),h.protectedKeys={...y},!h.isActive&&E===null||!x&&!h.prevProp||ms(x)||typeof x=="boolean")continue;let A=jw(h.prevProp,x)||p===u&&h.isActive&&!k&&P||g>v&&P,L=!1;const $=Array.isArray(x)?x:[x];let b=$.reduce(o,{});E===!1&&(b={});const{prevResolvedValues:J={}}=h,_={...J,...b},Z=B=>{A=!0,m.has(B)&&(L=!0,m.delete(B)),h.needsAnimating[B]=!0};for(const B in _){const se=b[B],R=J[B];if(y.hasOwnProperty(B))continue;let D=!1;Ui(se)&&Ui(R)?D=!tm(se,R):D=se!==R,D?se!==void 0?Z(B):m.add(B):se!==void 0&&m.has(B)?Z(B):h.protectedKeys[B]=!0}h.prevProp=x,h.prevResolvedValues=b,h.isActive&&(y={...y,...b}),r&&e.blockInitialAnimation&&(A=!1),A&&(!k||L)&&f.push(...$.map(B=>({animation:B,options:{type:p,...a}})))}if(m.size){const g={};m.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(g[p]=h)}),f.push({animation:g})}let S=!!f.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(f):Promise.resolve()}function l(a,u,c){var d;if(n[a].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(m=>{var y;return(y=m.animationState)===null||y===void 0?void 0:y.setActive(a,u)}),n[a].isActive=u;const f=s(c,a);for(const m in n)n[m].protectedKeys={};return f}return{animateChanges:s,setActive:l,setAnimateFunction:i,getState:()=>n}}function jw(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!tm(t,e):!1}function hn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Bw(){return{animate:hn(!0),whileInView:hn(),whileHover:hn(),whileTap:hn(),whileDrag:hn(),whileFocus:hn(),exit:hn()}}class Uw extends dn{constructor(t){super(t),t.animationState||(t.animationState=zw(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),ms(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let $w=0;class Ww extends dn{constructor(){super(...arguments),this.id=$w++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===o)return;const i=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Hw={animation:{Feature:Uw},exit:{Feature:Ww}},wd=(e,t)=>Math.abs(e-t);function Kw(e,t){const n=wd(e.x,t.x),r=wd(e.y,t.y);return Math.sqrt(n**2+r**2)}class Dm{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=rl(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Kw(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:y}=d,{timestamp:v}=xe;this.history.push({...y,timestamp:v});const{onStart:S,onMove:g}=this.handlers;f||(S&&S(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=nl(f,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:y,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=rl(d.type==="pointercancel"?this.lastMoveEventInfo:nl(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,S),y&&y(d,S)},!Xh(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=vs(t),l=nl(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=xe;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,rl(l,this.history)),this.removeListeners=tn(Rt(this.contextWindow,"pointermove",this.handlePointerMove),Rt(this.contextWindow,"pointerup",this.handlePointerUp),Rt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Vt(this.updatePoint)}}function nl(e,t){return t?{point:t(e.point)}:e}function xd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rl({point:e},t){return{point:e,delta:xd(e,Vm(t)),offset:xd(e,Gw(t)),velocity:Qw(t,.1)}}function Gw(e){return e[0]}function Vm(e){return e[e.length-1]}function Qw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Vm(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>nn(t)));)n--;if(!r)return{x:0,y:0};const i=At(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Be(e){return e.max-e.min}function fa(e,t=0,n=.01){return Math.abs(e-t)<=n}function Sd(e,t,n,r=.5){e.origin=r,e.originPoint=te(t.min,t.max,e.origin),e.scale=Be(n)/Be(t),(fa(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=te(n.min,n.max,e.origin)-e.originPoint,(fa(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Zr(e,t,n,r){Sd(e.x,t.x,n.x,r?r.originX:void 0),Sd(e.y,t.y,n.y,r?r.originY:void 0)}function Cd(e,t,n){e.min=n.min+t.min,e.max=e.min+Be(t)}function Yw(e,t,n){Cd(e.x,t.x,n.x),Cd(e.y,t.y,n.y)}function Pd(e,t,n){e.min=t.min-n.min,e.max=e.min+Be(t)}function qr(e,t,n){Pd(e.x,t.x,n.x),Pd(e.y,t.y,n.y)}function Xw(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?te(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?te(n,e,r.max):Math.min(e,n)),e}function kd(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Zw(e,{top:t,left:n,bottom:r,right:o}){return{x:kd(e.x,n,o),y:kd(e.y,t,r)}}function Td(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function qw(e,t){return{x:Td(e.x,t.x),y:Td(e.y,t.y)}}function Jw(e,t){let n=.5;const r=Be(e),o=Be(t);return o>r?n=wo(t.min,t.max-r,e.min):r>o&&(n=wo(e.min,e.max-o,t.min)),sn(0,1,n)}function ex(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const pa=.35;function tx(e=pa){return e===!1?e=0:e===!0&&(e=pa),{x:Ed(e,"left","right"),y:Ed(e,"top","bottom")}}function Ed(e,t,n){return{min:Nd(e,t),max:Nd(e,n)}}function Nd(e,t){return typeof e=="number"?e:e[t]||0}const Rd=()=>({translate:0,scale:1,origin:0,originPoint:0}),Jn=()=>({x:Rd(),y:Rd()}),Ad=()=>({min:0,max:0}),ae=()=>({x:Ad(),y:Ad()});function Ke(e){return[e("x"),e("y")]}function bm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nx({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function rx(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ol(e){return e===void 0||e===1}function ha({scale:e,scaleX:t,scaleY:n}){return!ol(e)||!ol(t)||!ol(n)}function yn(e){return ha(e)||Om(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Om(e){return Md(e.x)||Md(e.y)}function Md(e){return e&&e!=="0%"}function Gi(e,t,n){const r=e-n,o=t*r;return n+o}function Ld(e,t,n,r,o){return o!==void 0&&(e=Gi(e,o,r)),Gi(e,n,r)+t}function ma(e,t=0,n=1,r,o){e.min=Ld(e.min,t,n,r,o),e.max=Ld(e.max,t,n,r,o)}function Im(e,{x:t,y:n}){ma(e.x,t.translate,t.scale,t.originPoint),ma(e.y,n.translate,n.scale,n.originPoint)}function ox(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let l=0;l<o;l++){i=n[l],s=i.projectionDelta;const a=i.instance;a&&a.style&&a.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&er(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Im(e,s)),r&&yn(i.latestValues)&&er(e,i.latestValues))}t.x=_d(t.x),t.y=_d(t.y)}function _d(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ut(e,t){e.min=e.min+t,e.max=e.max+t}function Dd(e,t,[n,r,o]){const i=t[o]!==void 0?t[o]:.5,s=te(e.min,e.max,i);ma(e,t[n],t[r],s,t.scale)}const ix=["x","scaleX","originX"],sx=["y","scaleY","originY"];function er(e,t){Dd(e.x,t,ix),Dd(e.y,t,sx)}function Fm(e,t){return bm(rx(e.getBoundingClientRect(),t))}function lx(e,t,n){const r=Fm(e,n),{scroll:o}=t;return o&&(Ut(r.x,o.offset.x),Ut(r.y,o.offset.y)),r}const zm=({current:e})=>e?e.ownerDocument.defaultView:null,ax=new WeakMap;class ux{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ae(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(vs(c,"page").point)},i=(c,d)=>{const{drag:f,dragPropagation:m,onDragStart:y}=this.getProps();if(f&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=qh(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ke(S=>{let g=this.getAxisMotionValue(S).get()||0;if(yt.test(g)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[S];h&&(g=Be(h)*(parseFloat(g)/100))}}this.originPoint[S]=g}),y&&G.update(()=>y(c,d),!1,!0);const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},s=(c,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:y,onDrag:v}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:S}=d;if(m&&this.currentDirection===null){this.currentDirection=cx(S),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",d.point,S),this.updateAxis("y",d.point,S),this.visualElement.render(),v&&v(c,d)},l=(c,d)=>this.stop(c,d),a=()=>Ke(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Dm(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:zm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&G.update(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!qo(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=Xw(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Zn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=Zw(o.layoutBox,n):this.constraints=!1,this.elastic=tx(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Ke(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=ex(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Zn(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=lx(r,o.root,this.visualElement.getTransformPagePoint());let s=qw(o.layout.layoutBox,i);if(n){const l=n(nx(s));this.hasMutatedConstraints=!!l,l&&(s=bm(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Ke(c=>{if(!qo(c,n,this.currentDirection))return;let d=a&&a[c]||{};s&&(d={min:0,max:0});const f=o?200:1e6,m=o?40:1e7,y={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(c,y)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Ou(t,r,0,n))}stopAnimation(){Ke(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ke(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ke(n=>{const{drag:r}=this.getProps();if(!qo(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:l}=o.layout.layoutBox[n];i.set(t[n]-te(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Zn(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Ke(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();o[s]=Jw({min:a,max:a},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ke(s=>{if(!qo(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(te(a,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;ax.set(this.visualElement,this);const t=this.visualElement.current,n=Rt(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();Zn(a)&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),r();const s=Et(window,"resize",()=>this.scalePositionWithinConstraints()),l=o.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ke(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=a[c].translate,d.set(d.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=pa,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:l}}}function qo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function cx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class dx extends dn{constructor(t){super(t),this.removeGroupControls=ie,this.removeListeners=ie,this.controls=new ux(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ie}unmount(){this.removeGroupControls(),this.removeListeners()}}const Vd=e=>(t,n)=>{e&&G.update(()=>e(t,n))};class fx extends dn{constructor(){super(...arguments),this.removePointerDownListener=ie}onPointerDown(t){this.session=new Dm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:zm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:Vd(t),onStart:Vd(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&G.update(()=>o(i,s))}}}mount(){this.removePointerDownListener=Rt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function px(){const e=w.useContext(ps);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=w.useId();return w.useEffect(()=>r(o),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}const mi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function bd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Mr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(I.test(e))e=parseFloat(e);else return e;const n=bd(e,t.target.x),r=bd(e,t.target.y);return`${n}% ${r}%`}},hx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=ln.parse(e);if(o.length>5)return r;const i=ln.createTransformer(e),s=typeof o[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;o[0+s]/=l,o[1+s]/=a;const u=te(l,a,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}};class mx extends tt.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;C0(gx),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),mi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||G.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function jm(e){const[t,n]=px(),r=w.useContext(Cu);return tt.createElement(mx,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(bh),isPresent:t,safeToRemove:n})}const gx={borderRadius:{...Mr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Mr,borderTopRightRadius:Mr,borderBottomLeftRadius:Mr,borderBottomRightRadius:Mr,boxShadow:hx},Bm=["TopLeft","TopRight","BottomLeft","BottomRight"],yx=Bm.length,Od=e=>typeof e=="string"?parseFloat(e):e,Id=e=>typeof e=="number"||I.test(e);function vx(e,t,n,r,o,i){o?(e.opacity=te(0,n.opacity!==void 0?n.opacity:1,wx(r)),e.opacityExit=te(t.opacity!==void 0?t.opacity:1,0,xx(r))):i&&(e.opacity=te(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<yx;s++){const l=`border${Bm[s]}Radius`;let a=Fd(t,l),u=Fd(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Id(a)===Id(u)?(e[l]=Math.max(te(Od(a),Od(u),r),0),(yt.test(u)||yt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=te(t.rotate||0,n.rotate||0,r))}function Fd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const wx=Um(0,.5,cm),xx=Um(.5,.95,ie);function Um(e,t,n){return r=>r<e?0:r>t?1:n(wo(e,t,r))}function zd(e,t){e.min=t.min,e.max=t.max}function He(e,t){zd(e.x,t.x),zd(e.y,t.y)}function jd(e,t,n,r,o){return e-=t,e=Gi(e,1/n,r),o!==void 0&&(e=Gi(e,1/o,r)),e}function Sx(e,t=0,n=1,r=.5,o,i=e,s=e){if(yt.test(t)&&(t=parseFloat(t),t=te(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=te(i.min,i.max,r);e===i&&(l-=t),e.min=jd(e.min,t,n,l,o),e.max=jd(e.max,t,n,l,o)}function Bd(e,t,[n,r,o],i,s){Sx(e,t[n],t[r],t[o],t.scale,i,s)}const Cx=["x","scaleX","originX"],Px=["y","scaleY","originY"];function Ud(e,t,n,r){Bd(e.x,t,Cx,n?n.x:void 0,r?r.x:void 0),Bd(e.y,t,Px,n?n.y:void 0,r?r.y:void 0)}function $d(e){return e.translate===0&&e.scale===1}function $m(e){return $d(e.x)&&$d(e.y)}function kx(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Wm(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Wd(e){return Be(e.x)/Be(e.y)}class Tx{constructor(){this.members=[]}add(t){Iu(this.members,t),t.scheduleRender()}remove(t){if(Fu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Hd(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(r=`translate3d(${o}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Ex=(e,t)=>e.depth-t.depth;class Nx{constructor(){this.children=[],this.isDirty=!1}add(t){Iu(this.children,t),this.isDirty=!0}remove(t){Fu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ex),this.isDirty=!1,this.children.forEach(t)}}function Rx(e,t){const n=performance.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(Vt(r),e(i-t))};return G.read(r,!0),()=>Vt(r)}function Ax(e){window.MotionDebug&&window.MotionDebug.record(e)}function Mx(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Lx(e,t,n){const r=De(e)?e:mr(e);return r.start(Ou("",r,t,n)),r.animation}const Kd=["","X","Y","Z"],_x={visibility:"hidden"},Gd=1e3;let Dx=0;const vn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Hm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},l=t==null?void 0:t()){this.id=Dx++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,vn.totalNodes=vn.resolvedTargetDeltas=vn.recalculatedProjection=0,this.nodes.forEach(Ox),this.nodes.forEach(Bx),this.nodes.forEach(Ux),this.nodes.forEach(Ix),Ax(vn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new Nx)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new zu),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Mx(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=Rx(f,250),mi.hasAnimatedSinceResize&&(mi.hasAnimatedSinceResize=!1,this.nodes.forEach(Yd))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||c.getDefaultTransition()||Gx,{onLayoutAnimationStart:S,onLayoutAnimationComplete:g}=c.getProps(),p=!this.targetLayout||!Wm(this.targetLayout,y)||m,h=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,h);const x={...bu(v,"layout"),onPlay:S,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x)}else f||Yd(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($x),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Qd);return}this.isUpdating||this.nodes.forEach(zx),this.isUpdating=!1,this.nodes.forEach(jx),this.nodes.forEach(Vx),this.nodes.forEach(bx),this.clearAllSnapshots();const l=performance.now();xe.delta=sn(0,1e3/60,l-xe.timestamp),xe.timestamp=l,xe.isProcessing=!0,Qs.update.process(xe),Qs.preRender.process(xe),Qs.render.process(xe),xe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Fx),this.sharedNodes.forEach(Wx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ae(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!$m(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||yn(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),Qx(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ae();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Ut(l.x,a.offset.x),Ut(l.y,a.offset.y)),l}removeElementScroll(s){const l=ae();He(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){He(l,s);const{scroll:f}=this.root;f&&(Ut(l.x,-f.offset.x),Ut(l.y,-f.offset.y))}Ut(l.x,c.offset.x),Ut(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=ae();He(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&er(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),yn(c.latestValues)&&er(a,c.latestValues)}return yn(this.latestValues)&&er(a,this.latestValues),a}removeTransform(s){const l=ae();He(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!yn(u.latestValues))continue;ha(u.latestValues)&&u.updateSnapshot();const c=ae(),d=u.measurePageBox();He(c,d),Ud(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return yn(this.latestValues)&&Ud(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==xe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=xe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),qr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ae(),this.targetWithTransforms=ae()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Yw(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):He(this.target,this.layout.layoutBox),Im(this.target,this.targetDelta)):He(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),qr(this.relativeTargetOrigin,this.target,m.target),He(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}vn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ha(this.parent.latestValues)||Om(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===xe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;He(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;ox(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:y}=l;if(!y){this.projectionTransform&&(this.projectionDelta=Jn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Jn(),this.projectionDeltaWithTransform=Jn());const v=this.projectionTransform;Zr(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=Hd(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==f||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),vn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},d=Jn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=ae(),m=a?a.source:void 0,y=this.layout?this.layout.source:void 0,v=m!==y,S=this.getStack(),g=!S||S.members.length<=1,p=!!(v&&!g&&this.options.crossfade===!0&&!this.path.some(Kx));this.animationProgress=0;let h;this.mixTargetDelta=x=>{const P=x/1e3;Xd(d.x,s.x,P),Xd(d.y,s.y,P),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(qr(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Hx(this.relativeTarget,this.relativeTargetOrigin,f,P),h&&kx(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=ae()),He(h,this.relativeTarget)),v&&(this.animationValues=c,vx(c,u,this.latestValues,P,p,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{mi.hasAnimatedSinceResize=!0,this.currentAnimation=Lx(0,Gd,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Gd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Km(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ae();const d=Be(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+d;const f=Be(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+f}He(l,a),er(l,c),Zr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Tx),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Kd.length;c++){const d="rotate"+Kd[c];a[d]&&(u[d]=a[d],s.setStaticValue(d,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return _x;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=hi(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=hi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!yn(this.latestValues)&&(v.transform=c?c({},""):"none",this.hasProjected=!1),v}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=Hd(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:m,y}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${y.origin*100}% 0`,d.animationValues?u.opacity=d===this?(a=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const v in ji){if(f[v]===void 0)continue;const{correct:S,applyTo:g}=ji[v],p=u.transform==="none"?f[v]:S(f[v],d);if(g){const h=g.length;for(let x=0;x<h;x++)u[g[x]]=p}else u[v]=p}return this.options.layoutId&&(u.pointerEvents=d===this?hi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Qd),this.root.sharedNodes.clear()}}}function Vx(e){e.updateLayout()}function bx(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?Ke(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=Be(f);f.min=r[d].min,f.max=f.min+m}):Km(i,n.layoutBox,r)&&Ke(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=Be(r[d]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const l=Jn();Zr(l,r,n.layoutBox);const a=Jn();s?Zr(a,e.applyTransform(o,!0),n.measuredBox):Zr(a,r,n.layoutBox);const u=!$m(l);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const y=ae();qr(y,n.layoutBox,f.layoutBox);const v=ae();qr(v,r,m.layoutBox),Wm(y,v)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=y,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Ox(e){vn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Ix(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Fx(e){e.clearSnapshot()}function Qd(e){e.clearMeasurements()}function zx(e){e.isLayoutDirty=!1}function jx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Yd(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Bx(e){e.resolveTargetDelta()}function Ux(e){e.calcProjection()}function $x(e){e.resetRotation()}function Wx(e){e.removeLeadSnapshot()}function Xd(e,t,n){e.translate=te(t.translate,0,n),e.scale=te(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Zd(e,t,n,r){e.min=te(t.min,n.min,r),e.max=te(t.max,n.max,r)}function Hx(e,t,n,r){Zd(e.x,t.x,n.x,r),Zd(e.y,t.y,n.y,r)}function Kx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Gx={duration:.45,ease:[.4,0,.1,1]},qd=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Jd=qd("applewebkit/")&&!qd("chrome/")?Math.round:ie;function ef(e){e.min=Jd(e.min),e.max=Jd(e.max)}function Qx(e){ef(e.x),ef(e.y)}function Km(e,t,n){return e==="position"||e==="preserve-aspect"&&!fa(Wd(t),Wd(n),.2)}const Yx=Hm({attachResizeListener:(e,t)=>Et(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),il={current:void 0},Gm=Hm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!il.current){const e=new Yx({});e.mount(window),e.setOptions({layoutScroll:!0}),il.current=e}return il.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Xx={pan:{Feature:fx},drag:{Feature:dx,ProjectionNode:Gm,MeasureLayout:jm}},Zx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function qx(e){const t=Zx.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function ga(e,t,n=1){const[r,o]=qx(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return Am(s)?parseFloat(s):s}else return ia(o)?ga(o,t,n+1):o}function Jx(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(o=>{const i=o.get();if(!ia(i))return;const s=ga(i,r);s&&o.set(s)});for(const o in t){const i=t[o];if(!ia(i))continue;const s=ga(i,r);s&&(t[o]=s,n||(n={}),n[o]===void 0&&(n[o]=i))}return{target:t,transitionEnd:n}}const eS=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Qm=e=>eS.has(e),tS=e=>Object.keys(e).some(Qm),tf=e=>e===On||e===I,nf=(e,t)=>parseFloat(e.split(", ")[t]),rf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/);if(o)return nf(o[1],t);{const i=r.match(/^matrix\((.+)\)$/);return i?nf(i[1],e):0}},nS=new Set(["x","y","z"]),rS=Eo.filter(e=>!nS.has(e));function oS(e){const t=[];return rS.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const gr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:rf(4,13),y:rf(5,14)};gr.translateX=gr.x;gr.translateY=gr.y;const iS=(e,t,n)=>{const r=t.measureViewportBox(),o=t.current,i=getComputedStyle(o),{display:s}=i,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=gr[u](r,i)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(l[u]),e[u]=gr[u](a,i)}),e},sS=(e,t,n={},r={})=>{t={...t},r={...r};const o=Object.keys(t).filter(Qm);let i=[],s=!1;const l=[];if(o.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],d=Ar(c);const f=t[a];let m;if(Ui(f)){const y=f.length,v=f[0]===null?1:0;c=f[v],d=Ar(c);for(let S=v;S<y&&f[S]!==null;S++)m?Mu(Ar(f[S])===m):m=Ar(f[S])}else m=Ar(f);if(d!==m)if(tf(d)&&tf(m)){const y=u.get();typeof y=="string"&&u.set(parseFloat(y)),typeof f=="string"?t[a]=parseFloat(f):Array.isArray(f)&&m===I&&(t[a]=f.map(parseFloat))}else d!=null&&d.transform&&(m!=null&&m.transform)&&(c===0||f===0)?c===0?u.set(m.transform(c)):t[a]=d.transform(f):(s||(i=oS(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(f))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=iS(t,e,l);return i.length&&i.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),hs&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function lS(e,t,n,r){return tS(t)?sS(e,t,n,r):{target:t,transitionEnd:r}}const aS=(e,t,n,r)=>{const o=Jx(e,t,r);return t=o.target,r=o.transitionEnd,lS(e,t,n,r)},ya={current:null},Ym={current:!1};function uS(){if(Ym.current=!0,!!hs)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ya.current=e.matches;e.addListener(t),t()}else ya.current=!1}function cS(e,t,n){const{willChange:r}=t;for(const o in t){const i=t[o],s=n[o];if(De(i))e.addValue(o,i),Ki(r)&&r.add(o);else if(De(s))e.addValue(o,mr(i,{owner:e})),Ki(r)&&r.remove(o);else if(s!==i)if(e.hasValue(o)){const l=e.getValue(o);!l.hasAnimated&&l.set(i)}else{const l=e.getStaticValue(o);e.addValue(o,mr(l!==void 0?l:i,{owner:e}))}}for(const o in n)t[o]===void 0&&e.removeValue(o);return t}const of=new WeakMap,Xm=Object.keys(vo),dS=Xm.length,sf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],fS=Su.length;class pS{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,visualState:i},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>G.render(this.render,!1,!0);const{latestValues:l,renderState:a}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=s,this.isControllingVariants=gs(n),this.isVariantNode=Vh(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const f=c[d];l[d]!==void 0&&De(f)&&(f.set(l[d],!1),Ki(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,of.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Ym.current||uS(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ya.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){of.delete(this.current),this.projection&&this.projection.unmount(),Vt(this.notifyUpdate),Vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=bn.has(t),o=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&G.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{o(),i()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,o,i){let s,l;for(let a=0;a<dS;a++){const u=Xm[a],{isEnabled:c,Feature:d,ProjectionNode:f,MeasureLayout:m}=vo[u];f&&(s=f),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),m&&(l=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:d,layoutScroll:f,layoutRoot:m}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||d&&Zn(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:i,layoutScroll:f,layoutRoot:m})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ae()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<sf.length;r++){const o=sf[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i=t["on"+o];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=cS(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<fS;r++){const o=Su[r],i=this.props[o];(yo(i)||i===!1)&&(n[o]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=mr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,o=typeof r=="string"||typeof r=="object"?(n=Au(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!De(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new zu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Zm extends pS{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:o},i){let s=Mw(r,t||{},this);if(o&&(n&&(n=o(n)),r&&(r=o(r)),s&&(s=o(s))),i){Rw(this,r,s);const l=aS(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function hS(e){return window.getComputedStyle(e)}class mS extends Zm{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(bn.has(n)){const r=Vu(n);return r&&r.default||0}else{const r=hS(t),o=(Fh(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Fm(t,n)}build(t,n,r,o){ku(t,n,r,o.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Ru(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;De(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,o){Wh(t,n,r,o)}}class gS extends Zm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(bn.has(n)){const r=Vu(n);return r&&r.default||0}return n=Hh.has(n)?n:wu(n),t.getAttribute(n)}measureInstanceViewportBox(){return ae()}scrapeMotionValuesFromProps(t,n){return Gh(t,n)}build(t,n,r,o){Eu(t,n,r,this.isSVGTag,o.transformTemplate)}renderInstance(t,n,r,o){Kh(t,n,r,o)}mount(t){this.isSVGTag=Nu(t.tagName),super.mount(t)}}const yS=(e,t)=>Pu(e)?new gS(t,{enableHardwareAcceleration:!1}):new mS(t,{enableHardwareAcceleration:!0}),vS={layout:{ProjectionNode:Gm,MeasureLayout:jm}},wS={...Hw,...d1,...Xx,...vS},ot=x0((e,t)=>q0(e,t,wS,yS));function qm(){const e=w.useRef(!1);return vu(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function xS(){const e=qm(),[t,n]=w.useState(0),r=w.useCallback(()=>{e.current&&n(t+1)},[t]);return[w.useCallback(()=>G.postRender(r),[r]),t]}class SS extends w.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function CS({children:e,isPresent:t}){const n=w.useId(),r=w.useRef(null),o=w.useRef({width:0,height:0,top:0,left:0});return w.useInsertionEffect(()=>{const{width:i,height:s,top:l,left:a}=o.current;if(t||!r.current||!i||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${s}px !important;
            top: ${l}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),w.createElement(SS,{isPresent:t,childRef:r,sizeRef:o},w.cloneElement(e,{ref:r}))}const sl=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const l=Qh(PS),a=w.useId(),u=w.useMemo(()=>({id:a,initial:t,isPresent:n,custom:o,onExitComplete:c=>{l.set(c,!0);for(const d of l.values())if(!d)return;r&&r()},register:c=>(l.set(c,!1),()=>l.delete(c))}),i?void 0:[n]);return w.useMemo(()=>{l.forEach((c,d)=>l.set(d,!1))},[n]),w.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=w.createElement(CS,{isPresent:n},e)),w.createElement(ps.Provider,{value:u},e)};function PS(){return new Map}function kS(e){return w.useEffect(()=>()=>e(),[])}const wn=e=>e.key||"";function TS(e,t){e.forEach(n=>{const r=wn(n);t.set(r,n)})}function ES(e){const t=[];return w.Children.forEach(e,n=>{w.isValidElement(n)&&t.push(n)}),t}const NS=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:o,presenceAffectsLayout:i=!0,mode:s="sync"})=>{const l=w.useContext(Cu).forceRender||xS()[0],a=qm(),u=ES(e);let c=u;const d=w.useRef(new Map).current,f=w.useRef(c),m=w.useRef(new Map).current,y=w.useRef(!0);if(vu(()=>{y.current=!1,TS(u,m),f.current=c}),kS(()=>{y.current=!0,m.clear(),d.clear()}),y.current)return w.createElement(w.Fragment,null,c.map(p=>w.createElement(sl,{key:wn(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:i,mode:s},p)));c=[...c];const v=f.current.map(wn),S=u.map(wn),g=v.length;for(let p=0;p<g;p++){const h=v[p];S.indexOf(h)===-1&&!d.has(h)&&d.set(h,void 0)}return s==="wait"&&d.size&&(c=[]),d.forEach((p,h)=>{if(S.indexOf(h)!==-1)return;const x=m.get(h);if(!x)return;const P=v.indexOf(h);let E=p;if(!E){const k=()=>{d.delete(h);const T=Array.from(m.keys()).filter(A=>!S.includes(A));if(T.forEach(A=>m.delete(A)),f.current=u.filter(A=>{const L=wn(A);return L===h||T.includes(L)}),!d.size){if(a.current===!1)return;l(),r&&r()}};E=w.createElement(sl,{key:wn(x),isPresent:!1,onExitComplete:k,custom:t,presenceAffectsLayout:i,mode:s},x),d.set(h,E)}c.splice(P,0,E)}),c=c.map(p=>{const h=p.key;return d.has(h)?p:w.createElement(sl,{key:wn(p),isPresent:!0,presenceAffectsLayout:i,mode:s},p)}),w.createElement(w.Fragment,null,d.size?c:c.map(p=>w.cloneElement(p)))};function Jm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Jm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function eg(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Jm(e))&&(r&&(r+=" "),r+=t);return r}const ju="-",RS=e=>{const t=MS(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(ju);return l[0]===""&&l.length!==1&&l.shift(),tg(l,t)||AS(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},tg=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?tg(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(ju);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},lf=/^\[(.+)\]$/,AS=e=>{if(lf.test(e)){const t=lf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},MS=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return _S(Object.entries(e.classGroups),n).forEach(([i,s])=>{va(s,r,i,t)}),r},va=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:af(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(LS(o)){va(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{va(s,af(t,i),n,r)})})},af=(e,t)=>{let n=e;return t.split(ju).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},LS=e=>e.isThemeGetter,_S=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,DS=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},ng="!",VS=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,c=0,d;for(let S=0;S<l.length;S++){let g=l[S];if(u===0){if(g===o&&(r||l.slice(S,S+i)===t)){a.push(l.slice(c,S)),c=S+i;continue}if(g==="/"){d=S;continue}}g==="["?u++:g==="]"&&u--}const f=a.length===0?l:l.substring(c),m=f.startsWith(ng),y=m?f.substring(1):f,v=d&&d>c?d-c:void 0;return{modifiers:a,hasImportantModifier:m,baseClassName:y,maybePostfixModifierPosition:v}};return n?l=>n({className:l,parseClassName:s}):s},bS=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},OS=e=>({cache:DS(e.cacheSize),parseClassName:VS(e),...RS(e)}),IS=/\s+/,FS=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(IS);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:m}=n(u);let y=!!m,v=r(y?f.substring(0,m):f);if(!v){if(!y){l=u+(l.length>0?" "+l:l);continue}if(v=r(f),!v){l=u+(l.length>0?" "+l:l);continue}y=!1}const S=bS(c).join(":"),g=d?S+ng:S,p=g+v;if(i.includes(p))continue;i.push(p);const h=o(v,y);for(let x=0;x<h.length;++x){const P=h[x];i.push(g+P)}l=u+(l.length>0?" "+l:l)}return l};function zS(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=rg(t))&&(r&&(r+=" "),r+=n);return r}const rg=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=rg(e[r]))&&(n&&(n+=" "),n+=t);return n};function jS(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((c,d)=>d(c),e());return n=OS(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const c=FS(a,n);return o(a,c),c}return function(){return i(zS.apply(null,arguments))}}const Q=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},og=/^\[(?:([a-z-]+):)?(.+)\]$/i,BS=/^\d+\/\d+$/,US=new Set(["px","full","screen"]),$S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,WS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,HS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,KS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,GS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,xt=e=>lr(e)||US.has(e)||BS.test(e),It=e=>xr(e,"length",tC),lr=e=>!!e&&!Number.isNaN(Number(e)),ll=e=>xr(e,"number",lr),Lr=e=>!!e&&Number.isInteger(Number(e)),QS=e=>e.endsWith("%")&&lr(e.slice(0,-1)),z=e=>og.test(e),Ft=e=>$S.test(e),YS=new Set(["length","size","percentage"]),XS=e=>xr(e,YS,ig),ZS=e=>xr(e,"position",ig),qS=new Set(["image","url"]),JS=e=>xr(e,qS,rC),eC=e=>xr(e,"",nC),_r=()=>!0,xr=(e,t,n)=>{const r=og.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},tC=e=>WS.test(e)&&!HS.test(e),ig=()=>!1,nC=e=>KS.test(e),rC=e=>GS.test(e),oC=()=>{const e=Q("colors"),t=Q("spacing"),n=Q("blur"),r=Q("brightness"),o=Q("borderColor"),i=Q("borderRadius"),s=Q("borderSpacing"),l=Q("borderWidth"),a=Q("contrast"),u=Q("grayscale"),c=Q("hueRotate"),d=Q("invert"),f=Q("gap"),m=Q("gradientColorStops"),y=Q("gradientColorStopPositions"),v=Q("inset"),S=Q("margin"),g=Q("opacity"),p=Q("padding"),h=Q("saturate"),x=Q("scale"),P=Q("sepia"),E=Q("skew"),k=Q("space"),T=Q("translate"),A=()=>["auto","contain","none"],L=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",z,t],b=()=>[z,t],J=()=>["",xt,It],_=()=>["auto",lr,z],Z=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>["start","end","center","between","around","evenly","stretch"],D=()=>["","0",z],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>[lr,z];return{cacheSize:500,separator:":",theme:{colors:[_r],spacing:[xt,It],blur:["none","",Ft,z],brightness:V(),borderColor:[e],borderRadius:["none","","full",Ft,z],borderSpacing:b(),borderWidth:J(),contrast:V(),grayscale:D(),hueRotate:V(),invert:D(),gap:b(),gradientColorStops:[e],gradientColorStopPositions:[QS,It],inset:$(),margin:$(),opacity:V(),padding:b(),saturate:V(),scale:V(),sepia:D(),skew:V(),space:b(),translate:b()},classGroups:{aspect:[{aspect:["auto","square","video",z]}],container:["container"],columns:[{columns:[Ft]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Z(),z]}],overflow:[{overflow:L()}],"overflow-x":[{"overflow-x":L()}],"overflow-y":[{"overflow-y":L()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Lr,z]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",z]}],grow:[{grow:D()}],shrink:[{shrink:D()}],order:[{order:["first","last","none",Lr,z]}],"grid-cols":[{"grid-cols":[_r]}],"col-start-end":[{col:["auto",{span:["full",Lr,z]},z]}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":[_r]}],"row-start-end":[{row:["auto",{span:[Lr,z]},z]}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",z]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...R()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...R(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...R(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",z,t]}],"min-w":[{"min-w":[z,t,"min","max","fit"]}],"max-w":[{"max-w":[z,t,"none","full","min","max","fit","prose",{screen:[Ft]},Ft]}],h:[{h:[z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ft,It]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ll]}],"font-family":[{font:[_r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",z]}],"line-clamp":[{"line-clamp":["none",lr,ll]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",xt,z]}],"list-image":[{"list-image":["none",z]}],"list-style-type":[{list:["none","disc","decimal",z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",xt,It]}],"underline-offset":[{"underline-offset":["auto",xt,z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:b()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Z(),ZS]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",XS]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},JS]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[y]}],"gradient-via-pos":[{via:[y]}],"gradient-to-pos":[{to:[y]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[xt,z]}],"outline-w":[{outline:[xt,It]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:J()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[xt,It]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ft,eC]}],"shadow-color":[{shadow:[_r]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...se(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":se()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Ft,z]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[h]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",z]}],duration:[{duration:V()}],ease:[{ease:["linear","in","out","in-out",z]}],delay:[{delay:V()}],animate:[{animate:["none","spin","ping","pulse","bounce",z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Lr,z]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":b()}],"scroll-mx":[{"scroll-mx":b()}],"scroll-my":[{"scroll-my":b()}],"scroll-ms":[{"scroll-ms":b()}],"scroll-me":[{"scroll-me":b()}],"scroll-mt":[{"scroll-mt":b()}],"scroll-mr":[{"scroll-mr":b()}],"scroll-mb":[{"scroll-mb":b()}],"scroll-ml":[{"scroll-ml":b()}],"scroll-p":[{"scroll-p":b()}],"scroll-px":[{"scroll-px":b()}],"scroll-py":[{"scroll-py":b()}],"scroll-ps":[{"scroll-ps":b()}],"scroll-pe":[{"scroll-pe":b()}],"scroll-pt":[{"scroll-pt":b()}],"scroll-pr":[{"scroll-pr":b()}],"scroll-pb":[{"scroll-pb":b()}],"scroll-pl":[{"scroll-pl":b()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[xt,It,ll]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},iC=jS(oC);function he(...e){return iC(eg(e))}function al(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:2}).format(e)}function Jo(e){return`${e.toFixed(2)}%`}const ut=w.forwardRef(({className:e,...t},n)=>C("div",{ref:n,className:he("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ut.displayName="Card";const ct=w.forwardRef(({className:e,...t},n)=>C("div",{ref:n,className:he("flex flex-col space-y-1.5 p-6",e),...t}));ct.displayName="CardHeader";const dt=w.forwardRef(({className:e,...t},n)=>C("h3",{ref:n,className:he("text-2xl font-semibold leading-none tracking-tight",e),...t}));dt.displayName="CardTitle";const Fr=w.forwardRef(({className:e,...t},n)=>C("p",{ref:n,className:he("text-sm text-muted-foreground",e),...t}));Fr.displayName="CardDescription";const ft=w.forwardRef(({className:e,...t},n)=>C("div",{ref:n,className:he("p-6 pt-0",e),...t}));ft.displayName="CardContent";const sC=w.forwardRef(({className:e,...t},n)=>C("div",{ref:n,className:he("flex items-center p-6 pt-0",e),...t}));sC.displayName="CardFooter";const uf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,cf=eg,Bu=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return cf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],d=i==null?void 0:i[u];if(c===null)return null;const f=uf(c)||uf(d);return o[u][f]}),l=n&&Object.entries(n).reduce((u,c)=>{let[d,f]=c;return f===void 0||(u[d]=f),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:d,className:f,...m}=c;return Object.entries(m).every(y=>{let[v,S]=y;return Array.isArray(S)?S.includes({...i,...l}[v]):{...i,...l}[v]===S})?[...u,d,f]:u},[]);return cf(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)},lC=Bu("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function St({className:e,variant:t,...n}){return C("div",{className:he(lC({variant:t}),e),...n})}function df(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function sg(...e){return t=>{let n=!1;const r=e.map(o=>{const i=df(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():df(e[o],null)}}}}function Ln(...e){return w.useCallback(sg(...e),e)}function Qi(e){const t=uC(e),n=w.forwardRef((r,o)=>{const{children:i,...s}=r,l=w.Children.toArray(i),a=l.find(dC);if(a){const u=a.props.children,c=l.map(d=>d===a?w.Children.count(u)>1?w.Children.only(null):w.isValidElement(u)?u.props.children:null:d);return C(t,{...s,ref:o,children:w.isValidElement(u)?w.cloneElement(u,void 0,c):null})}return C(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var aC=Qi("Slot");function uC(e){const t=w.forwardRef((n,r)=>{const{children:o,...i}=n;if(w.isValidElement(o)){const s=pC(o),l=fC(i,o.props);return o.type!==w.Fragment&&(l.ref=r?sg(r,s):s),w.cloneElement(o,l)}return w.Children.count(o)>1?w.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var cC=Symbol("radix.slottable");function dC(e){return w.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===cC}function fC(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{const a=i(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function pC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const hC=Bu("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),zr=w.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>C(r?aC:"button",{className:he(hC({variant:t,size:n,className:e})),ref:i,...o}));zr.displayName="Button";const Uu=w.createContext(),mC=({value:e,onValueChange:t,children:n,className:r,...o})=>{const[i,s]=w.useState(e),l=e!==void 0?e:i,a=t||s;return C(Uu.Provider,{value:{value:l,onValueChange:a},children:C("div",{className:he("w-full",r),...o,children:n})})},gC=({children:e,className:t,...n})=>C("div",{className:he("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...n,children:e}),ul=({value:e,children:t,className:n,...r})=>{const o=w.useContext(Uu);if(!o)throw new Error("TabsTrigger must be used within a Tabs component");const i=o.value===e;return C("button",{className:he("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",i?"bg-background text-foreground shadow-sm":"hover:bg-muted/50",n),onClick:()=>o.onValueChange(e),...r,children:t})},cl=({value:e,children:t,className:n,...r})=>{const o=w.useContext(Uu);if(!o)throw new Error("TabsContent must be used within a Tabs component");return o.value!==e?null:C("div",{className:he("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n),...r,children:t})};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var yC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),vt=(e,t)=>{const n=w.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:l="",children:a,...u},c)=>w.createElement("svg",{ref:c,...yC,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:["lucide",`lucide-${vC(e)}`,l].join(" "),...u},[...t.map(([d,f])=>w.createElement(d,f)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wC=vt("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xC=vt("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ff=vt("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SC=vt("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CC=vt("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pf=vt("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PC=vt("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kC=vt("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TC=vt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EC=vt("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),lg=w.createContext(),NC=()=>{const e=w.useContext(lg);if(!e)throw new Error("useWebSocket must be used within a WebSocketProvider");return e},RC=({children:e})=>{const[t,n]=w.useState(null),[r,o]=w.useState(!1),[i,s]=w.useState({system_status:{supervisor:{is_running:!1,agno_enabled:!1,uptime_seconds:0},connections:{meteora_api:"disconnected",pancakeswap_api:"disconnected",coingecko_api:"disconnected"}},pool_data:[],positions:[],risk_alerts:[],agent_logs:[],token_prices:{},last_update:new Date().toISOString()}),[l,a]=w.useState("disconnected"),u=w.useRef(null),c=w.useRef(0),d=5,f=()=>{try{const h=window.location.protocol==="https:"?"wss:":"ws:",x=window.location.hostname,P=window.location.port||(window.location.protocol==="https:"?"443":"80"),E=`${h}//${x}:${P}/ws`;console.log("🔌 正在連接WebSocket:",E),a("connecting");const k=new WebSocket(E);k.onopen=()=>{console.log("✅ WebSocket連接成功"),n(k),o(!0),a("connected"),c.current=0},k.onmessage=T=>{try{const A=JSON.parse(T.data);switch(console.log("📨 收到WebSocket消息:",A.type),A.type){case"initial_data":case"dashboard_update":s(A.data);break;case"command_response":console.log("🤖 Agent命令響應:",A);break;default:console.log("❓ 未知消息類型:",A.type)}}catch(A){console.error("❌ 解析WebSocket消息失敗:",A)}},k.onclose=T=>{if(console.log("🔌 WebSocket連接關閉:",T.code,T.reason),n(null),o(!1),a("disconnected"),c.current<d){const A=Math.min(1e3*Math.pow(2,c.current),3e4);console.log(`🔄 ${A/1e3}秒後嘗試重連 (${c.current+1}/${d})`),u.current=setTimeout(()=>{c.current++,f()},A)}else console.log("❌ 達到最大重連次數，停止重連"),a("failed")},k.onerror=T=>{console.error("❌ WebSocket錯誤:",T),a("error")}}catch(h){console.error("❌ 創建WebSocket連接失敗:",h),a("error")}},m=h=>{t&&t.readyState===WebSocket.OPEN?(t.send(JSON.stringify(h)),console.log("📤 發送WebSocket消息:",h.type)):console.warn("⚠️ WebSocket未連接，無法發送消息")},y=()=>{m({type:"force_update"})},v=(h,x={})=>{m({type:"agent_command",command:h,...x})},S=h=>{m({type:"agent_command",command:"evaluate_pool",pool_data:h})},g=()=>{m({type:"agent_command",command:"emergency_exit"})};w.useEffect(()=>(f(),()=>{u.current&&clearTimeout(u.current),t&&t.close()}),[]);const p={socket:t,isConnected:r,connectionStatus:l,dashboardData:i,sendMessage:m,forceUpdate:y,executeAgentCommand:v,evaluatePool:S,emergencyExit:g,reconnect:f};return C(lg.Provider,{value:p,children:e})},AC=()=>{const{isConnected:e,connectionStatus:t,dashboardData:n,forceUpdate:r,evaluatePool:o,emergencyExit:i}=NC(),[s,l]=w.useState("overview"),a={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},u={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}},c=y=>{o(y)},d=()=>{window.confirm("確定要執行緊急退出嗎？這將關閉所有LP持倉。")&&i()},f=()=>{switch(t){case"connected":return"text-green-500";case"connecting":return"text-yellow-500";case"disconnected":return"text-gray-500";case"error":return"text-red-500";default:return"text-gray-500"}},m=()=>{switch(t){case"connected":return"已連接";case"connecting":return"連接中...";case"disconnected":return"未連接";case"error":return"連接錯誤";default:return"未知狀態"}};return C("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6",children:O(ot.div,{variants:a,initial:"hidden",animate:"visible",className:"max-w-7xl mx-auto space-y-6",children:[O(ot.div,{variants:u,className:"flex justify-between items-center mb-8",children:[O("div",{className:"text-center flex-1",children:[C("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"DyFlow Dashboard"}),C("p",{className:"text-gray-600",children:"24/7 自動化流動性挖礦策略系統"})]}),O("div",{className:"flex items-center space-x-4",children:[O("div",{className:"flex items-center space-x-2",children:[e?C(kC,{className:`h-5 w-5 ${f()}`}):C(PC,{className:`h-5 w-5 ${f()}`}),C("span",{className:`text-sm ${f()}`,children:m()})]}),O(zr,{size:"sm",variant:"outline",onClick:r,disabled:!e,children:[C(CC,{className:"h-4 w-4 mr-1"}),"刷新"]}),O(zr,{size:"sm",variant:"destructive",onClick:d,disabled:!e,children:[C(xC,{className:"h-4 w-4 mr-1"}),"緊急退出"]})]})]}),O(ot.div,{variants:u,className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6",children:[O(ut,{children:[O(ct,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[C(dt,{className:"text-sm font-medium",children:"Meteora API"}),C(wC,{className:"h-4 w-4 text-muted-foreground"})]}),C(ft,{children:C(St,{variant:n.system_status.connections.meteora_api==="connected"?"default":"destructive",children:n.system_status.connections.meteora_api==="connected"?"已連接":"未連接"})})]}),O(ut,{children:[O(ct,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[C(dt,{className:"text-sm font-medium",children:"PancakeSwap API"}),C(EC,{className:"h-4 w-4 text-muted-foreground"})]}),C(ft,{children:C(St,{variant:n.system_status.connections.pancakeswap_api==="connected"?"default":"destructive",children:n.system_status.connections.pancakeswap_api==="connected"?"已連接":"未連接"})})]}),O(ut,{children:[O(ct,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[C(dt,{className:"text-sm font-medium",children:"CoinGecko API"}),C(pf,{className:"h-4 w-4 text-muted-foreground"})]}),C(ft,{children:C(St,{variant:n.system_status.connections.coingecko_api==="connected"?"default":"destructive",children:n.system_status.connections.coingecko_api==="connected"?"已連接":"未連接"})})]}),O(ut,{children:[O(ct,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[C(dt,{className:"text-sm font-medium",children:"總池子數"}),C(pf,{className:"h-4 w-4 text-muted-foreground"})]}),C(ft,{children:C("div",{className:"text-2xl font-bold",children:n.pool_data.length})})]}),O(ut,{children:[O(ct,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[C(dt,{className:"text-sm font-medium",children:"Agent狀態"}),C(ff,{className:"h-4 w-4 text-muted-foreground"})]}),C(ft,{children:C(St,{variant:n.system_status.supervisor.is_running?"default":"destructive",children:n.system_status.supervisor.is_running?"運行中":"已停止"})})]})]}),C(ot.div,{variants:u,children:O(mC,{value:s,onValueChange:l,className:"w-full",children:[O(gC,{className:"grid w-full grid-cols-3",children:[C(ul,{value:"overview",children:"系統概覽"}),C(ul,{value:"pools",children:"流動性池子"}),C(ul,{value:"positions",children:"LP持倉"})]}),C(cl,{value:"overview",className:"space-y-4",children:O("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[O(ut,{children:[O(ct,{children:[C(dt,{children:"Agent日誌"}),C(Fr,{children:"最新的Agent活動記錄"})]}),C(ft,{children:C("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:n.agent_logs.map((y,v)=>O("div",{className:"flex items-start space-x-2 text-sm",children:[C("span",{className:"text-gray-500 text-xs whitespace-nowrap",children:new Date(y.timestamp).toLocaleTimeString()}),C(St,{variant:y.level==="error"?"destructive":y.level==="warning"?"secondary":"default",className:"text-xs",children:y.agent}),C("span",{className:"flex-1",children:y.message})]},v))})})]}),O(ut,{children:[O(ct,{children:[C(dt,{children:"代幣價格"}),C(Fr,{children:"實時代幣價格信息"})]}),C(ft,{children:C("div",{className:"grid grid-cols-2 gap-4",children:Object.entries(n.token_prices).map(([y,v])=>O("div",{className:"flex justify-between items-center",children:[C("span",{className:"font-medium",children:y}),O("span",{className:"text-green-600",children:["$",v.toFixed(2)]})]},y))})})]})]})}),C(cl,{value:"pools",className:"space-y-4",children:O(ut,{children:[O(ct,{children:[C(dt,{children:"流動性池子"}),O(Fr,{children:["實時監控的高收益LP池子 (",n.pool_data.length," 個池子)"]})]}),C(ft,{children:C("div",{className:"overflow-x-auto",children:O("table",{className:"w-full",children:[C("thead",{children:O("tr",{className:"border-b",children:[C("th",{className:"text-left p-2",children:"交易對"}),C("th",{className:"text-left p-2",children:"網絡"}),C("th",{className:"text-left p-2",children:"TVL"}),C("th",{className:"text-left p-2",children:"APR"}),C("th",{className:"text-left p-2",children:"24h 手續費"}),C("th",{className:"text-left p-2",children:"風險等級"}),C("th",{className:"text-left p-2",children:"操作"})]})}),C("tbody",{children:n.pool_data.map((y,v)=>O(ot.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:v*.05},className:"border-b hover:bg-gray-50",children:[C("td",{className:"p-2 font-medium",children:y.pair}),C("td",{className:"p-2",children:C(St,{variant:y.chain==="bsc"?"secondary":"default",children:y.chain==="bsc"?"BSC":"SOL"})}),C("td",{className:"p-2",children:al(y.tvl_usd)}),C("td",{className:"p-2 text-green-600 font-semibold",children:Jo(y.apr)}),C("td",{className:"p-2",children:al(y.fees_24h)}),C("td",{className:"p-2",children:C(St,{variant:y.risk_level==="low"?"default":y.risk_level==="medium"?"secondary":"destructive",children:y.risk_level==="low"?"低風險":y.risk_level==="medium"?"中風險":"高風險"})}),O("td",{className:"p-2 space-x-2",children:[O(zr,{size:"sm",variant:"outline",onClick:()=>window.open(y.pancake_url||y.meteora_url,"_blank"),children:[C(SC,{className:"h-4 w-4 mr-1"}),"查看"]}),O(zr,{size:"sm",variant:"default",onClick:()=>c(y),disabled:!e,children:[C(ff,{className:"h-4 w-4 mr-1"}),"評估"]})]})]},y.id||v))})]})})})]})}),C(cl,{value:"positions",className:"space-y-4",children:O(ut,{children:[O(ct,{children:[C(dt,{children:"LP持倉"}),O(Fr,{children:["當前活躍的流動性挖礦持倉 (",n.positions.length," 個持倉)"]})]}),C(ft,{children:C("div",{className:"overflow-x-auto",children:O("table",{className:"w-full",children:[C("thead",{children:O("tr",{className:"border-b",children:[C("th",{className:"text-left p-2",children:"池子"}),C("th",{className:"text-left p-2",children:"網絡"}),C("th",{className:"text-left p-2",children:"流動性"}),C("th",{className:"text-left p-2",children:"PnL"}),C("th",{className:"text-left p-2",children:"IL"}),C("th",{className:"text-left p-2",children:"APR"}),C("th",{className:"text-left p-2",children:"狀態"})]})}),C("tbody",{children:n.positions.map((y,v)=>O(ot.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:v*.1},className:"border-b hover:bg-gray-50",children:[C("td",{className:"p-2 font-medium",children:y.pool}),C("td",{className:"p-2",children:C(St,{variant:y.chain==="bsc"?"secondary":"default",children:y.chain==="bsc"?"BSC":"SOL"})}),C("td",{className:"p-2",children:al(y.liquidity_usd)}),O("td",{className:`p-2 font-semibold ${y.pnl_pct>=0?"text-green-600":"text-red-600"}`,children:[y.pnl_pct>=0?"+":"",Jo(y.pnl_pct)]}),C("td",{className:`p-2 font-semibold ${y.il_pct>=0?"text-green-600":"text-red-600"}`,children:Jo(y.il_pct)}),C("td",{className:"p-2 text-green-600 font-semibold",children:Jo(y.apr)}),C("td",{className:"p-2",children:C(St,{variant:y.status==="active"?"default":"secondary",children:y.status==="active"?"活躍":"監控中"})})]},y.id||v))})]})})})]})})]})})]})})};function Ie(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ag(e,t=[]){let n=[];function r(i,s){const l=w.createContext(s),a=n.length;n=[...n,s];const u=d=>{var g;const{scope:f,children:m,...y}=d,v=((g=f==null?void 0:f[e])==null?void 0:g[a])||l,S=w.useMemo(()=>y,Object.values(y));return C(v.Provider,{value:S,children:m})};u.displayName=i+"Provider";function c(d,f){var v;const m=((v=f==null?void 0:f[e])==null?void 0:v[a])||l,y=w.useContext(m);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,MC(o,...t)]}function MC(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function LC(e){const t=e+"CollectionProvider",[n,r]=ag(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=v=>{const{scope:S,children:g}=v,p=tt.useRef(null),h=tt.useRef(new Map).current;return C(o,{scope:S,itemMap:h,collectionRef:p,children:g})};s.displayName=t;const l=e+"CollectionSlot",a=Qi(l),u=tt.forwardRef((v,S)=>{const{scope:g,children:p}=v,h=i(l,g),x=Ln(S,h.collectionRef);return C(a,{ref:x,children:p})});u.displayName=l;const c=e+"CollectionItemSlot",d="data-radix-collection-item",f=Qi(c),m=tt.forwardRef((v,S)=>{const{scope:g,children:p,...h}=v,x=tt.useRef(null),P=Ln(S,x),E=i(c,g);return tt.useEffect(()=>(E.itemMap.set(x,{ref:x,...h}),()=>void E.itemMap.delete(x))),C(f,{[d]:"",ref:P,children:p})});m.displayName=c;function y(v){const S=i(e+"CollectionConsumer",v);return tt.useCallback(()=>{const p=S.collectionRef.current;if(!p)return[];const h=Array.from(p.querySelectorAll(`[${d}]`));return Array.from(S.itemMap.values()).sort((E,k)=>h.indexOf(E.ref.current)-h.indexOf(k.ref.current))},[S.collectionRef,S.itemMap])}return[{Provider:s,Slot:u,ItemSlot:m},y,r]}var _C=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],wt=_C.reduce((e,t)=>{const n=Qi(`Primitive.${t}`),r=w.forwardRef((o,i)=>{const{asChild:s,...l}=o,a=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C(a,{...l,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function ug(e,t){e&&ds.flushSync(()=>e.dispatchEvent(t))}function _n(e){const t=w.useRef(e);return w.useEffect(()=>{t.current=e}),w.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function DC(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e);w.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var VC="DismissableLayer",wa="dismissableLayer.update",bC="dismissableLayer.pointerDownOutside",OC="dismissableLayer.focusOutside",hf,cg=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),dg=w.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=w.useContext(cg),[c,d]=w.useState(null),f=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=w.useState({}),y=Ln(t,k=>d(k)),v=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=v.indexOf(S),p=c?v.indexOf(c):-1,h=u.layersWithOutsidePointerEventsDisabled.size>0,x=p>=g,P=FC(k=>{const T=k.target,A=[...u.branches].some(L=>L.contains(T));!x||A||(o==null||o(k),s==null||s(k),k.defaultPrevented||l==null||l())},f),E=zC(k=>{const T=k.target;[...u.branches].some(L=>L.contains(T))||(i==null||i(k),s==null||s(k),k.defaultPrevented||l==null||l())},f);return DC(k=>{p===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&l&&(k.preventDefault(),l()))},f),w.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(hf=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),mf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=hf)}},[c,f,n,u]),w.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),mf())},[c,u]),w.useEffect(()=>{const k=()=>m({});return document.addEventListener(wa,k),()=>document.removeEventListener(wa,k)},[]),C(wt.div,{...a,ref:y,style:{pointerEvents:h?x?"auto":"none":void 0,...e.style},onFocusCapture:Ie(e.onFocusCapture,E.onFocusCapture),onBlurCapture:Ie(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:Ie(e.onPointerDownCapture,P.onPointerDownCapture)})});dg.displayName=VC;var IC="DismissableLayerBranch",fg=w.forwardRef((e,t)=>{const n=w.useContext(cg),r=w.useRef(null),o=Ln(t,r);return w.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),C(wt.div,{...e,ref:o})});fg.displayName=IC;function FC(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e),r=w.useRef(!1),o=w.useRef(()=>{});return w.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){pg(bC,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function zC(e,t=globalThis==null?void 0:globalThis.document){const n=_n(e),r=w.useRef(!1);return w.useEffect(()=>{const o=i=>{i.target&&!r.current&&pg(OC,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function mf(){const e=new CustomEvent(wa);document.dispatchEvent(e)}function pg(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ug(o,i):o.dispatchEvent(i)}var jC=dg,BC=fg,xo=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},UC="Portal",hg=w.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=w.useState(!1);xo(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?c0.createPortal(C(wt.div,{...r,ref:t}),s):null});hg.displayName=UC;function $C(e,t){return w.useReducer((n,r)=>t[n][r]??n,e)}var mg=e=>{const{present:t,children:n}=e,r=WC(t),o=typeof n=="function"?n({present:r.isPresent}):w.Children.only(n),i=Ln(r.ref,HC(o));return typeof n=="function"||r.isPresent?w.cloneElement(o,{ref:i}):null};mg.displayName="Presence";function WC(e){const[t,n]=w.useState(),r=w.useRef(null),o=w.useRef(e),i=w.useRef("none"),s=e?"mounted":"unmounted",[l,a]=$C(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const u=ei(r.current);i.current=l==="mounted"?u:"none"},[l]),xo(()=>{const u=r.current,c=o.current;if(c!==e){const f=i.current,m=ei(u);e?a("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(c&&f!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),xo(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,d=m=>{const v=ei(r.current).includes(m.animationName);if(m.target===t&&v&&(a("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},f=m=>{m.target===t&&(i.current=ei(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:w.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function ei(e){return(e==null?void 0:e.animationName)||"none"}function HC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var KC=Jg[" useInsertionEffect ".trim().toString()]||xo;function GC({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=QC({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const c=w.useRef(e!==void 0);w.useEffect(()=>{const d=c.current;d!==l&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=l},[l,r])}const u=w.useCallback(c=>{var d;if(l){const f=YC(c)?c(e):c;f!==e&&((d=s.current)==null||d.call(s,f))}else i(c)},[l,e,i,s]);return[a,u]}function QC({defaultProp:e,onChange:t}){const[n,r]=w.useState(e),o=w.useRef(n),i=w.useRef(t);return KC(()=>{i.current=t},[t]),w.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function YC(e){return typeof e=="function"}var XC=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ZC="VisuallyHidden",$u=w.forwardRef((e,t)=>C(wt.span,{...e,ref:t,style:{...XC,...e.style}}));$u.displayName=ZC;var Wu="ToastProvider",[Hu,qC,JC]=LC("Toast"),[gg,NP]=ag("Toast",[JC]),[eP,xs]=gg(Wu),yg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[l,a]=w.useState(null),[u,c]=w.useState(0),d=w.useRef(!1),f=w.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Wu}\`. Expected non-empty \`string\`.`),C(Hu.Provider,{scope:t,children:C(eP,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:w.useCallback(()=>c(m=>m+1),[]),onToastRemove:w.useCallback(()=>c(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:s})})};yg.displayName=Wu;var vg="ToastViewport",tP=["F8"],xa="toast.viewportPause",Sa="toast.viewportResume",wg=w.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=tP,label:o="Notifications ({hotkey})",...i}=e,s=xs(vg,n),l=qC(n),a=w.useRef(null),u=w.useRef(null),c=w.useRef(null),d=w.useRef(null),f=Ln(t,d,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),y=s.toastCount>0;w.useEffect(()=>{const S=g=>{var h;r.length!==0&&r.every(x=>g[x]||g.code===x)&&((h=d.current)==null||h.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),w.useEffect(()=>{const S=a.current,g=d.current;if(y&&S&&g){const p=()=>{if(!s.isClosePausedRef.current){const E=new CustomEvent(xa);g.dispatchEvent(E),s.isClosePausedRef.current=!0}},h=()=>{if(s.isClosePausedRef.current){const E=new CustomEvent(Sa);g.dispatchEvent(E),s.isClosePausedRef.current=!1}},x=E=>{!S.contains(E.relatedTarget)&&h()},P=()=>{S.contains(document.activeElement)||h()};return S.addEventListener("focusin",p),S.addEventListener("focusout",x),S.addEventListener("pointermove",p),S.addEventListener("pointerleave",P),window.addEventListener("blur",p),window.addEventListener("focus",h),()=>{S.removeEventListener("focusin",p),S.removeEventListener("focusout",x),S.removeEventListener("pointermove",p),S.removeEventListener("pointerleave",P),window.removeEventListener("blur",p),window.removeEventListener("focus",h)}}},[y,s.isClosePausedRef]);const v=w.useCallback(({tabbingDirection:S})=>{const p=l().map(h=>{const x=h.ref.current,P=[x,...hP(x)];return S==="forwards"?P:P.reverse()});return(S==="forwards"?p.reverse():p).flat()},[l]);return w.useEffect(()=>{const S=d.current;if(S){const g=p=>{var P,E,k;const h=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!h){const T=document.activeElement,A=p.shiftKey;if(p.target===S&&A){(P=u.current)==null||P.focus();return}const b=v({tabbingDirection:A?"backwards":"forwards"}),J=b.findIndex(_=>_===T);dl(b.slice(J+1))?p.preventDefault():A?(E=u.current)==null||E.focus():(k=c.current)==null||k.focus()}};return S.addEventListener("keydown",g),()=>S.removeEventListener("keydown",g)}},[l,v]),O(BC,{ref:a,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:y?void 0:"none"},children:[y&&C(Ca,{ref:u,onFocusFromOutsideViewport:()=>{const S=v({tabbingDirection:"forwards"});dl(S)}}),C(Hu.Slot,{scope:n,children:C(wt.ol,{tabIndex:-1,...i,ref:f})}),y&&C(Ca,{ref:c,onFocusFromOutsideViewport:()=>{const S=v({tabbingDirection:"backwards"});dl(S)}})]})});wg.displayName=vg;var xg="ToastFocusProxy",Ca=w.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=xs(xg,n);return C($u,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const l=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});Ca.displayName=xg;var Mo="Toast",nP="toast.swipeStart",rP="toast.swipeMove",oP="toast.swipeCancel",iP="toast.swipeEnd",Sg=w.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[l,a]=GC({prop:r,defaultProp:o??!0,onChange:i,caller:Mo});return C(mg,{present:n||l,children:C(aP,{open:l,...s,ref:t,onClose:()=>a(!1),onPause:_n(e.onPause),onResume:_n(e.onResume),onSwipeStart:Ie(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Ie(e.onSwipeMove,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:Ie(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Ie(e.onSwipeEnd,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),a(!1)})})})});Sg.displayName=Mo;var[sP,lP]=gg(Mo,{onClose(){}}),aP=w.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:c,onSwipeMove:d,onSwipeCancel:f,onSwipeEnd:m,...y}=e,v=xs(Mo,n),[S,g]=w.useState(null),p=Ln(t,_=>g(_)),h=w.useRef(null),x=w.useRef(null),P=o||v.duration,E=w.useRef(0),k=w.useRef(P),T=w.useRef(0),{onToastAdd:A,onToastRemove:L}=v,$=_n(()=>{var Z;(S==null?void 0:S.contains(document.activeElement))&&((Z=v.viewport)==null||Z.focus()),s()}),b=w.useCallback(_=>{!_||_===1/0||(window.clearTimeout(T.current),E.current=new Date().getTime(),T.current=window.setTimeout($,_))},[$]);w.useEffect(()=>{const _=v.viewport;if(_){const Z=()=>{b(k.current),u==null||u()},B=()=>{const se=new Date().getTime()-E.current;k.current=k.current-se,window.clearTimeout(T.current),a==null||a()};return _.addEventListener(xa,B),_.addEventListener(Sa,Z),()=>{_.removeEventListener(xa,B),_.removeEventListener(Sa,Z)}}},[v.viewport,P,a,u,b]),w.useEffect(()=>{i&&!v.isClosePausedRef.current&&b(P)},[i,P,v.isClosePausedRef,b]),w.useEffect(()=>(A(),()=>L()),[A,L]);const J=w.useMemo(()=>S?Rg(S):null,[S]);return v.viewport?O(Af,{children:[J&&C(uP,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:J}),C(sP,{scope:n,onClose:$,children:ds.createPortal(C(Hu.ItemSlot,{scope:n,children:C(jC,{asChild:!0,onEscapeKeyDown:Ie(l,()=>{v.isFocusedToastEscapeKeyDownRef.current||$(),v.isFocusedToastEscapeKeyDownRef.current=!1}),children:C(wt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":v.swipeDirection,...y,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Ie(e.onKeyDown,_=>{_.key==="Escape"&&(l==null||l(_.nativeEvent),_.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:Ie(e.onPointerDown,_=>{_.button===0&&(h.current={x:_.clientX,y:_.clientY})}),onPointerMove:Ie(e.onPointerMove,_=>{if(!h.current)return;const Z=_.clientX-h.current.x,B=_.clientY-h.current.y,se=!!x.current,R=["left","right"].includes(v.swipeDirection),D=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,F=R?D(0,Z):0,V=R?0:D(0,B),H=_.pointerType==="touch"?10:2,We={x:F,y:V},Ve={originalEvent:_,delta:We};se?(x.current=We,ti(rP,d,Ve,{discrete:!1})):gf(We,v.swipeDirection,H)?(x.current=We,ti(nP,c,Ve,{discrete:!1}),_.target.setPointerCapture(_.pointerId)):(Math.abs(Z)>H||Math.abs(B)>H)&&(h.current=null)}),onPointerUp:Ie(e.onPointerUp,_=>{const Z=x.current,B=_.target;if(B.hasPointerCapture(_.pointerId)&&B.releasePointerCapture(_.pointerId),x.current=null,h.current=null,Z){const se=_.currentTarget,R={originalEvent:_,delta:Z};gf(Z,v.swipeDirection,v.swipeThreshold)?ti(iP,m,R,{discrete:!0}):ti(oP,f,R,{discrete:!0}),se.addEventListener("click",D=>D.preventDefault(),{once:!0})}})})})}),v.viewport)})]}):null}),uP=e=>{const{__scopeToast:t,children:n,...r}=e,o=xs(Mo,t),[i,s]=w.useState(!1),[l,a]=w.useState(!1);return fP(()=>s(!0)),w.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:C(hg,{asChild:!0,children:C($u,{...r,children:i&&O(Af,{children:[o.label," ",n]})})})},cP="ToastTitle",Cg=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C(wt.div,{...r,ref:t})});Cg.displayName=cP;var dP="ToastDescription",Pg=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C(wt.div,{...r,ref:t})});Pg.displayName=dP;var kg="ToastAction",Tg=w.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?C(Ng,{altText:n,asChild:!0,children:C(Ku,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${kg}\`. Expected non-empty \`string\`.`),null)});Tg.displayName=kg;var Eg="ToastClose",Ku=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=lP(Eg,n);return C(Ng,{asChild:!0,children:C(wt.button,{type:"button",...r,ref:t,onClick:Ie(e.onClick,o.onClose)})})});Ku.displayName=Eg;var Ng=w.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return C(wt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Rg(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),pP(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Rg(r))}}),t}function ti(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ug(o,i):o.dispatchEvent(i)}var gf=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function fP(e=()=>{}){const t=_n(e);xo(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function pP(e){return e.nodeType===e.ELEMENT_NODE}function hP(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function dl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var mP=yg,Ag=wg,Mg=Sg,Lg=Cg,_g=Pg,Dg=Tg,Vg=Ku;const gP=mP,bg=w.forwardRef(({className:e,...t},n)=>C(Ag,{ref:n,className:he("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));bg.displayName=Ag.displayName;const yP=Bu("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),vP=w.forwardRef(({className:e,variant:t,...n},r)=>C(Mg,{ref:r,className:he(yP({variant:t}),e),...n}));vP.displayName=Mg.displayName;const wP=w.forwardRef(({className:e,...t},n)=>C(Dg,{ref:n,className:he("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));wP.displayName=Dg.displayName;const xP=w.forwardRef(({className:e,...t},n)=>C(Vg,{ref:n,className:he("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:C(TC,{className:"h-4 w-4"})}));xP.displayName=Vg.displayName;const SP=w.forwardRef(({className:e,...t},n)=>C(Lg,{ref:n,className:he("text-sm font-semibold",e),...t}));SP.displayName=Lg.displayName;const CP=w.forwardRef(({className:e,...t},n)=>C(_g,{ref:n,className:he("text-sm opacity-90",e),...t}));CP.displayName=_g.displayName;function PP(){return C(gP,{children:C(bg,{})})}function kP(){const[e,t]=w.useState(!0);return w.useEffect(()=>{const n=setTimeout(()=>{t(!1)},2e3);return()=>clearTimeout(n)},[]),e?C("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:O(ot.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center",children:[C(ot.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),C(ot.h1,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},className:"text-2xl font-bold text-gray-800",children:"DyFlow React UI"}),C(ot.p,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},className:"text-gray-600 mt-2",children:"正在初始化現代化界面..."})]})}):C("div",{className:"min-h-screen bg-background",children:O(RC,{children:[C(NS,{children:C(ot.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:C(AC,{})})}),C(PP,{})]})})}fl.createRoot(document.getElementById("root")).render(C(tt.StrictMode,{children:C(kP,{})}));
