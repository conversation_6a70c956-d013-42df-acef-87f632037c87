import { test, expect } from '@playwright/test';

test.describe('DyFlow v3.3 UI Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 導航到應用
    await page.goto('http://localhost:3000');
    
    // 等待頁面加載
    await page.waitForLoadState('networkidle');
  });

  test('應該顯示 DyFlow v3.3 標題', async ({ page }) => {
    // 檢查頁面標題
    await expect(page).toHaveTitle(/DyFlow/);
    
    // 檢查主標題
    await expect(page.locator('h1')).toContainText('DyFlow v3.3');
  });

  test('應該顯示系統概覽標籤', async ({ page }) => {
    // 檢查標籤是否存在
    await expect(page.locator('[data-value="overview"]')).toBeVisible();
    await expect(page.locator('[data-value="portfolio"]')).toBeVisible();
    await expect(page.locator('[data-value="phases"]')).toBeVisible();
    await expect(page.locator('[data-value="pools"]')).toBeVisible();
    await expect(page.locator('[data-value="agents"]')).toBeVisible();
    await expect(page.locator('[data-value="positions"]')).toBeVisible();
  });

  test('投資組合標籤應該顯示正確內容', async ({ page }) => {
    // 點擊投資組合標籤
    await page.click('[data-value="portfolio"]');
    
    // 等待內容加載
    await page.waitForTimeout(1000);
    
    // 檢查投資組合卡片
    await expect(page.locator('text=總投資組合價值')).toBeVisible();
    await expect(page.locator('text=24h 收益')).toBeVisible();
    await expect(page.locator('text=總體 IL')).toBeVisible();
    await expect(page.locator('text=平均 APR')).toBeVisible();
    
    // 檢查風險評估
    await expect(page.locator('text=風險評估')).toBeVisible();
    
    // 檢查策略分佈
    await expect(page.locator('text=策略分佈')).toBeVisible();
    
    // 檢查鏈分佈
    await expect(page.locator('text=鏈分佈')).toBeVisible();
  });

  test('階段監控標籤應該顯示八階段', async ({ page }) => {
    // 點擊階段監控標籤
    await page.click('[data-value="phases"]');
    
    // 等待內容加載
    await page.waitForTimeout(1000);
    
    // 檢查八階段啟動序列標題
    await expect(page.locator('text=DyFlow v3.3 八階段啟動序列')).toBeVisible();
    
    // 檢查進度條
    await expect(page.locator('text=整體進度')).toBeVisible();
    
    // 檢查各個階段
    await expect(page.locator('text=Phase 0: 系統初始化')).toBeVisible();
    await expect(page.locator('text=Phase 1: 健康檢查')).toBeVisible();
    await expect(page.locator('text=Phase 2: UI 啟動')).toBeVisible();
    await expect(page.locator('text=Phase 3: 錢包測試')).toBeVisible();
    await expect(page.locator('text=Phase 4: 市場情報')).toBeVisible();
    await expect(page.locator('text=Phase 5: 投資組合')).toBeVisible();
    await expect(page.locator('text=Phase 6: 策略生成')).toBeVisible();
    await expect(page.locator('text=Phase 7: 交易執行')).toBeVisible();
    await expect(page.locator('text=Phase 8: 風控監控')).toBeVisible();
    
    // 檢查時間軸
    await expect(page.locator('text=啟動時間軸')).toBeVisible();
  });

  test('池子監控標籤應該顯示池子數據', async ({ page }) => {
    // 點擊池子監控標籤
    await page.click('[data-value="pools"]');
    
    // 等待內容加載
    await page.waitForTimeout(2000);
    
    // 檢查池子監控標題
    await expect(page.locator('text=池子監控')).toBeVisible();
    
    // 檢查過濾器
    await expect(page.locator('text=鏈過濾')).toBeVisible();
    
    // 檢查池子列表或表格
    // 注意：這裡可能需要等待真實數據加載
    await page.waitForTimeout(3000);
  });

  test('Agent 狀態標籤應該顯示 Agent 信息', async ({ page }) => {
    // 點擊 Agent 狀態標籤
    await page.click('[data-value="agents"]');
    
    // 等待內容加載
    await page.waitForTimeout(1000);
    
    // 檢查 Agent 監控標題
    await expect(page.locator('text=Agent 監控')).toBeVisible();
    
    // 檢查系統狀態
    await expect(page.locator('text=系統狀態')).toBeVisible();
  });

  test('LP 持倉標籤應該顯示持倉信息', async ({ page }) => {
    // 點擊 LP 持倉標籤
    await page.click('[data-value="positions"]');
    
    // 等待內容加載
    await page.waitForTimeout(1000);
    
    // 檢查持倉相關內容
    // 注意：具體內容取決於 LP 持倉組件的實現
  });

  test('應該能夠在標籤之間切換', async ({ page }) => {
    // 測試標籤切換功能
    await page.click('[data-value="portfolio"]');
    await expect(page.locator('text=總投資組合價值')).toBeVisible();
    
    await page.click('[data-value="phases"]');
    await expect(page.locator('text=八階段啟動序列')).toBeVisible();
    
    await page.click('[data-value="pools"]');
    await page.waitForTimeout(1000);
    
    await page.click('[data-value="agents"]');
    await expect(page.locator('text=Agent 監控')).toBeVisible();
    
    // 回到概覽
    await page.click('[data-value="overview"]');
    await page.waitForTimeout(1000);
  });

  test('投資組合數據應該顯示數值', async ({ page }) => {
    // 點擊投資組合標籤
    await page.click('[data-value="portfolio"]');
    await page.waitForTimeout(2000);
    
    // 檢查是否有數值顯示（不為空或零）
    const portfolioValue = await page.locator('text=總投資組合價值').locator('..').locator('div').nth(1);
    await expect(portfolioValue).not.toBeEmpty();
    
    // 檢查 APR 數值
    const aprValue = await page.locator('text=平均 APR').locator('..').locator('div').nth(1);
    await expect(aprValue).not.toBeEmpty();
  });

  test('階段進度應該顯示百分比', async ({ page }) => {
    // 點擊階段監控標籤
    await page.click('[data-value="phases"]');
    await page.waitForTimeout(1000);
    
    // 檢查進度百分比
    await expect(page.locator('text=% 完成')).toBeVisible();
    
    // 檢查進度條
    await expect(page.locator('[role="progressbar"]')).toBeVisible();
  });

  test('應該響應式設計在不同屏幕尺寸下工作', async ({ page }) => {
    // 測試桌面尺寸
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('h1')).toBeVisible();
    
    // 測試平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();
    
    // 測試手機尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toBeVisible();
  });

  test('數據刷新功能應該工作', async ({ page }) => {
    // 等待初始數據加載
    await page.waitForTimeout(3000);
    
    // 查找刷新按鈕（如果存在）
    const refreshButton = page.locator('button:has-text("刷新")').or(
      page.locator('button[aria-label*="刷新"]')
    ).or(
      page.locator('[data-testid="refresh-button"]')
    );
    
    if (await refreshButton.count() > 0) {
      await refreshButton.click();
      await page.waitForTimeout(1000);
    }
  });

  test('錯誤處理應該正常工作', async ({ page }) => {
    // 模擬網絡錯誤情況
    await page.route('**/api/**', route => {
      route.abort();
    });
    
    // 重新加載頁面
    await page.reload();
    await page.waitForTimeout(3000);
    
    // 檢查是否有錯誤處理或加載狀態
    // 這取決於具體的錯誤處理實現
  });
});
