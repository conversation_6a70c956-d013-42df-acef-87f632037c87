/**
 * DyFlow v3.3 Zustand Store
 * 全局狀態管理，接收 WSHub 事件驅動更新
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { AgentFlowState, PositionData, RiskSummary, InfraHealth } from '../lib/wsHub'

export interface SystemOverview {
  nav: number
  nav24hChange: number
  nav24hPct: number
  fees24h: number
  riskScore: number
  totalPositions: number
  activePositions: number
}

export interface AgnoState {
  // 連接狀態
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error' | 'failed'
  
  // 系統概覽
  systemOverview: SystemOverview
  
  // 交易模式
  tradingMode: 'paused' | 'exit_only' | 'active'
  
  // 工作流程狀態
  workflowRunning: boolean
  currentPhase: number
  
  // Agent 流程
  agentFlow: AgentFlowState[]
  
  // 階段結果
  phaseResults: any[]
  
  // LP 持倉
  positions: PositionData[]
  
  // 池子數據
  hotPools: any[]
  
  // 風險監控
  riskSummary: RiskSummary
  
  // 基礎設施健康
  infraHealth: InfraHealth

  // LaunchWizard 狀態 - v3.4 新增
  wizardState: {
    isOpen: boolean
    currentStep: number
    scanResults: any[]
    proposal: any | null
    selectedPools: string[]
    isScanning: boolean
    isLaunching: boolean
  }

  // Actions
  setConnectionStatus: (status: AgnoState['connectionStatus']) => void
  setSystemOverview: (overview: Partial<SystemOverview>) => void
  setTradingMode: (mode: AgnoState['tradingMode']) => void
  setWorkflowRunning: (running: boolean) => void
  setCurrentPhase: (phase: number) => void
  setAgentFlow: (flow: AgentFlowState[]) => void
  updateAgentStatus: (agentId: string, status: Partial<AgentFlowState>) => void
  setPhaseResults: (results: any[]) => void
  updatePositions: (positions: PositionData[]) => void
  updatePosition: (id: string, updates: Partial<PositionData>) => void
  setHotPools: (pools: any[]) => void
  addPool: (pool: any) => void
  addLPPlan: (plan: any) => void
  updateRiskSummary: (risk: Partial<RiskSummary>) => void
  updateInfraHealth: (health: Partial<InfraHealth>) => void

  // LaunchWizard Actions - v3.4 新增
  setWizardState: (state: Partial<AgnoState['wizardState']>) => void
  openWizard: () => void
  closeWizard: () => void
  setWizardStep: (step: number) => void
  setWizardScanResults: (results: any[]) => void
  setWizardProposal: (proposal: any) => void
  setWizardSelectedPools: (pools: string[]) => void
  setWizardScanning: (scanning: boolean) => void
  setWizardLaunching: (launching: boolean) => void
}

export const useAgnoStore = create<AgnoState>()(
  devtools(
    (set, get) => ({
      // 初始狀態
      connectionStatus: 'connecting',
      
      systemOverview: {
        nav: 0,
        nav24hChange: 0,
        nav24hPct: 0,
        fees24h: 0,
        riskScore: 0,
        totalPositions: 0,
        activePositions: 0
      },
      
      tradingMode: 'paused',
      workflowRunning: false,
      currentPhase: 0,
      
      agentFlow: [
        { id: 'supervisor', name: 'SupervisorAgent', phase: 0, status: 'idle', task: '', ts: 0 },
        { id: 'health', name: 'HealthGuardAgent', phase: 1, status: 'idle', task: '', ts: 0 },
        { id: 'market', name: 'MarketIntelAgent', phase: 4, status: 'idle', task: '', ts: 0 },
        { id: 'portfolio', name: 'PortfolioManagerAgent', phase: 5, status: 'idle', task: '', ts: 0 },
        { id: 'strategy', name: 'StrategyAgent', phase: 6, status: 'idle', task: '', ts: 0 },
        { id: 'execution', name: 'ExecutionAgent', phase: 7, status: 'idle', task: '', ts: 0 },
        { id: 'risk', name: 'RiskSentinelAgent', phase: 8, status: 'idle', task: '', ts: 0 }
      ],
      
      phaseResults: [],
      positions: [],
      hotPools: [
        {
          id: 'bsc_pool_1',
          chain: 'bsc',
          protocol: 'pancakeswap_v3',
          pair: 'BNB/USDT',
          token0: 'BNB',
          token1: 'USDT',
          tvl_usd: 15000000,
          apr: 125.5,
          fees_24h: 75000,
          fee_tvl_ratio: 0.05,
          volatility: 0.25,
          risk_level: 'medium',
          strategy_recommendation: 'CURVE_BALANCED'
        },
        {
          id: 'sol_pool_1',
          chain: 'solana',
          protocol: 'meteora_dlmm_v2',
          pair: 'SOL/USDC',
          token0: 'SOL',
          token1: 'USDC',
          tvl_usd: 12000000,
          apr: 185.2,
          fees_24h: 60000,
          fee_tvl_ratio: 0.08,
          volatility: 0.45,
          risk_level: 'high',
          strategy_recommendation: 'SPOT_IMBALANCED_DAMM'
        }
      ],
      
      riskSummary: {
        ilNet: -1.9,
        var95: 2.8,
        maxDrawdown: -3.2,
        riskScore: 75,
        healthScore: 85
      },
      
      infraHealth: {
        rpcBsc: { status: 'connected', latency: 85 },
        rpcSolana: { status: 'connected', latency: 120 },
        subgraphPancake: { status: 'connected', latency: 95 },
        apiMeteora: { status: 'connected', latency: 110 },
        dbSupabase: { status: 'connected', latency: 65 }
      },

      // LaunchWizard 初始狀態 - v3.4
      wizardState: {
        isOpen: false,
        currentStep: 1,
        scanResults: [],
        proposal: null,
        selectedPools: [],
        isScanning: false,
        isLaunching: false
      },
      
      // Actions
      setConnectionStatus: (status) => set({ connectionStatus: status }),
      
      setSystemOverview: (overview) => set((state) => ({
        systemOverview: { ...state.systemOverview, ...overview }
      })),
      
      setTradingMode: (mode) => set({ tradingMode: mode }),
      
      setWorkflowRunning: (running) => set({ workflowRunning: running }),
      
      setCurrentPhase: (phase) => set({ currentPhase: phase }),
      
      setAgentFlow: (flow) => set({ agentFlow: flow }),

      updateAgentStatus: (agentId, status) => set((state) => ({
        agentFlow: state.agentFlow.map(agent =>
          agent.id === agentId ? { ...agent, ...status } : agent
        )
      })),

      setPhaseResults: (results) => set({ phaseResults: results }),
      
      updatePositions: (positions) => {
        set({ positions })
        
        // 同時更新系統概覽
        const totalPositions = positions.length
        const activePositions = positions.filter(p => p.status === 'active').length
        const nav = positions.reduce((sum, p) => sum + p.liquidityUsd + p.pnlUsd, 0)
        const fees24h = positions.reduce((sum, p) => sum + p.fees24hUsd, 0)
        
        set((state) => ({
          systemOverview: {
            ...state.systemOverview,
            nav,
            fees24h,
            totalPositions,
            activePositions
          }
        }))
      },
      
      updatePosition: (id, updates) => set((state) => ({
        positions: state.positions.map(p => 
          p.id === id ? { ...p, ...updates } : p
        )
      })),
      
      setHotPools: (pools) => set({ hotPools: pools }),

      addPool: (pool) => set((state) => {
        // 檢查是否已存在，避免重複
        const exists = state.hotPools.some(p => p.pool_id === pool.pool_id)
        if (exists) {
          // 更新現有池子
          return {
            hotPools: state.hotPools.map(p =>
              p.pool_id === pool.pool_id ? { ...p, ...pool } : p
            )
          }
        } else {
          // 添加新池子
          return {
            hotPools: [...state.hotPools, {
              ...pool,
              id: pool.pool_id,
              pair: pool.pool_id.split('/').slice(-2).join('/'),
              tvl_usd: pool.tvl,
              fee_tvl_ratio: pool.fee_tvl_pct,
              volatility: pool.sigma
            }]
          }
        }
      }),

      addLPPlan: (plan) => {
        // 這裡可以添加 LP 計劃到狀態中
        console.log('📋 LP Plan added to store:', plan.plan_id)
        // 暫時不存儲 LP 計劃，直接處理
      },

      updateRiskSummary: (risk) => set((state) => ({
        riskSummary: { ...state.riskSummary, ...risk }
      })),
      
      updateInfraHealth: (health) => set((state) => ({
        infraHealth: { ...state.infraHealth, ...health }
      })),

      // LaunchWizard Actions 實現 - v3.4
      setWizardState: (wizardUpdates) => set((state) => ({
        wizardState: { ...state.wizardState, ...wizardUpdates }
      })),

      openWizard: () => set((state) => ({
        wizardState: { ...state.wizardState, isOpen: true, currentStep: 1 }
      })),

      closeWizard: () => set((state) => ({
        wizardState: {
          ...state.wizardState,
          isOpen: false,
          currentStep: 1,
          scanResults: [],
          proposal: null,
          selectedPools: [],
          isScanning: false,
          isLaunching: false
        }
      })),

      setWizardStep: (step) => set((state) => ({
        wizardState: { ...state.wizardState, currentStep: step }
      })),

      setWizardScanResults: (results) => set((state) => ({
        wizardState: { ...state.wizardState, scanResults: results }
      })),

      setWizardProposal: (proposal) => set((state) => ({
        wizardState: { ...state.wizardState, proposal }
      })),

      setWizardSelectedPools: (pools) => set((state) => ({
        wizardState: { ...state.wizardState, selectedPools: pools }
      })),

      setWizardScanning: (scanning) => set((state) => ({
        wizardState: { ...state.wizardState, isScanning: scanning }
      })),

      setWizardLaunching: (launching) => set((state) => ({
        wizardState: { ...state.wizardState, isLaunching: launching }
      }))
    }),
    {
      name: 'dyflow-agno-store',
      partialize: (state) => ({
        tradingMode: state.tradingMode,
        systemOverview: state.systemOverview
      })
    }
  )
)

// 便捷 hooks
export const useConnectionStatus = () => useAgnoStore(state => state.connectionStatus)
export const useSystemOverview = () => useAgnoStore(state => state.systemOverview)
export const useTradingMode = () => useAgnoStore(state => state.tradingMode)
export const useWorkflowStatus = () => useAgnoStore(state => ({
  running: state.workflowRunning,
  currentPhase: state.currentPhase
}))
export const useAgentFlow = () => useAgnoStore(state => state.agentFlow)
export const usePositions = () => useAgnoStore(state => state.positions)
export const useHotPools = () => useAgnoStore(state => state.hotPools)
export const useRiskSummary = () => useAgnoStore(state => state.riskSummary)
export const useInfraHealth = () => useAgnoStore(state => state.infraHealth)

// 計算衍生狀態
export const useSystemHealth = () => useAgnoStore(state => {
  const { infraHealth } = state
  const services = Object.values(infraHealth)
  const connectedCount = services.filter(s => s.status === 'connected').length
  const totalCount = services.length
  const healthPct = (connectedCount / totalCount) * 100
  
  let status: 'healthy' | 'warning' | 'critical'
  if (healthPct >= 80) status = 'healthy'
  else if (healthPct >= 60) status = 'warning'
  else status = 'critical'
  
  return {
    status,
    healthPct,
    connectedCount,
    totalCount
  }
})

export const useRiskStatus = () => useAgnoStore(state => {
  const { riskSummary } = state
  const { ilNet, var95, riskScore } = riskSummary
  
  // IL 風險等級
  let ilRisk: 'safe' | 'warning' | 'danger'
  if (ilNet > -5) ilRisk = 'safe'
  else if (ilNet > -8) ilRisk = 'warning'
  else ilRisk = 'danger'
  
  // VaR 風險等級
  let varRisk: 'safe' | 'warning' | 'danger'
  if (var95 < 2) varRisk = 'safe'
  else if (var95 < 4) varRisk = 'warning'
  else varRisk = 'danger'
  
  return {
    ilRisk,
    varRisk,
    overallRisk: Math.max(
      ilRisk === 'danger' ? 2 : ilRisk === 'warning' ? 1 : 0,
      varRisk === 'danger' ? 2 : varRisk === 'warning' ? 1 : 0
    ) as 0 | 1 | 2, // 0=safe, 1=warning, 2=danger
    riskScore
  }
})
