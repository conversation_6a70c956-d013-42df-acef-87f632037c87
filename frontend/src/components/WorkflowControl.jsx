import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import {
  Play,
  Square,
  RotateCcw,
  Zap,
  CheckCircle,
  AlertTriangle,
  Clock,
  Loader,
  Bot,
  Brain,
  Pause
} from 'lucide-react'

const WorkflowControl = ({ onWorkflowStart, onWorkflowReset, workflowStatus }) => {
  const [isStarting, setIsStarting] = useState(false)
  const [workflowStep, setWorkflowStep] = useState('idle') // idle, scanning, configuring, executing, monitoring
  const [availablePools, setAvailablePools] = useState([])
  const [recommendedPools, setRecommendedPools] = useState([])
  const [selectedPools, setSelectedPools] = useState([])
  const [currentAgent, setCurrentAgent] = useState(null)

  const startWorkflow = async () => {
    setIsStarting(true)
    setWorkflowStep('scanning')
    setCurrentAgent('MarketIntelAgent')

    try {
      console.log('🚀 啟動 LP 投資工作流程...')

      // Step 1: MarketIntelAgent 掃描池子
      console.log('📊 MarketIntelAgent 正在掃描可用池子...')
      const poolsResponse = await fetch('http://localhost:8001/api/pools')
      const poolsData = await poolsResponse.json()
      setAvailablePools(poolsData.pools || [])

      // 模擬掃描時間
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Step 2: PortfolioManagerAgent 分析推薦
      setWorkflowStep('configuring')
      setCurrentAgent('PortfolioManagerAgent')
      console.log('🎯 PortfolioManagerAgent 正在分析投資組合配置...')

      // 篩選高收益、低風險的池子
      const recommended = (poolsData.pools || []).filter(pool =>
        pool.apr > 100 && pool.tvl_usd > 5000000
      )
      setRecommendedPools(recommended)

      // 模擬分析時間
      await new Promise(resolve => setTimeout(resolve, 1500))

      console.log('✅ 工作流程準備完成，等待用戶確認投資配置')

    } catch (error) {
      console.error('❌ 工作流程啟動失敗:', error)
      setWorkflowStep('idle')
      setCurrentAgent(null)
    } finally {
      setIsStarting(false)
    }
  }

  const executeInvestment = async () => {
    setWorkflowStep('executing')
    setCurrentAgent('ExecutionAgent')

    try {
      console.log('💰 ExecutionAgent 正在執行 LP 投資...')
      console.log('選中的池子:', selectedPools)

      // 模擬執行投資
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Step 3: 進入風控監控
      setWorkflowStep('monitoring')
      setCurrentAgent('RiskSentinelAgent')
      console.log('🛡️ RiskSentinelAgent 開始風控監控...')

      console.log('✅ LP 投資執行完成，進入持續監控模式')

    } catch (error) {
      console.error('❌ 投資執行失敗:', error)
      setWorkflowStep('configuring')
    }
  }

  const resetWorkflow = () => {
    setWorkflowStep('idle')
    setCurrentAgent(null)
    setAvailablePools([])
    setRecommendedPools([])
    setSelectedPools([])
  }

  const handlePoolSelection = (pool, selected) => {
    if (selected) {
      setSelectedPools([...selectedPools, pool])
    } else {
      setSelectedPools(selectedPools.filter(p => p.id !== pool.id))
    }
  }

  const getWorkflowStatusBadge = () => {
    switch (workflowStep) {
      case 'idle':
        return (
          <Badge variant="outline">
            <Square className="h-3 w-3 mr-1" />
            等待啟動
          </Badge>
        )
      case 'scanning':
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <Loader className="h-3 w-3 mr-1 animate-spin" />
            掃描池子中
          </Badge>
        )
      case 'configuring':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Settings className="h-3 w-3 mr-1" />
            等待配置確認
          </Badge>
        )
      case 'executing':
        return (
          <Badge className="bg-purple-100 text-purple-800">
            <Loader className="h-3 w-3 mr-1 animate-spin" />
            執行投資中
          </Badge>
        )
      case 'monitoring':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            風控監控中
          </Badge>
        )
      default:
        return <Badge variant="outline">未知狀態</Badge>
    }
  }

  const getCurrentAgentInfo = () => {
    switch (workflowStep) {
      case 'scanning':
        return { name: 'MarketIntelAgent', action: '正在掃描可用的 LP 池子...' }
      case 'configuring':
        return { name: 'PortfolioManagerAgent', action: '分析投資組合配置，等待用戶確認' }
      case 'executing':
        return { name: 'ExecutionAgent', action: '正在執行 LP 投資交易...' }
      case 'monitoring':
        return { name: 'RiskSentinelAgent', action: '持續監控風險指標和 IL 變化' }
      default:
        return { name: '無', action: '點擊啟動開始 LP 投資流程' }
    }
  }

  const getAgentSummary = () => {
    if (!workflowStatus || !workflowStatus.agents) {
      return { total: 0, active: 0 }
    }

    // 處理新的 Agno API 數據結構 (agents 是數組)
    const agents = Array.isArray(workflowStatus.agents)
      ? workflowStatus.agents
      : Object.values(workflowStatus.agents)

    return {
      total: agents.length,
      active: agents.filter(agent =>
        agent.status === 'active' || agent.status === 'initialized'
      ).length
    }
  }

  const agentSummary = getAgentSummary()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-purple-500" />
          <span>AI Agent 工作流程</span>
        </CardTitle>
        <CardDescription>
          啟動 Agents 開始 LP 投資流程
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 狀態概覽 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {workflowStatus?.completed_phases || 0}/{workflowStatus?.total_phases || 9}
            </div>
            <div className="text-sm text-gray-500">系統初始化</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {agentSummary.active}/{agentSummary.total}
            </div>
            <div className="text-sm text-gray-500">活躍 Agent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              24/7
            </div>
            <div className="text-sm text-gray-500">自動運行</div>
          </div>
        </div>

        {/* 狀態標籤 */}
        <div className="flex justify-center">
          {getAgentStatusBadge()}
        </div>

        {/* 進度條 */}
        {workflowStatus && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>整體進度</span>
              <span>{(workflowStatus.progress_percentage || 0).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${workflowStatus.progress_percentage || 0}%` }}
              />
            </div>
          </div>
        )}

        {/* 系統運行狀態 */}
        <div className="flex justify-center">
          <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
            <Bot className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-800 font-medium">
              24/7 自動化系統運行中
            </span>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          </div>
        </div>

        {/* Agent 狀態摘要 */}
        {workflowStatus && workflowStatus.agents && workflowStatus.agents.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">活躍 Agent ({workflowStatus.agents.length} 個)</h4>
            <div className="grid grid-cols-2 gap-2">
              {workflowStatus.agents.map((agent, index) => (
                <div key={agent.name || index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                  <div className={`w-2 h-2 rounded-full ${
                    agent.status === 'active' || agent.status === 'initialized' ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span className="text-xs font-medium">{agent.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 最後更新時間 */}
        {workflowStatus && workflowStatus.last_updated && (
          <div className="text-xs text-gray-500 text-center">
            最後更新: {new Date(workflowStatus.last_updated).toLocaleTimeString()}
          </div>
        )}

        {/* 系統信息 */}
        {workflowStatus && workflowStatus.is_running && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <AlertTriangle className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              系統正在初始化中，即將進入自動運行模式
            </span>
          </motion.div>
        )}
      </CardContent>
    </Card>
  )
}

export default WorkflowControl
