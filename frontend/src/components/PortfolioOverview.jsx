import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Progress } from './ui/progress'
import { 
  PieChart, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  AlertTriangle,
  Target,
  Clock,
  Zap
} from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'

const PortfolioOverview = ({ portfolioData, positions }) => {
  const [timeframe, setTimeframe] = useState('24h')

  // 計算投資組合總體指標
  const calculatePortfolioMetrics = () => {
    if (!positions || positions.length === 0) {
      return {
        totalValue: 0,
        totalPnL: 0,
        totalIL: 0,
        avgAPR: 0,
        fees24h: 0,
        riskScore: 0
      }
    }

    const totalValue = positions.reduce((sum, pos) => sum + (pos.liquidity_usd || 0), 0)
    const totalPnL = positions.reduce((sum, pos) => sum + (pos.pnl_usd || 0), 0)
    const totalIL = positions.reduce((sum, pos) => sum + (pos.il_usd || 0), 0)
    const avgAPR = positions.reduce((sum, pos) => sum + (pos.apr || 0), 0) / positions.length
    const fees24h = positions.reduce((sum, pos) => sum + (pos.fees_24h_usd || 0), 0)
    
    // 風險評分 (基於 IL 和波動率)
    const riskScore = Math.min(100, Math.max(0, 100 - Math.abs(totalIL / totalValue * 100) * 10))

    return {
      totalValue,
      totalPnL,
      totalIL,
      avgAPR,
      fees24h,
      riskScore,
      pnlPercentage: totalValue > 0 ? (totalPnL / totalValue) * 100 : 0,
      ilPercentage: totalValue > 0 ? (totalIL / totalValue) * 100 : 0
    }
  }

  const metrics = calculatePortfolioMetrics()

  // 策略分佈
  const getStrategyDistribution = () => {
    if (!positions || positions.length === 0) return []
    
    const strategies = {}
    positions.forEach(pos => {
      const strategy = pos.strategy || 'UNKNOWN'
      if (!strategies[strategy]) {
        strategies[strategy] = { count: 0, value: 0 }
      }
      strategies[strategy].count += 1
      strategies[strategy].value += pos.liquidity_usd || 0
    })

    return Object.entries(strategies).map(([name, data]) => ({
      name,
      count: data.count,
      value: data.value,
      percentage: (data.value / metrics.totalValue) * 100
    }))
  }

  const strategyDistribution = getStrategyDistribution()

  // 鏈分佈
  const getChainDistribution = () => {
    if (!positions || positions.length === 0) return []
    
    const chains = {}
    positions.forEach(pos => {
      const chain = pos.chain || 'unknown'
      if (!chains[chain]) {
        chains[chain] = { count: 0, value: 0 }
      }
      chains[chain].count += 1
      chains[chain].value += pos.liquidity_usd || 0
    })

    return Object.entries(chains).map(([name, data]) => ({
      name,
      count: data.count,
      value: data.value,
      percentage: (data.value / metrics.totalValue) * 100
    }))
  }

  const chainDistribution = getChainDistribution()

  const getRiskLevel = (score) => {
    if (score >= 80) return { level: 'LOW', color: 'text-green-600', bg: 'bg-green-100' }
    if (score >= 60) return { level: 'MEDIUM', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    return { level: 'HIGH', color: 'text-red-600', bg: 'bg-red-100' }
  }

  const riskLevel = getRiskLevel(metrics.riskScore)

  return (
    <div className="space-y-6">
      {/* Portfolio Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Portfolio Value */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">總投資組合價值</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                {positions?.length || 0} 個活躍持倉
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* 24h PnL */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">24h 收益</CardTitle>
              {metrics.pnlPercentage >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${metrics.pnlPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics.pnlPercentage >= 0 ? '+' : ''}{formatPercentage(metrics.pnlPercentage)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(metrics.totalPnL)}
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Impermanent Loss */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">總體 IL</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${metrics.ilPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatPercentage(metrics.ilPercentage)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(metrics.totalIL)}
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Average APR */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均 APR</CardTitle>
              <Target className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {formatPercentage(metrics.avgAPR)}
              </div>
              <p className="text-xs text-muted-foreground">
                24h 手續費: {formatCurrency(metrics.fees24h)}
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Risk Assessment and Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Risk Assessment */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>風險評估</span>
            </CardTitle>
            <CardDescription>基於 IL 和波動率的風險評分</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">風險評分</span>
              <Badge className={`${riskLevel.bg} ${riskLevel.color}`}>
                {riskLevel.level}
              </Badge>
            </div>
            <Progress value={metrics.riskScore} className="w-full" />
            <div className="text-2xl font-bold">{metrics.riskScore.toFixed(1)}/100</div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>IL 風險:</span>
                <span className={metrics.ilPercentage < -5 ? 'text-red-600' : 'text-green-600'}>
                  {metrics.ilPercentage < -5 ? '高' : '低'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>流動性風險:</span>
                <span className="text-yellow-600">中等</span>
              </div>
              <div className="flex justify-between">
                <span>市場風險:</span>
                <span className="text-green-600">低</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Strategy Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-5 w-5 text-blue-500" />
              <span>策略分佈</span>
            </CardTitle>
            <CardDescription>投資組合策略配置</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {strategyDistribution.map((strategy, index) => (
                <div key={strategy.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ 
                        backgroundColor: [
                          '#3B82F6', '#10B981', '#F59E0B', '#EF4444'
                        ][index % 4] 
                      }}
                    />
                    <span className="text-sm font-medium">{strategy.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold">
                      {formatPercentage(strategy.percentage)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(strategy.value)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chain Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-green-500" />
            <span>鏈分佈</span>
          </CardTitle>
          <CardDescription>跨鏈投資組合分配</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {chainDistribution.map((chain, index) => (
              <div key={chain.name} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant={chain.name === 'bsc' ? 'secondary' : 'default'}>
                    {chain.name === 'bsc' ? 'BSC' : 'Solana'}
                  </Badge>
                  <span className="text-sm font-semibold">
                    {formatPercentage(chain.percentage)}
                  </span>
                </div>
                <div className="text-lg font-bold">{formatCurrency(chain.value)}</div>
                <div className="text-sm text-gray-500">{chain.count} 個持倉</div>
                <Progress value={chain.percentage} className="mt-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PortfolioOverview
