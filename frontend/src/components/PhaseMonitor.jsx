import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { <PERSON><PERSON> } from './ui/button'
import {
  CheckCircle,
  XCircle,
  Loader,
  Clock,
  Settings,
  Shield,
  Globe,
  Wallet,
  BarChart3,
  Target,
  Zap,
  AlertTriangle,
  Play,
  Square,
  RefreshCw
} from 'lucide-react'

const PhaseMonitor = () => {
  // 狀態管理
  const [systemStatus, setSystemStatus] = useState(null)
  const [agents, setAgents] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [workflowRunning, setWorkflowRunning] = useState(false)

  // API 基礎 URL - 連接到新的 Agno API
  const API_BASE = 'http://localhost:8001'

  // 獲取系統狀態
  const fetchSystemStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/system/status`)
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      setSystemStatus(data)
      setWorkflowRunning(data.overall_status === 'running')
    } catch (err) {
      console.error('獲取系統狀態失敗:', err)
      setError(err.message)
    }
  }

  // 獲取 Agents 狀態
  const fetchAgents = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/agents`)
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      setAgents(data.agents || [])
    } catch (err) {
      console.error('獲取 Agents 狀態失敗:', err)
      setError(err.message)
    }
  }

  // 啟動 Workflow
  const startWorkflow = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/workflow/start`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      console.log('Workflow 啟動:', data)
      setWorkflowRunning(true)
      // 立即刷新狀態
      await fetchSystemStatus()
    } catch (err) {
      console.error('啟動 Workflow 失敗:', err)
      setError(err.message)
    }
  }

  // 停止 Workflow
  const stopWorkflow = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/workflow/stop`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      console.log('Workflow 停止:', data)
      setWorkflowRunning(false)
      await fetchSystemStatus()
    } catch (err) {
      console.error('停止 Workflow 失敗:', err)
      setError(err.message)
    }
  }

  // 初始化和定時刷新
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchSystemStatus(), fetchAgents()])
      setLoading(false)
    }

    loadData()

    // 每 5 秒刷新一次
    const interval = setInterval(loadData, 5000)
    return () => clearInterval(interval)
  }, [])

  // 如果正在加載
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2">載入 DyFlow v3.3 系統狀態...</span>
      </div>
    )
  }

  // 如果有錯誤
  if (error) {
    return (
      <div className="text-center p-8">
        <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-red-700 mb-2">連接失敗</h3>
        <p className="text-red-600 mb-4">無法連接到 DyFlow Agno API: {error}</p>
        <Button onClick={() => window.location.reload()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重新載入
        </Button>
      </div>
    )
  }
  // DyFlow v3.3 階段定義 (移除 NATS 引用，使用 Agno Framework)
  const phases = [
    {
      id: 0,
      name: 'Phase 0: 系統初始化',
      description: 'SupervisorAgent - 讀取配置、初始化 Agno Framework',
      agent: 'SupervisorAgent',
      icon: Settings,
      color: 'blue'
    },
    {
      id: 1,
      name: 'Phase 1: 健康檢查',
      description: 'HealthGuardAgent - RPC/Subgraph/DB/UI ≥ 90% 健康',
      agent: 'HealthGuardAgent',
      icon: Shield,
      color: 'green'
    },
    {
      id: 2,
      name: 'Phase 2: UI 啟動',
      description: 'WebUI 啟動 - http://localhost:3000 200 OK',
      agent: 'WebUI',
      icon: Globe,
      color: 'purple'
    },
    {
      id: 3,
      name: 'Phase 3: 錢包測試',
      description: 'WalletProbe - MPC 簽名 & nonce 測試通過',
      agent: 'WalletProbe',
      icon: Wallet,
      color: 'yellow'
    },
    {
      id: 4,
      name: 'Phase 4: 市場情報',
      description: 'MarketIntelAgent - 掃描 BSC/Solana 池子，Agno 通訊',
      agent: 'MarketIntelAgent',
      icon: BarChart3,
      color: 'indigo'
    },
    {
      id: 5,
      name: 'Phase 5: 投資組合',
      description: 'PortfolioManagerAgent - NAV ≥ 0 且資金鎖可寫入',
      agent: 'PortfolioManagerAgent',
      icon: Target,
      color: 'pink'
    },
    {
      id: 6,
      name: 'Phase 6: 策略生成',
      description: 'StrategyAgent - 產生 LPPlan.approved，Agno 協調',
      agent: 'StrategyAgent',
      icon: Zap,
      color: 'orange'
    },
    {
      id: 7,
      name: 'Phase 7: 交易執行',
      description: 'ExecutionAgent - Tx ≥ 1 筆成功廣播',
      agent: 'ExecutionAgent',
      icon: CheckCircle,
      color: 'teal'
    },
    {
      id: 8,
      name: 'Phase 8: 風控監控',
      description: 'RiskSentinelAgent - IL_net、VaR 監控，Agno 通訊',
      agent: 'RiskSentinelAgent',
      icon: AlertTriangle,
      color: 'red'
    }
  ]

  const getPhaseStatus = (phaseId) => {
    // 使用真實的 Agno API 數據
    if (!systemStatus || !systemStatus.phases) {
      return { status: 'pending', started_at: null, completed_at: null, error: null }
    }

    const phase = systemStatus.phases.find(p => p.phase_id === phaseId)
    if (!phase) {
      return { status: 'pending', started_at: null, completed_at: null, error: null }
    }

    return {
      status: phase.status,
      started_at: phase.started_at,
      completed_at: phase.completed_at,
      error: null,
      duration_seconds: phase.duration_seconds,
      agent_name: phase.agent_name
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'running':
        return <Loader className="h-5 w-5 text-blue-500 animate-spin" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">運行中</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">失敗</Badge>
      default:
        return <Badge variant="outline">等待中</Badge>
    }
  }

  const getColorClasses = (color, status) => {
    const baseColors = {
      blue: 'border-blue-200 bg-blue-50',
      green: 'border-green-200 bg-green-50',
      purple: 'border-purple-200 bg-purple-50',
      yellow: 'border-yellow-200 bg-yellow-50',
      indigo: 'border-indigo-200 bg-indigo-50',
      pink: 'border-pink-200 bg-pink-50',
      orange: 'border-orange-200 bg-orange-50',
      teal: 'border-teal-200 bg-teal-50',
      red: 'border-red-200 bg-red-50'
    }

    if (status === 'completed') {
      return 'border-green-300 bg-green-100'
    } else if (status === 'running') {
      return 'border-blue-300 bg-blue-100'
    } else if (status === 'failed') {
      return 'border-red-300 bg-red-100'
    }

    return baseColors[color] || 'border-gray-200 bg-gray-50'
  }

  const completedPhases = phases.filter(phase => {
    const status = getPhaseStatus(phase.id)
    return status.status === 'completed'
  }).length

  const progressPercentage = systemStatus ? systemStatus.progress_percentage : 0
  const currentPhase = systemStatus ? systemStatus.current_phase : 0

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-blue-500" />
              <span>DyFlow v3.3 系統狀態監控</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 px-2 py-1 bg-green-50 border border-green-200 rounded">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-xs text-green-800 font-medium">自動運行中</span>
              </div>
            </div>
          </CardTitle>
          <CardDescription>
            系統狀態: {systemStatus?.overall_status || 'unknown'} - 當前階段: Phase {currentPhase}
            {agents.length > 0 && ` - ${agents.length} 個 Agents 活躍`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">整體進度</span>
              <span className="text-sm text-gray-500">
                {completedPhases}/{phases.length} 階段完成
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
            <div className="text-lg font-semibold">
              {progressPercentage.toFixed(1)}% 完成
            </div>

            {/* Agents 狀態 */}
            {agents.length > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium mb-2">活躍 Agents:</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {agents.map(agent => (
                    <div key={agent.name} className="text-xs">
                      <span className="font-medium">{agent.name}</span>
                      <div className="text-gray-500">Phase {agent.phase} - {agent.status}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Phase Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {phases.map((phase, index) => {
          const status = getPhaseStatus(phase.id)
          const IconComponent = phase.icon
          
          return (
            <motion.div
              key={phase.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${getColorClasses(phase.color, status.status)} transition-all duration-300`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <IconComponent className={`h-5 w-5 text-${phase.color}-600`} />
                      <span className="font-medium text-sm">{phase.name}</span>
                    </div>
                    {getStatusIcon(status.status)}
                  </div>
                  {getStatusBadge(status.status)}
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-xs text-gray-600 mb-3">
                    {phase.description}
                  </p>

                  {/* Agent 信息 */}
                  <div className="text-xs text-blue-600 mb-2">
                    負責 Agent: {status.agent_name || phase.agent}
                  </div>

                  {status.started_at && (
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>開始: {new Date(status.started_at).toLocaleTimeString()}</div>
                      {status.completed_at && (
                        <div>完成: {new Date(status.completed_at).toLocaleTimeString()}</div>
                      )}
                      {status.duration_seconds && (
                        <div>耗時: {status.duration_seconds.toFixed(1)}s</div>
                      )}
                      {status.error && (
                        <div className="text-red-600">錯誤: {status.error}</div>
                      )}
                    </div>
                  )}

                  {phase.id === currentPhase && status.status === 'running' && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <Loader className="h-3 w-3 animate-spin text-blue-500" />
                        <span className="text-xs text-blue-600">正在執行...</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Phase Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>DyFlow v3.3 執行時間軸</CardTitle>
          <CardDescription>
            各階段執行記錄 - 使用 Agno Framework 替代 NATS 消息總線
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {phases.map((phase, index) => {
              const status = getPhaseStatus(phase.id)

              return (
                <div key={phase.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(status.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">{phase.name}</span>
                        <div className="text-xs text-gray-500">
                          {status.agent_name || phase.agent}
                        </div>
                      </div>
                      {status.started_at && (
                        <div className="text-xs text-gray-500 text-right">
                          <div>{new Date(status.started_at).toLocaleTimeString()}</div>
                          {status.duration_seconds && (
                            <div>{status.duration_seconds.toFixed(1)}s</div>
                          )}
                        </div>
                      )}
                    </div>
                    {status.error && (
                      <div className="text-xs text-red-600 mt-1">
                        {status.error}
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusBadge(status.status)}
                  </div>
                </div>
              )
            })}
          </div>

          {/* API 連接狀態 */}
          <div className="mt-4 pt-4 border-t">
            <div className="text-xs text-gray-500">
              API 連接: {API_BASE}
              {systemStatus && (
                <span className="text-green-600 ml-2">✅ 已連接</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PhaseMonitor
