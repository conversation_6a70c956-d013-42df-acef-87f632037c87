/**
 * Agent 日誌組件
 * 顯示 Agent 間通信和系統日誌
 */

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  ScrollText, 
  Filter,
  Download,
  Pause,
  Play,
  Circle,
  ArrowRight,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface LogEntry {
  id: string
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'success'
  source: string
  target?: string
  message: string
  details?: any
}

export const AgentLogs: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [isPaused, setIsPaused] = useState(false)
  const [filter, setFilter] = useState<'all' | 'info' | 'warning' | 'error' | 'success'>('all')
  const logsEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 初始化一些示例日誌
    const initialLogs: LogEntry[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 300000),
        level: 'info',
        source: 'OnboardingAgent',
        message: '系統啟動完成，等待用戶指令',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 240000),
        level: 'info',
        source: 'MarketIntelAgent',
        target: 'StrategyAgent',
        message: '發現高 APR 池子: SOL/USDC (185.2%)',
        details: { pool: 'SOL/USDC', apr: 185.2, tvl: 12300000 }
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 180000),
        level: 'success',
        source: 'StrategyAgent',
        target: 'ExecutionAgent',
        message: '生成 LP 策略計劃',
        details: { strategy: 'SPOT_BALANCED', amount: 5000 }
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 120000),
        level: 'warning',
        source: 'RiskSentinelAgent',
        message: 'BNB/USDT 池子波動率上升至 28%',
        details: { pool: 'BNB/USDT', volatility: 0.28 }
      },
      {
        id: '5',
        timestamp: new Date(Date.now() - 60000),
        level: 'info',
        source: 'ExecutionAgent',
        message: '等待用戶確認 LP 操作',
      }
    ]
    
    setLogs(initialLogs)

    // 模擬實時日誌
    if (!isPaused) {
      const interval = setInterval(() => {
        const newLog = generateRandomLog()
        setLogs(prev => [...prev.slice(-49), newLog]) // 保持最新 50 條
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [isPaused])

  useEffect(() => {
    if (!isPaused) {
      scrollToBottom()
    }
  }, [logs, isPaused])

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const generateRandomLog = (): LogEntry => {
    const agents = ['MarketIntelAgent', 'StrategyAgent', 'ExecutionAgent', 'RiskSentinelAgent', 'OnboardingAgent']
    const levels: LogEntry['level'][] = ['info', 'warning', 'success']
    const messages = [
      '池子數據更新完成',
      '風險評估通過',
      '檢測到新的高收益機會',
      '持倉狀態正常',
      '執行策略調整',
      '收到用戶指令',
      '完成風險檢查',
      '更新市場數據'
    ]

    const source = agents[Math.floor(Math.random() * agents.length)]
    const target = Math.random() > 0.5 ? agents[Math.floor(Math.random() * agents.length)] : undefined
    
    return {
      id: Date.now().toString(),
      timestamp: new Date(),
      level: levels[Math.floor(Math.random() * levels.length)],
      source,
      target: target !== source ? target : undefined,
      message: messages[Math.floor(Math.random() * messages.length)]
    }
  }

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'info':
        return <Info className="w-3 h-3 text-blue-500" />
      case 'warning':
        return <AlertTriangle className="w-3 h-3 text-amber-500" />
      case 'error':
        return <XCircle className="w-3 h-3 text-rose-500" />
      case 'success':
        return <CheckCircle className="w-3 h-3 text-emerald-500" />
    }
  }

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'info':
        return 'bg-blue-50 border-blue-200'
      case 'warning':
        return 'bg-amber-50 border-amber-200'
      case 'error':
        return 'bg-rose-50 border-rose-200'
      case 'success':
        return 'bg-emerald-50 border-emerald-200'
    }
  }

  const getAgentColor = (agent: string) => {
    const colors: Record<string, string> = {
      'OnboardingAgent': 'bg-purple-100 text-purple-800',
      'MarketIntelAgent': 'bg-blue-100 text-blue-800',
      'StrategyAgent': 'bg-emerald-100 text-emerald-800',
      'ExecutionAgent': 'bg-orange-100 text-orange-800',
      'RiskSentinelAgent': 'bg-rose-100 text-rose-800'
    }
    return colors[agent] || 'bg-gray-100 text-gray-800'
  }

  const filteredLogs = filter === 'all' 
    ? logs 
    : logs.filter(log => log.level === filter)

  const exportLogs = () => {
    const logText = logs.map(log => 
      `[${log.timestamp.toISOString()}] ${log.level.toUpperCase()} ${log.source}${log.target ? ` → ${log.target}` : ''}: ${log.message}`
    ).join('\n')
    
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `dyflow-logs-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ScrollText className="w-4 h-4 text-gray-600" />
            <CardTitle className="text-sm font-medium">Agent 通信日誌</CardTitle>
            <Badge variant="outline" className="text-xs">
              {filteredLogs.length} 條
            </Badge>
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsPaused(!isPaused)}
              className="text-xs px-2"
            >
              {isPaused ? <Play className="w-3 h-3" /> : <Pause className="w-3 h-3" />}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={exportLogs}
              className="text-xs px-2"
            >
              <Download className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        {/* 過濾器 */}
        <div className="flex items-center space-x-1 mt-2">
          {(['all', 'info', 'warning', 'error', 'success'] as const).map((level) => (
            <Button
              key={level}
              size="sm"
              variant={filter === level ? 'default' : 'outline'}
              onClick={() => setFilter(level)}
              className="text-xs px-2 py-1"
            >
              {level === 'all' ? '全部' : level}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto p-0">
        <div className="space-y-1 p-2">
          {filteredLogs.map((log) => (
            <div
              key={log.id}
              className={`border rounded-lg p-2 text-xs ${getLevelColor(log.level)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2 min-w-0 flex-1">
                  {getLevelIcon(log.level)}
                  
                  <div className="flex items-center space-x-1 min-w-0">
                    <Badge className={`text-xs px-1 py-0 ${getAgentColor(log.source)}`} variant="secondary">
                      {log.source.replace('Agent', '')}
                    </Badge>
                    
                    {log.target && (
                      <>
                        <ArrowRight className="w-3 h-3 text-gray-400" />
                        <Badge className={`text-xs px-1 py-0 ${getAgentColor(log.target)}`} variant="secondary">
                          {log.target.replace('Agent', '')}
                        </Badge>
                      </>
                    )}
                  </div>
                </div>
                
                <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                  {log.timestamp.toLocaleTimeString()}
                </span>
              </div>
              
              <div className="mt-1 text-gray-700">
                {log.message}
              </div>
              
              {log.details && (
                <div className="mt-1 text-xs text-gray-600 bg-white bg-opacity-50 rounded px-2 py-1">
                  {JSON.stringify(log.details, null, 2)}
                </div>
              )}
            </div>
          ))}
          
          {filteredLogs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <ScrollText className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">暫無 {filter === 'all' ? '' : filter} 日誌</p>
            </div>
          )}
          
          <div ref={logsEndRef} />
        </div>
      </CardContent>
      
      {/* 底部狀態 */}
      <div className="border-t px-3 py-2 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            <Circle className={`w-2 h-2 ${isPaused ? 'text-gray-400' : 'text-emerald-500 animate-pulse'}`} />
            <span>{isPaused ? '已暫停' : '實時更新'}</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span>總計: {logs.length}</span>
            <span>顯示: {filteredLogs.length}</span>
          </div>
        </div>
      </div>
    </Card>
  )
}
