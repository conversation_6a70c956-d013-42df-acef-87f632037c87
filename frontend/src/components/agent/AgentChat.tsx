/**
 * DyFlow Agent 對話組件
 * 用戶與 DyFlow Agent 的直接對話界面
 */

import React, { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { 
  Send, 
  Bot, 
  User, 
  Mic,
  MicOff,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

interface ChatMessage {
  id: string
  type: 'user' | 'agent'
  content: string
  timestamp: Date
  status?: 'sending' | 'sent' | 'error'
  actions?: ChatAction[]
}

interface ChatAction {
  id: string
  label: string
  type: 'primary' | 'secondary' | 'danger'
  action: () => void
}

export const AgentChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'agent',
      content: '👋 您好！我是 DyFlow v3.4 智能助手。我可以幫您：\n\n• 分析最佳 LP 機會\n• 管理風險和持倉\n• 執行交易策略\n• 回答系統問題\n\n請告訴我您需要什麼幫助？',
      timestamp: new Date(),
      actions: [
        {
          id: 'scan_pools',
          label: '掃描最佳池子',
          type: 'primary',
          action: () => handleQuickAction('scan_pools')
        },
        {
          id: 'check_positions',
          label: '檢查我的持倉',
          type: 'secondary',
          action: () => handleQuickAction('check_positions')
        }
      ]
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isListening, setIsListening] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      status: 'sent'
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsTyping(true)

    // 模擬 Agent 回應
    setTimeout(() => {
      const agentResponse = generateAgentResponse(inputValue)
      setMessages(prev => [...prev, agentResponse])
      setIsTyping(false)
    }, 1500)
  }

  const handleQuickAction = (actionType: string) => {
    let message = ''
    switch (actionType) {
      case 'scan_pools':
        message = '請幫我掃描當前最佳的 LP 機會'
        break
      case 'check_positions':
        message = '檢查我的 LP 持倉狀態'
        break
      default:
        message = actionType
    }

    setInputValue(message)
    handleSendMessage()
  }

  const generateAgentResponse = (userInput: string): ChatMessage => {
    const input = userInput.toLowerCase()
    let content = ''
    let actions: ChatAction[] = []

    if (input.includes('掃描') || input.includes('池子') || input.includes('機會')) {
      content = `🔍 正在掃描最佳 LP 機會...\n\n**發現高收益池子：**\n• BNB/USDT (BSC) - 125.5% APR\n• SOL/USDC (Solana) - 185.2% APR\n• ETH/USDC (BSC) - 89.2% APR\n\n**推薦策略：**\n✅ SOL/USDC 池子風險適中，APR 最高\n⚠️ 建議分散投資，降低風險\n\n需要我幫您啟動 LP 策略嗎？`
      
      actions = [
        {
          id: 'start_lp',
          label: '啟動 LP 策略',
          type: 'primary',
          action: () => handleQuickAction('啟動推薦的 LP 策略')
        },
        {
          id: 'risk_analysis',
          label: '風險分析',
          type: 'secondary',
          action: () => handleQuickAction('詳細風險分析')
        }
      ]
    } else if (input.includes('持倉') || input.includes('position')) {
      content = `📊 您的 LP 持倉狀態：\n\n**活躍持倉：** 1 個\n• BNB/USDT (BSC) - $5,000\n  - PnL: +$125.5 (+2.51%)\n  - IL: -$45.2 (-0.9%)\n  - 費用收益: $15.2\n\n**風險狀態：** ✅ 正常\n• IL 在安全範圍內\n• VaR 95%: 2.8%\n\n需要調整持倉或設置風控嗎？`
      
      actions = [
        {
          id: 'adjust_position',
          label: '調整持倉',
          type: 'primary',
          action: () => handleQuickAction('調整 LP 持倉')
        },
        {
          id: 'set_risk',
          label: '設置風控',
          type: 'secondary',
          action: () => handleQuickAction('設置風險控制')
        }
      ]
    } else if (input.includes('風險') || input.includes('risk')) {
      content = `🛡️ 風險分析報告：\n\n**當前風險等級：** 中等\n• 風險分數: 75/100\n• IL 風險: -1.9% (安全)\n• VaR 95%: 2.8% (正常)\n\n**建議措施：**\n✅ 持倉分散度良好\n⚠️ 建議設置 IL 熔斷 (-8%)\n💡 可考慮增加穩定幣對\n\n需要我幫您設置自動風控嗎？`
      
      actions = [
        {
          id: 'auto_risk',
          label: '啟動自動風控',
          type: 'primary',
          action: () => handleQuickAction('啟動自動風險控制')
        }
      ]
    } else if (input.includes('啟動') || input.includes('start')) {
      content = `🚀 正在啟動 LP 策略...\n\n**執行步驟：**\n1. ✅ 掃描最佳池子\n2. ✅ 風險評估\n3. 🔄 生成 LP 計劃\n4. ⏳ 等待執行確認\n\n**預計投入：** $5,000\n**目標 APR：** 150%+\n**風險等級：** 中等\n\n確認執行此策略嗎？`
      
      actions = [
        {
          id: 'confirm_strategy',
          label: '確認執行',
          type: 'primary',
          action: () => handleQuickAction('確認執行 LP 策略')
        },
        {
          id: 'modify_strategy',
          label: '修改參數',
          type: 'secondary',
          action: () => handleQuickAction('修改策略參數')
        }
      ]
    } else {
      content = `🤖 我理解您的問題。作為 DyFlow v3.4 智能助手，我可以幫您：\n\n• **池子分析** - 找到最佳 LP 機會\n• **風險管理** - 監控和控制投資風險\n• **策略執行** - 自動化 LP 操作\n• **持倉監控** - 實時跟蹤收益和損失\n\n請告訴我您具體需要什麼幫助？`
      
      actions = [
        {
          id: 'help_pools',
          label: '池子分析',
          type: 'secondary',
          action: () => handleQuickAction('幫我分析池子')
        },
        {
          id: 'help_risk',
          label: '風險管理',
          type: 'secondary',
          action: () => handleQuickAction('幫我管理風險')
        }
      ]
    }

    return {
      id: Date.now().toString(),
      type: 'agent',
      content,
      timestamp: new Date(),
      actions
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const toggleVoiceInput = () => {
    setIsListening(!isListening)
    // 這裡可以集成語音識別 API
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium">DyFlow Agent</CardTitle>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span className="text-xs text-gray-500">在線</span>
              </div>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            v3.4
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                <div
                  className={`rounded-lg px-3 py-2 text-sm ${
                    message.type === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  
                  {/* 快速操作按鈕 */}
                  {message.actions && message.actions.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {message.actions.map((action) => (
                        <Button
                          key={action.id}
                          size="sm"
                          variant={action.type === 'primary' ? 'default' : 'outline'}
                          className={`w-full text-xs ${
                            action.type === 'danger' ? 'bg-rose-500 hover:bg-rose-600' : ''
                          }`}
                          onClick={action.action}
                        >
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className={`text-xs text-gray-500 mt-1 ${
                  message.type === 'user' ? 'text-right' : 'text-left'
                }`}>
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
              
              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                message.type === 'user' ? 'order-1 mr-2 bg-blue-100' : 'order-2 ml-2 bg-gray-200'
              }`}>
                {message.type === 'user' ? (
                  <User className="w-3 h-3 text-blue-600" />
                ) : (
                  <Bot className="w-3 h-3 text-gray-600" />
                )}
              </div>
            </div>
          ))}
          
          {/* 正在輸入指示器 */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                  <Bot className="w-3 h-3 text-gray-600" />
                </div>
                <div className="bg-gray-100 rounded-lg px-3 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 輸入區域 */}
        <div className="border-t p-4">
          <div className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="輸入消息或語音指令..."
                className="pr-10"
              />
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
                onClick={toggleVoiceInput}
              >
                {isListening ? (
                  <MicOff className="w-4 h-4 text-rose-500" />
                ) : (
                  <Mic className="w-4 h-4 text-gray-400" />
                )}
              </Button>
            </div>
            <Button 
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              size="sm"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          
          {/* 快速指令 */}
          <div className="mt-2 flex flex-wrap gap-1">
            {['掃描池子', '檢查持倉', '風險分析', '關閉持倉'].map((cmd) => (
              <Button
                key={cmd}
                size="sm"
                variant="outline"
                className="text-xs px-2 py-1"
                onClick={() => {
                  setInputValue(cmd)
                  handleSendMessage()
                }}
              >
                {cmd}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
