/**
 * 真實池子監控組件
 * 顯示來自 PancakeSwap V3 和 Meteora DLMM 的真實數據
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { <PERSON><PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown,
  Eye,
  Plus,
  Clock,
  DollarSign,
  BarChart3
} from 'lucide-react'
import { realDataService, RealPoolData } from '../../services/realDataService'

export const RealPoolMonitor: React.FC = () => {
  const [bscPools, setBscPools] = useState<RealPoolData[]>([])
  const [solanaPools, setSolanaPools] = useState<RealPoolData[]>([])
  const [loading, setLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [activeTab, setActiveTab] = useState<'BSC' | 'SOL'>('BSC')

  useEffect(() => {
    loadPoolData()
    
    // 每 30 秒自動更新
    const interval = setInterval(loadPoolData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadPoolData = async () => {
    setLoading(true)
    try {
      const [bscData, solanaData] = await Promise.all([
        realDataService.getBscPools(10),
        realDataService.getSolanaPools(10)
      ])
      
      setBscPools(bscData)
      setSolanaPools(solanaData)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('Error loading pool data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(0)}`
  }

  const formatPercentage = (value: number) => `${(value * 100).toFixed(2)}%`

  const getChainColor = (chain: string) => {
    return chain === 'bsc' ? 'bg-amber-100 text-amber-800' : 'bg-purple-100 text-purple-800'
  }

  const getAPRColor = (apr: number) => {
    if (apr >= 100) return 'text-emerald-600 font-bold'
    if (apr >= 50) return 'text-emerald-600'
    if (apr >= 20) return 'text-blue-600'
    return 'text-gray-600'
  }

  const getRiskLevel = (volatility: number) => {
    if (volatility >= 0.4) return { level: '高風險', color: 'bg-rose-100 text-rose-800' }
    if (volatility >= 0.2) return { level: '中風險', color: 'bg-amber-100 text-amber-800' }
    return { level: '低風險', color: 'bg-emerald-100 text-emerald-800' }
  }

  const PoolRow: React.FC<{ pool: RealPoolData }> = ({ pool }) => {
    const risk = getRiskLevel(pool.volatility)
    
    return (
      <tr className="border-b border-gray-100 hover:bg-gray-50">
        <td className="px-4 py-3">
          <div className="flex items-center space-x-3">
            <Badge className={getChainColor(pool.chain)} variant="secondary">
              {pool.chain === 'bsc' ? 'BSC' : 'SOL'}
            </Badge>
            <div>
              <div className="font-medium text-sm">{pool.pair}</div>
              <div className="text-xs text-gray-500">{pool.protocol}</div>
            </div>
          </div>
        </td>
        <td className="px-4 py-3 text-right">
          <div className="font-medium">{formatCurrency(pool.tvlUsd)}</div>
        </td>
        <td className="px-4 py-3 text-right">
          <div className={`font-bold ${getAPRColor(pool.apr)}`}>
            {pool.apr.toFixed(1)}%
          </div>
        </td>
        <td className="px-4 py-3 text-right">
          <div className="text-sm">{formatPercentage(pool.volatility)}</div>
        </td>
        <td className="px-4 py-3 text-right">
          <div className="text-sm">{formatPercentage(pool.spread)}</div>
        </td>
        <td className="px-4 py-3 text-right">
          <div className="font-medium">{formatCurrency(pool.fees24hUsd)}</div>
        </td>
        <td className="px-4 py-3">
          <Badge className={risk.color} variant="secondary">
            {risk.level}
          </Badge>
        </td>
        <td className="px-4 py-3">
          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" className="text-xs px-2 py-1">
              <Eye className="w-3 h-3 mr-1" />
              查看
            </Button>
            <Button size="sm" className="text-xs px-2 py-1 bg-emerald-600 hover:bg-emerald-700">
              <Plus className="w-3 h-3 mr-1" />
              添加
            </Button>
          </div>
        </td>
      </tr>
    )
  }

  const currentPools = activeTab === 'BSC' ? bscPools : solanaPools
  const avgAPR = currentPools.length > 0 
    ? currentPools.reduce((sum, p) => sum + p.apr, 0) / currentPools.length 
    : 0
  const totalTVL = currentPools.reduce((sum, p) => sum + p.tvlUsd, 0)

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BarChart3 className="w-5 h-5 text-blue-500" />
            <CardTitle className="text-lg font-bold">熱門池子監控</CardTitle>
            <Badge variant="outline" className="text-xs">
              實時數據
            </Badge>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <Clock className="w-3 h-3" />
              <span>更新：{lastUpdate.toLocaleTimeString()}</span>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadPoolData}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'BSC' | 'SOL')}>
          <div className="px-6 pb-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="BSC" className="text-sm">
                <span className="mr-2">🟡</span>
                BSC ({bscPools.length})
              </TabsTrigger>
              <TabsTrigger value="SOL" className="text-sm">
                <span className="mr-2">🟣</span>
                Solana ({solanaPools.length})
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="BSC" className="mt-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">池子</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">TVL</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">APR</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">波動率</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">價差</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">24h 費用</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">風險</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {bscPools.map((pool) => (
                    <PoolRow key={pool.id} pool={pool} />
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="SOL" className="mt-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">池子</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">TVL</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">APR</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">波動率</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">價差</th>
                    <th className="px-4 py-3 text-right font-medium text-gray-700">24h 費用</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">風險</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {solanaPools.map((pool) => (
                    <PoolRow key={pool.id} pool={pool} />
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>

        {/* 底部統計 */}
        <div className="px-6 py-4 bg-gray-50 border-t">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <span className="text-gray-600">
                顯示 {currentPools.length} 個 {activeTab} 池子
              </span>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-emerald-500" />
                <span className="font-medium">平均 APR: {avgAPR.toFixed(1)}%</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-blue-500" />
              <span className="font-medium">總 TVL: {formatCurrency(totalTVL)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
