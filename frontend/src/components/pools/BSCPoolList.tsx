import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  DollarSign, 
  Percent, 
  Activity,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { useAgnoStore } from '@/store/useAgno'

interface BSCPool {
  id: string
  pair: string
  token0: any
  token1: any
  tvl_usd: number
  volume_24h: number
  fees_24h: number
  fee_apr: number
  total_apr: number
  risk_level: string
  volatility: number
  strategy_type: string
  fee_tvl_ratio: number
  last_update: string
}

const BSCPoolList: React.FC = () => {
  const { hotPools } = useAgnoStore()
  const [loading, setLoading] = useState(false)
  const [sortBy, setSortBy] = useState<'tvl' | 'apr' | 'volume' | 'fees'>('tvl')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 過濾 BSC 池子
  const bscPools = hotPools.filter(pool => pool.chain === 'bsc') as BSCPool[]

  // 排序池子
  const sortedPools = [...bscPools].sort((a, b) => {
    let aValue: number, bValue: number
    
    switch (sortBy) {
      case 'tvl':
        aValue = a.tvl_usd || 0
        bValue = b.tvl_usd || 0
        break
      case 'apr':
        aValue = a.total_apr || 0
        bValue = b.total_apr || 0
        break
      case 'volume':
        aValue = a.volume_24h || 0
        bValue = b.volume_24h || 0
        break
      case 'fees':
        aValue = a.fees_24h || 0
        bValue = b.fees_24h || 0
        break
      default:
        aValue = a.tvl_usd || 0
        bValue = b.tvl_usd || 0
    }
    
    return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
  })

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1e9) return `$${(num / 1e9).toFixed(decimals)}B`
    if (num >= 1e6) return `$${(num / 1e6).toFixed(decimals)}M`
    if (num >= 1e3) return `$${(num / 1e3).toFixed(decimals)}K`
    return `$${num.toFixed(decimals)}`
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return 'bg-blue-100 text-blue-800'
      case 'CURVE_BALANCED': return 'bg-green-100 text-green-800'
      case 'BID_ASK_BALANCED': return 'bg-purple-100 text-purple-800'
      case 'SPOT_IMBALANCED_DAMM': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const refreshPools = async () => {
    setLoading(true)
    // 這裡可以觸發重新獲取數據
    setTimeout(() => setLoading(false), 1000)
  }

  return (
    <div className="space-y-4">
      {/* 控制欄 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold">BSC 池子</h3>
          <Badge variant="outline">{bscPools.length} 個池子</Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* 排序按鈕 */}
          <div className="flex items-center space-x-1">
            <Button
              variant={sortBy === 'tvl' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSort('tvl')}
            >
              TVL
            </Button>
            <Button
              variant={sortBy === 'apr' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSort('apr')}
            >
              APR
            </Button>
            <Button
              variant={sortBy === 'volume' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSort('volume')}
            >
              交易量
            </Button>
            <Button
              variant={sortBy === 'fees' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSort('fees')}
            >
              手續費
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refreshPools}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* 池子列表 */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {sortedPools.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暫無 BSC 池子數據</p>
            <p className="text-sm">MarketIntelAgent 正在掃描中...</p>
          </div>
        ) : (
          sortedPools.map((pool) => (
            <Card key={pool.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  {/* 左側：池子信息 */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-lg">{pool.pair}</h4>
                      <Badge className="bg-yellow-100 text-yellow-800">BSC</Badge>
                      <Badge 
                        variant="outline" 
                        className={getRiskColor(pool.risk_level)}
                      >
                        {pool.risk_level === 'low' ? '低風險' : 
                         pool.risk_level === 'medium' ? '中風險' : '高風險'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">TVL:</span>
                        <span className="font-medium">{formatNumber(pool.tvl_usd)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-gray-600">APR:</span>
                        <span className="font-medium text-green-600">
                          {pool.total_apr?.toFixed(1)}%
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Activity className="h-4 w-4 text-blue-500" />
                        <span className="text-gray-600">24h 交易量:</span>
                        <span className="font-medium">{formatNumber(pool.volume_24h)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Percent className="h-4 w-4 text-purple-500" />
                        <span className="text-gray-600">24h 手續費:</span>
                        <span className="font-medium">{formatNumber(pool.fees_24h)}</span>
                      </div>
                    </div>
                    
                    <div className="mt-3 flex items-center space-x-2">
                      <Badge 
                        variant="outline" 
                        className={getStrategyColor(pool.strategy_type)}
                      >
                        {pool.strategy_type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        波動率: {(pool.volatility * 100).toFixed(1)}%
                      </span>
                      <span className="text-xs text-gray-500">
                        Fee/TVL: {pool.fee_tvl_ratio?.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                  
                  {/* 右側：操作按鈕 */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <Button size="sm" className="whitespace-nowrap">
                      建立 LP
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(`https://pancakeswap.finance/v3/pools/${pool.id}`, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
      
      {/* 底部統計 */}
      {bscPools.length > 0 && (
        <div className="text-xs text-gray-500 text-center pt-2 border-t">
          總 TVL: {formatNumber(bscPools.reduce((sum, pool) => sum + (pool.tvl_usd || 0), 0))} · 
          平均 APR: {(bscPools.reduce((sum, pool) => sum + (pool.total_apr || 0), 0) / bscPools.length).toFixed(1)}% · 
          最後更新: {new Date().toLocaleTimeString()}
        </div>
      )}
    </div>
  )
}

export default BSCPoolList
