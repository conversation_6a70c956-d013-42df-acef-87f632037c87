/**
 * Agent Timeline Card - DyFlow v3.4
 * 帶 ACK 狀態的 React 實作樣板
 * 顯示 Agent 狀態流程：灰點 idle、黃 Loader RUN、綠勾 ACK、紅叉 NACK
 */

import React from 'react'
import { useAgnoStore } from '@/store/useAgno'
import { Check, X, Loader2, Circle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface AgentFlowItem {
  name: string
  state: 'idle' | 'RUN' | 'ACK' | 'NACK'
  task?: string
  ts?: number
  phase?: number
}

const stateIcon = (state: string) => {
  switch (state) {
    case 'ACK':
      return <Check className="w-4 h-4 text-green-500" />
    case 'NACK':
      return <X className="w-4 h-4 text-red-500" />
    case 'RUN':
      return <Loader2 className="w-4 h-4 animate-spin text-amber-500" />
    case 'idle':
    default:
      return <Circle className="w-4 h-4 text-slate-400 fill-current" />
  }
}

const stateColor = (state: string) => {
  switch (state) {
    case 'ACK':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'NACK':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'RUN':
      return 'bg-amber-100 text-amber-800 border-amber-200'
    case 'idle':
    default:
      return 'bg-slate-100 text-slate-600 border-slate-200'
  }
}

const stateText = (state: string) => {
  switch (state) {
    case 'ACK':
      return '已確認'
    case 'NACK':
      return '已拒絕'
    case 'RUN':
      return '執行中'
    case 'idle':
    default:
      return '空閒中'
  }
}

export default function AgentTimeline() {
  const flow = useAgnoStore((state) => state.agentFlow) as AgentFlowItem[]
  const currentPhase = useAgnoStore((state) => state.currentPhase)
  const workflowRunning = useAgnoStore((state) => state.workflowRunning)

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Agent 工作流程</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge 
              variant={workflowRunning ? "default" : "secondary"}
              className={workflowRunning ? "bg-green-500" : ""}
            >
              {workflowRunning ? '運行中' : '已停止'}
            </Badge>
            <Badge variant="outline">
              Phase {currentPhase}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-4 pr-2 overflow-y-auto h-80">
          {flow.map((agent, index) => (
            <div 
              key={agent.name} 
              className={`flex items-start space-x-3 p-3 rounded-lg transition-all duration-200 ${
                agent.phase === currentPhase 
                  ? 'bg-blue-50 border border-blue-200' 
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}
            >
              {/* 狀態圖示 */}
              <div className="flex-shrink-0 mt-1">
                {stateIcon(agent.state)}
              </div>
              
              {/* Agent 信息 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="font-medium text-gray-900 truncate">
                    {agent.name}
                  </p>
                  <Badge 
                    className={`text-xs ${stateColor(agent.state)}`}
                    variant="outline"
                  >
                    {stateText(agent.state)}
                  </Badge>
                </div>
                
                {/* 任務描述 */}
                <p className="text-sm text-gray-600 mb-1">
                  {agent.task || '空閒中'}
                </p>
                
                {/* 時間戳和階段 */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    Phase {agent.phase || 0}
                  </span>
                  {agent.ts && (
                    <span>
                      {new Date(agent.ts * 1000).toLocaleTimeString('zh-TW', {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                      })}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 底部統計 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div className="text-xs">
              <div className="text-gray-500">空閒</div>
              <div className="font-medium text-slate-600">
                {flow.filter(a => a.state === 'idle').length}
              </div>
            </div>
            <div className="text-xs">
              <div className="text-gray-500">執行中</div>
              <div className="font-medium text-amber-600">
                {flow.filter(a => a.state === 'RUN').length}
              </div>
            </div>
            <div className="text-xs">
              <div className="text-gray-500">已確認</div>
              <div className="font-medium text-green-600">
                {flow.filter(a => a.state === 'ACK').length}
              </div>
            </div>
            <div className="text-xs">
              <div className="text-gray-500">已拒絕</div>
              <div className="font-medium text-red-600">
                {flow.filter(a => a.state === 'NACK').length}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 導出類型定義供其他組件使用
export type { AgentFlowItem }
