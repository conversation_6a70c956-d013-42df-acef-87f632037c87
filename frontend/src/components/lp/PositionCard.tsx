/**
 * LP 持倉卡片
 * 解決痛點③：LP 卡片資訊不足
 * 新增：持倉倒計時、Fee APR、ExitAsset
 */

import React from 'react'
import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  DollarSign, 
  AlertTriangle,
  ExternalLink,
  LogOut
} from 'lucide-react'
import type { PositionData } from '../../lib/wsHub'
import { emergencyExit } from '../../lib/wsHub'

interface PositionCardProps {
  position: PositionData
  onExit?: (positionId: string) => void
}

export const PositionCard: React.FC<PositionCardProps> = ({ position, onExit }) => {
  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const getChainColor = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'bg-amber-500'
      case 'solana': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getChainText = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'BSC'
      case 'solana': return 'SOL'
      default: return chain.toUpperCase()
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return 'bg-blue-500'
      case 'CURVE_BALANCED': return 'bg-green-500'
      case 'BID_ASK_BALANCED': return 'bg-purple-500'
      case 'SPOT_IMBALANCED_DAMM': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  const getStrategyText = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return '對稱流動性'
      case 'CURVE_BALANCED': return '曲線分布'
      case 'BID_ASK_BALANCED': return '買賣價差'
      case 'SPOT_IMBALANCED_DAMM': return '單邊流動性'
      default: return strategy
    }
  }

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'active':
        return 'border-emerald-200 bg-white'
      case 'exiting':
        return 'border-amber-200 bg-amber-50'
      case 'closed':
        return 'border-gray-200 bg-gray-50 opacity-75'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  const getILColor = (ilPct: number) => {
    if (ilPct > -2) return 'text-emerald-600'
    if (ilPct > -5) return 'text-amber-600'
    if (ilPct > -8) return 'text-orange-600'
    return 'text-rose-600'
  }

  const getILBarColor = (ilPct: number) => {
    if (ilPct > -2) return 'from-emerald-400 to-emerald-600'
    if (ilPct > -5) return 'from-amber-400 to-amber-600'
    if (ilPct > -8) return 'from-orange-400 to-orange-600'
    return 'from-rose-400 to-rose-600'
  }

  const getCountdownColor = (countdown: number) => {
    if (countdown > 3600) return 'text-emerald-600' // > 1h
    if (countdown > 1800) return 'text-amber-600'   // > 30m
    return 'text-rose-600' // < 30m
  }

  return (
    <Card className={`transition-all duration-200 ${getStatusStyle(position.status)}`}>
      <CardContent className="p-4">
        {/* 頭部信息 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-800">{position.pool}</h3>
            <Badge className={`${getChainColor(position.chain)} text-white text-xs`}>
              {getChainText(position.chain)}
            </Badge>
            <Badge 
              variant="outline" 
              className={`${getStrategyColor(position.strategy)} text-white text-xs`}
            >
              {getStrategyText(position.strategy)}
            </Badge>
          </div>
          
          {/* 狀態指示器 */}
          <div className="flex items-center space-x-2">
            {position.status === 'closed' && position.exitAsset && (
              <Badge variant="secondary" className="text-xs">
                已兌回 {position.exitAsset} {position.exitAmount?.toFixed(2)}
              </Badge>
            )}
            {position.status === 'exiting' && (
              <Badge className="bg-amber-500 text-white text-xs animate-pulse">
                退出中...
              </Badge>
            )}
            {position.status === 'active' && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onExit?.(position.id)}
                className="text-xs px-2 py-1"
              >
                <LogOut className="w-3 h-3 mr-1" />
                退出
              </Button>
            )}
          </div>
        </div>

        {/* 主要指標 */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          {/* 流動性 */}
          <div>
            <div className="text-xs text-gray-500 mb-1">流動性</div>
            <div className="text-lg font-bold text-gray-800">
              {formatCurrency(position.liquidityUsd)}
            </div>
          </div>

          {/* PnL */}
          <div>
            <div className="text-xs text-gray-500 mb-1">PnL</div>
            <div className={`text-lg font-bold flex items-center ${position.pnlPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
              {position.pnlPct >= 0 ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              {formatPercentage(position.pnlPct)}
            </div>
            <div className="text-xs text-gray-500">
              {formatCurrency(position.pnlUsd)}
            </div>
          </div>
        </div>

        {/* APR 和 Fee APR */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="bg-blue-50 rounded-lg p-2">
            <div className="text-xs text-blue-600 mb-1">總 APR</div>
            <div className="text-sm font-bold text-blue-800">
              {position.apr.toFixed(1)}%
            </div>
          </div>
          <div className="bg-green-50 rounded-lg p-2">
            <div className="text-xs text-green-600 mb-1">Fee APR</div>
            <div className="text-sm font-bold text-green-800">
              {position.feeApr.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* IL 進度條 */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-gray-500">無常損失</span>
            <span className={`text-xs font-medium ${getILColor(position.ilPct)}`}>
              {formatPercentage(position.ilPct)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full bg-gradient-to-r ${getILBarColor(position.ilPct)}`}
              style={{ width: `${Math.min(Math.abs(position.ilPct) * 10, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>0%</span>
            <span className="text-rose-500">-8%</span>
            <span className="text-rose-600">-10%</span>
          </div>
        </div>

        {/* 持倉時間和倒計時 */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <div>
              <div className="text-xs text-gray-500">持倉時間</div>
              <div className="text-sm font-medium text-gray-700">
                {formatTime(position.holdingTime)}
              </div>
            </div>
          </div>
          
          {position.countdown > 0 && (
            <div className="flex items-center space-x-2">
              <AlertTriangle className={`w-4 h-4 ${getCountdownColor(position.countdown)}`} />
              <div>
                <div className="text-xs text-gray-500">預計退出</div>
                <div className={`text-sm font-medium ${getCountdownColor(position.countdown)}`}>
                  {formatTime(position.countdown)}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 24h 手續費 */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-1">
            <DollarSign className="w-4 h-4 text-amber-500" />
            <span className="text-gray-600">24h 手續費:</span>
          </div>
          <span className="font-medium text-amber-600">
            {formatCurrency(position.fees24hUsd)}
          </span>
        </div>

        {/* 外部鏈接 */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <Button
            variant="ghost"
            size="sm"
            className="w-full text-xs text-gray-500 hover:text-gray-700"
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            查看池子詳情
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
