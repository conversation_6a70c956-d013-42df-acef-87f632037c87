import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Bot, Cpu, Shield, Target, Database, Activity, AlertTriangle, CheckCircle, Clock, Zap } from 'lucide-react'

const AgentMonitor = ({ systemStatus, agentLogs, agentsStatus }) => {
  const [selectedAgent, setSelectedAgent] = useState('all')

  // 使用真实的Agent状态数据
  const getAgentData = () => {
    if (!agentsStatus?.agents) {
      // 如果没有真实数据，返回默认数据
      return [
        {
          id: 'supervisor',
          name: 'Supervisor',
          description: '系統監督者',
          icon: Cpu,
          color: 'blue',
          status: systemStatus?.supervisor?.is_running ? 'running' : 'stopped',
          tasks: ['系統協調', '任務分配', '狀態監控'],
          metrics: {
            uptime: '24h 15m',
            tasks_completed: 156,
            success_rate: 98.5
          }
        },
        {
          id: 'risk_sentinel',
          name: 'Risk Sentinel',
          description: '風險監控代理',
          icon: Shield,
          color: 'orange',
          status: 'monitoring',
          tasks: ['風險評估', 'IL監控', '緊急退出'],
          metrics: {
            alerts_sent: 12,
            positions_monitored: 8,
            risk_score: 'LOW'
          }
        },
        {
          id: 'planner',
          name: 'Planner Agent',
          description: '策略規劃代理',
          icon: Target,
          color: 'green',
          status: 'planning',
          tasks: ['策略制定', '機會識別', '投資建議'],
          metrics: {
            strategies_created: 23,
            opportunities_found: 45,
            success_rate: 87.2
          }
        },
        {
          id: 'data_provider',
          name: 'Data Provider',
          description: '數據提供者',
          icon: Database,
          color: 'purple',
          status: 'syncing',
          tasks: ['數據收集', 'API同步', '價格更新'],
          metrics: {
            api_calls: 1247,
            data_points: 5632,
            sync_rate: 99.1
          }
        }
      ]
    }

    // 转换真实Agent数据
    const realAgents = []
    Object.entries(agentsStatus.agents).forEach(([agentKey, agentInfo]) => {
      const agentConfig = {
        data_provider: {
          id: 'data_provider',
          name: 'Data Provider',
          description: '數據提供者',
          icon: Database,
          color: 'purple',
          tasks: ['數據收集', 'API同步', '價格更新']
        },
        planner: {
          id: 'planner',
          name: 'Planner Agent',
          description: '策略規劃代理',
          icon: Target,
          color: 'green',
          tasks: ['策略制定', '機會識別', '投資建議']
        },
        risk_sentinel: {
          id: 'risk_sentinel',
          name: 'Risk Sentinel',
          description: '風險監控代理',
          icon: Shield,
          color: 'orange',
          tasks: ['風險評估', 'IL監控', '緊急退出']
        },
        portfolio: {
          id: 'portfolio',
          name: 'Portfolio Agent',
          description: '投資組合管理',
          icon: Activity,
          color: 'blue',
          tasks: ['組合管理', '收益追蹤', '重平衡']
        }
      }

      const config = agentConfig[agentKey] || {
        id: agentKey,
        name: agentInfo.name,
        description: '智能代理',
        icon: Bot,
        color: 'gray',
        tasks: ['執行任務']
      }

      realAgents.push({
        ...config,
        status: agentInfo.status,
        metrics: {
          success_rate: agentInfo.success_rate,
          total_runs: agentInfo.total_runs,
          errors: agentInfo.errors,
          last_run: agentInfo.last_run ? new Date(agentInfo.last_run).toLocaleString() : 'Never',
          next_run: agentInfo.next_run ? new Date(agentInfo.next_run).toLocaleString() : 'Unknown',
          ...agentInfo.data
        }
      })
    })

    return realAgents
  }

  const agents = getAgentData()

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-50'
      case 'monitoring': return 'text-blue-600 bg-blue-50'
      case 'planning': return 'text-yellow-600 bg-yellow-50'
      case 'syncing': return 'text-purple-600 bg-purple-50'
      case 'stopped': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />
      case 'monitoring': return <Activity className="h-4 w-4" />
      case 'planning': return <Clock className="h-4 w-4" />
      case 'syncing': return <Zap className="h-4 w-4" />
      case 'stopped': return <AlertTriangle className="h-4 w-4" />
      default: return <Bot className="h-4 w-4" />
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'running': return '運行中'
      case 'monitoring': return '監控中'
      case 'planning': return '策略中'
      case 'syncing': return '同步中'
      case 'stopped': return '已停止'
      default: return '未知'
    }
  }

  const filteredLogs = selectedAgent === 'all' 
    ? agentLogs 
    : agentLogs.filter(log => log.agent.toLowerCase().includes(selectedAgent.toLowerCase()))

  return (
    <div className="space-y-6">
      {/* Agents Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5 text-blue-500" />
            <span>AI Agents 總覽</span>
          </CardTitle>
          <CardDescription>智能代理系統運行狀態</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {agents.map((agent, index) => {
              const IconComponent = agent.icon
              return (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-white"
                >
                  {/* Agent Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`p-2 rounded-lg bg-${agent.color}-50`}>
                        <IconComponent className={`h-5 w-5 text-${agent.color}-500`} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">{agent.name}</h3>
                        <p className="text-xs text-gray-500">{agent.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Status */}
                  <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg mb-3 ${getStatusColor(agent.status)}`}>
                    {getStatusIcon(agent.status)}
                    <span className="text-sm font-medium">{getStatusText(agent.status)}</span>
                  </div>

                  {/* Tasks */}
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-700 mb-1">主要任務</h4>
                    <div className="space-y-1">
                      {agent.tasks.map((task, taskIndex) => (
                        <div key={taskIndex} className="text-xs text-gray-600 flex items-center space-x-1">
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <span>{task}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="space-y-1">
                    {Object.entries(agent.metrics).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center text-xs">
                        <span className="text-gray-600 capitalize">{key.replace('_', ' ')}</span>
                        <span className="font-medium">{value}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Agent Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-green-500" />
            <span>Agent 活動日誌</span>
          </CardTitle>
          <CardDescription>實時代理活動記錄</CardDescription>
          
          {/* Log Filters */}
          <div className="flex space-x-2 mt-4">
            <Button
              size="sm"
              variant={selectedAgent === 'all' ? 'default' : 'outline'}
              onClick={() => setSelectedAgent('all')}
            >
              全部
            </Button>
            {agents.map(agent => (
              <Button
                key={agent.id}
                size="sm"
                variant={selectedAgent === agent.id ? 'default' : 'outline'}
                onClick={() => setSelectedAgent(agent.id)}
              >
                {agent.name}
              </Button>
            ))}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredLogs.map((log, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.02 }}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-shrink-0">
                  <span className="text-gray-500 text-xs font-mono">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex-shrink-0">
                  <Badge
                    variant={
                      log.level === 'error' ? 'destructive' :
                      log.level === 'warning' ? 'secondary' : 'default'
                    }
                    className="text-xs"
                  >
                    {log.agent}
                  </Badge>
                </div>
                <div className="flex-1 min-w-0">
                  <span className="text-sm text-gray-800 break-words">{log.message}</span>
                </div>
              </motion.div>
            ))}
          </div>
          
          {filteredLogs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>沒有找到相關的日誌記錄</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AgentMonitor
