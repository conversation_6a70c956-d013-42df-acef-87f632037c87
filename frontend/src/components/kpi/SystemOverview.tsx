/**
 * 系統概覽卡片 - KPI + 控制按鈕
 * 解決痛點①：KPI 與控制混雜
 */

import React from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Play, 
  Pause, 
  Square, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Shield,
  Activity
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'
import { startWorkflow, stopWorkflow, setTradingMode } from '../../lib/wsHub'

export const SystemOverview: React.FC = () => {
  const overview = useAgnoStore(state => state.systemOverview)
  const tradingMode = useAgnoStore(state => state.tradingMode)
  const workflowRunning = useAgnoStore(state => state.workflowRunning)
  const currentPhase = useAgnoStore(state => state.currentPhase)

  const handleStartWorkflow = () => {
    startWorkflow()
    setTradingMode('active')
  }

  const handleStopWorkflow = () => {
    stopWorkflow()
    setTradingMode('paused')
  }

  const handleToggleMode = () => {
    const nextMode = tradingMode === 'paused' ? 'active' : 
                    tradingMode === 'active' ? 'exit_only' : 'paused'
    setTradingMode(nextMode)
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  }

  const getTradingModeColor = (mode: string) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500'
      case 'exit_only': return 'bg-amber-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTradingModeText = (mode: string) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-500'
    if (score >= 60) return 'text-amber-500'
    return 'text-rose-500'
  }

  return (
    <Card className="w-full h-full bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardContent className="p-4">
        {/* 緊湊的 KPI 指標區 - 一行顯示 */}
        <div className="flex items-center justify-between mb-4">
          {/* 左側 KPI */}
          <div className="flex items-center space-x-8">
            {/* NAV */}
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-blue-500" />
              <div>
                <span className="text-sm text-gray-500">NAV</span>
                <div className="flex items-center space-x-1">
                  <span className="text-lg font-bold text-gray-800">{formatCurrency(overview.nav)}</span>
                  <span className={`text-xs ${overview.nav24hPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                    {overview.nav24hPct >= 0 ? '↑' : '↓'}{Math.abs(overview.nav24hPct).toFixed(2)}%
                  </span>
                </div>
              </div>
            </div>

            {/* 24h Fee */}
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-emerald-500" />
              <div>
                <span className="text-sm text-gray-500">24h Fee</span>
                <div className="text-lg font-bold text-gray-800">{formatCurrency(overview.fees24h)}</div>
              </div>
            </div>

            {/* 風控分數 */}
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-purple-500" />
              <div>
                <span className="text-sm text-gray-500">風控分數</span>
                <div className={`text-lg font-bold ${getRiskScoreColor(overview.riskScore)}`}>
                  {overview.riskScore.toFixed(0)}
                </div>
              </div>
            </div>

            {/* 活躍持倉 */}
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-indigo-500" />
              <div>
                <span className="text-sm text-gray-500">活躍持倉</span>
                <div className="text-lg font-bold text-gray-800">
                  {overview.activePositions}/{overview.totalPositions}
                </div>
              </div>
            </div>
          </div>

          {/* 右側控制按鈕 */}
          <div className="flex items-center space-x-3">
            <Badge className={`${getTradingModeColor(tradingMode)} text-white`}>
              {getTradingModeText(tradingMode)}
            </Badge>

            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleMode}
              className="text-xs px-3 py-1"
            >
              切換手動/被動
            </Button>

            {!workflowRunning ? (
              <Button
                onClick={handleStartWorkflow}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 text-sm"
              >
                <Play className="w-4 h-4 mr-1" />
                一鍵啟動 AI 模式
              </Button>
            ) : (
              <Button
                onClick={handleStopWorkflow}
                variant="destructive"
                className="px-4 py-2 text-sm"
              >
                <Square className="w-4 h-4 mr-1" />
                停止
              </Button>
            )}
          </div>
        </div>

        {/* 工作流程狀態條 - 緊湊版 */}
        {workflowRunning && (
          <div className="bg-white rounded-lg p-2 shadow-sm border">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium text-gray-700">Phase {currentPhase}/8</span>
              <span className="text-xs text-gray-500">{Math.round((currentPhase / 8) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-gradient-to-r from-blue-500 to-emerald-500 h-1.5 rounded-full transition-all duration-500"
                style={{ width: `${(currentPhase / 8) * 100}%` }}
              ></div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
