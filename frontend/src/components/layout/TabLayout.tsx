import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Activity,
  TrendingUp,
  Shield,
  Server,
  Coins,
  BarChart3,
  Users,
  Clock
} from 'lucide-react'
import { useAgnoStore } from '@/store/useAgno'

type TabType = 'overview' | 'bsc' | 'solana' | 'agents' | 'risk'

const TabLayout: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const { hotPools, positions, agentFlow, riskSummary, infraHealth } = useAgnoStore()

  // 統計數據
  const bscPools = hotPools.filter(pool => pool.chain === 'bsc')
  const solPools = hotPools.filter(pool => pool.chain === 'solana')
  const bscPositions = positions.filter(pos => pos.chain === 'bsc')
  const solPositions = positions.filter(pos => pos.chain === 'solana')

  const tabs = [
    {
      id: 'overview' as TabType,
      label: '系統概覽',
      icon: Activity,
      count: null,
      description: '整體狀態和 KPI'
    },
    {
      id: 'bsc' as TabType,
      label: 'BSC',
      icon: Coins,
      count: bscPools.length + bscPositions.length,
      description: 'PancakeSwap V3 池子和持倉'
    },
    {
      id: 'solana' as TabType,
      label: 'Solana',
      icon: TrendingUp,
      count: solPools.length + solPositions.length,
      description: 'Meteora DLMM 池子和持倉'
    },
    {
      id: 'agents' as TabType,
      label: 'Agent 流程',
      icon: Users,
      count: agentFlow.filter(agent => agent.status === 'completed').length,
      description: '8 階段工作流程'
    },
    {
      id: 'risk' as TabType,
      label: '風險監控',
      icon: Shield,
      count: null,
      description: '風險指標和基礎設施'
    }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>系統概覽</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <Coins className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm text-gray-600">BSC 池子</p>
                      <p className="text-2xl font-bold">{bscPools.length}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-sm text-gray-600">SOL 池子</p>
                      <p className="text-2xl font-bold">{solPools.length}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm text-gray-600">總持倉</p>
                      <p className="text-2xl font-bold">{positions.length}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Shield className="h-5 w-5 text-red-500" />
                    <div>
                      <p className="text-sm text-gray-600">風險分數</p>
                      <p className="text-2xl font-bold">{riskSummary.riskScore || 75}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )

      case 'bsc':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">BSC (Binance Smart Chain)</h2>
                <p className="text-gray-600">PancakeSwap V3 池子和 LP 持倉管理</p>
              </div>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                {bscPools.length} 池子 · {bscPositions.length} 持倉
              </Badge>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>BSC 池子和持倉</CardTitle>
              </CardHeader>
              <CardContent>
                <p>BSC 相關功能正在開發中...</p>
                <p>池子數量: {bscPools.length}</p>
                <p>持倉數量: {bscPositions.length}</p>
              </CardContent>
            </Card>
          </div>
        )

      case 'solana':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">Solana</h2>
                <p className="text-gray-600">Meteora DLMM v2 池子和 LP 持倉管理</p>
              </div>
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                {solPools.length} 池子 · {solPositions.length} 持倉
              </Badge>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Solana 池子和持倉</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Solana 相關功能正在開發中...</p>
                <p>池子數量: {solPools.length}</p>
                <p>持倉數量: {solPositions.length}</p>
              </CardContent>
            </Card>
          </div>
        )

      case 'agents':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">Agent 協作流程</h2>
                <p className="text-gray-600">8 階段工作流程和 Agent 狀態監控</p>
              </div>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                {agentFlow.filter(agent => agent.status === 'completed').length}/8 完成
              </Badge>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Agent 流程</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Agent 協作流程正在開發中...</p>
                <p>已完成 Agent: {agentFlow.filter(agent => agent.status === 'completed').length}</p>
              </CardContent>
            </Card>
          </div>
        )

      case 'risk':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">風險監控</h2>
                <p className="text-gray-600">風險指標監控和基礎設施健康狀態</p>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                風險分數: {riskSummary.riskScore || 75}
              </Badge>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>風險監控</CardTitle>
              </CardHeader>
              <CardContent>
                <p>風險監控功能正在開發中...</p>
                <p>風險分數: {riskSummary.riskScore || 75}</p>
              </CardContent>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 標籤頁導航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                    ${isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                  {tab.count !== null && (
                    <Badge variant="secondary" className="ml-1">
                      {tab.count}
                    </Badge>
                  )}
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* 標籤頁內容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default TabLayout
