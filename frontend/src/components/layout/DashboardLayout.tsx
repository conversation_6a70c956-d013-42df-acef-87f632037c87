/**
 * DyFlow v3.3 Dashboard 佈局
 * 新版資訊架構：解決 5 個痛點的重新佈局
 */

import React, { useEffect } from 'react'
import { ToggleMode } from '../common/ToggleMode'
import { SystemOverview } from '../kpi/SystemOverview'
import { HotPoolTable } from '../pools/HotPoolTable'
import { PositionList } from '../lp/PositionList'
import { FlowTimeline } from '../agents/FlowTimeline'
import { RiskPanel } from '../risk/RiskPanel'
import { InfraStatus } from '../health/InfraStatus'
import { useAgnoStore } from '../../store/useAgno'

export const DashboardLayout: React.FC = () => {
  const connectionStatus = useAgnoStore(state => state.connectionStatus)

  // 初始化 WebSocket 連接在 wsHub 中自動處理
  useEffect(() => {
    // WebSocket 連接已在 wsHub 中自動建立
    console.log('🎯 DashboardLayout mounted, WebSocket connection status:', connectionStatus)
  }, [connectionStatus])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂欄標題 */}
      <header className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800">DyFlow v3.3 Dashboard</h1>
          <p className="text-sm text-gray-500 mt-1">24/7 自動化多 Agent LP 策略系統 — 全景總覽</p>
        </div>
      </header>

      {/* 12-column Grid Layout - 一屏全景 */}
      <main className="p-4">
        <div className="grid grid-cols-12 gap-4 max-w-7xl mx-auto">
          {/* A. 全局 KPI & 控制 - col-span-12 row-span-2 */}
          <section className="col-span-12 h-32">
            <SystemOverview />
          </section>

          {/* B. 池子監控 - col-span-4 row-span-4 */}
          <section className="col-span-4 h-96">
            <HotPoolTable />
          </section>

          {/* C. Agent 狀態 - col-span-4 row-span-4 */}
          <section className="col-span-4 h-96">
            <FlowTimeline />
          </section>

          {/* D. LP 持倉 - col-span-4 row-span-4 */}
          <section className="col-span-4 h-96">
            <PositionList />
          </section>

          {/* E. 風險監控 - col-span-6 row-span-3 */}
          <section className="col-span-6 h-72">
            <RiskPanel />
          </section>

          {/* F. 系統健康 - col-span-6 row-span-3 */}
          <section className="col-span-6 h-72">
            <InfraStatus />
          </section>
        </div>
      </main>

      {/* 連接狀態指示器 */}
      {connectionStatus !== 'connected' && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`rounded-lg px-4 py-2 text-white text-sm shadow-lg ${
            connectionStatus === 'connecting' ? 'bg-amber-500' :
            connectionStatus === 'disconnected' ? 'bg-gray-500' :
            connectionStatus === 'error' ? 'bg-rose-500' :
            connectionStatus === 'failed' ? 'bg-rose-600' : 'bg-gray-500'
          }`}>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connecting' ? 'bg-white animate-pulse' : 'bg-white'
              }`}></div>
              <span>
                {connectionStatus === 'connecting' ? '正在連接...' :
                 connectionStatus === 'disconnected' ? '連接已斷開' :
                 connectionStatus === 'error' ? '連接錯誤' :
                 connectionStatus === 'failed' ? '連接失敗' : '連接異常'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
