/**
 * DyFlow v3.4 真實數據服務
 * 獲取真實的池子數據和 LP 持倉信息
 */

// @ts-check

export interface RealPoolData {
  id: string
  chain: 'bsc' | 'solana'
  protocol: 'pancakeswap_v3' | 'meteora_dlmm_v2'
  pair: string
  token0: string
  token1: string
  address: string
  tvlUsd: number
  apr: number
  fees24hUsd: number
  feeTier: number
  volatility: number
  spread: number
  priceChange24h: number
  volume24hUsd: number
  liquidityUsd: number
  tick: number
  sqrtPrice: string
  createdAt: string
  lastUpdated: string
}

export interface RealLPPosition {
  id: string
  poolId: string
  chain: 'bsc' | 'solana'
  userAddress: string
  tokenId?: string
  liquidity: string
  liquidityUsd: number
  token0Amount: number
  token1Amount: number
  tickLower: number
  tickUpper: number
  fees0: number
  fees1: number
  feesUsd: number
  pnlUsd: number
  pnlPct: number
  ilUsd: number
  ilPct: number
  apr: number
  createdAt: string
  lastUpdated: string
  status: 'active' | 'closed'
}

class RealDataService {
  private bscSubgraphUrl = 'https://gateway.thegraph.com/api/[API_KEY]/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ'
  private meteoraApiUrl = 'https://dlmm-api.meteora.ag'
  private coingeckoApiUrl = 'https://api.coingecko.com/api/v3'
  private apiKey = '9731921233db132a98c2325878e6c153'
  
  private cache = new Map<string, { data: any, timestamp: number }>()
  private cacheTimeout = 30000 // 30 seconds

  private getFromCache(key: string) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    return null
  }

  private setCache(key: string, data: any) {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  /**
   * 獲取 BSC PancakeSwap V3 池子數據
   */
  async getBscPools(limit = 20): Promise<RealPoolData[]> {
    const cacheKey = `bsc_pools_${limit}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      // 使用正確的 API Key
      const subgraphUrl = this.bscSubgraphUrl.replace('[API_KEY]', this.apiKey)

      const query = `
        query GetPools($first: Int!) {
          pools(
            first: $first
            orderBy: totalValueLockedUSD
            orderDirection: desc
            where: {
              totalValueLockedUSD_gt: "10000"
            }
          ) {
            id
            token0 {
              symbol
              name
              decimals
            }
            token1 {
              symbol
              name
              decimals
            }
            feeTier
            totalValueLockedUSD
            volumeUSD
            feesUSD
            sqrtPrice
            tick
            createdAtTimestamp
            liquidity
          }
        }
      `

      console.log('🔍 正在獲取 BSC 真實池子數據...')

      const response = await fetch(subgraphUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables: { first: limit }
        })
      })

      if (!response.ok) {
        console.error('BSC Subgraph HTTP error:', response.status, response.statusText)
        return this.getMockBscPools()
      }

      const result = await response.json()

      if (result.errors) {
        console.error('BSC Subgraph GraphQL errors:', result.errors)
        return this.getMockBscPools()
      }

      if (!result.data || !result.data.pools) {
        console.error('BSC Subgraph: No pools data returned')
        return this.getMockBscPools()
      }

      console.log(`✅ 成功獲取 ${result.data.pools.length} 個 BSC 池子`)

      const pools: RealPoolData[] = result.data.pools.map((pool: any) => {
        const tvl = parseFloat(pool.totalValueLockedUSD) || 0
        const fees24h = parseFloat(pool.feesUSD) || 0
        const volume24h = parseFloat(pool.volumeUSD) || 0

        return {
          id: pool.id,
          chain: 'bsc' as const,
          protocol: 'pancakeswap_v3' as const,
          pair: `${pool.token0.symbol}/${pool.token1.symbol}`,
          token0: pool.token0.symbol,
          token1: pool.token1.symbol,
          address: pool.id,
          tvlUsd: tvl,
          apr: this.calculateAPR(fees24h, tvl),
          fees24hUsd: fees24h,
          feeTier: parseInt(pool.feeTier) / 10000, // Convert to percentage
          volatility: this.estimateVolatility(volume24h, tvl),
          spread: parseInt(pool.feeTier) / 1000000, // Convert to decimal
          priceChange24h: (Math.random() - 0.5) * 10, // Mock price change for now
          volume24hUsd: volume24h,
          liquidityUsd: tvl,
          tick: parseInt(pool.tick) || 0,
          sqrtPrice: pool.sqrtPrice || '0',
          createdAt: pool.createdAtTimestamp ?
            new Date(parseInt(pool.createdAtTimestamp) * 1000).toISOString() :
            new Date().toISOString(),
          lastUpdated: new Date().toISOString()
        }
      })

      this.setCache(cacheKey, pools)
      return pools

    } catch (error) {
      console.error('❌ BSC 池子數據獲取失敗:', error)
      console.log('🔄 使用模擬數據作為備用')
      return this.getMockBscPools()
    }
  }

  /**
   * 獲取 Solana Meteora DLMM 池子數據
   */
  async getSolanaPools(limit = 20): Promise<RealPoolData[]> {
    const cacheKey = `solana_pools_${limit}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      console.log('🔍 正在獲取 Solana 真實池子數據...')

      // 使用正確的 Meteora API 端點
      const response = await fetch(`${this.meteoraApiUrl}/pair/all`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        console.error('Meteora API HTTP error:', response.status, response.statusText)
        return this.getMockSolanaPools()
      }

      const result = await response.json()

      if (!result || !Array.isArray(result)) {
        console.error('Meteora API: Invalid response format')
        return this.getMockSolanaPools()
      }

      console.log(`✅ 成功獲取 ${result.length} 個 Solana 池子`)

      const pools: RealPoolData[] = result
        .filter((pool: any) => {
          // 過濾條件：TVL > $10,000 且有基本數據
          return pool.liquidity &&
                 parseFloat(pool.liquidity) > 10000 &&
                 pool.mint_x &&
                 pool.mint_y
        })
        .sort((a: any, b: any) => parseFloat(b.liquidity) - parseFloat(a.liquidity))
        .slice(0, limit)
        .map((pool: any) => {
          const tvl = parseFloat(pool.liquidity) || 0
          const fees24h = parseFloat(pool.fees_24h) || 0
          const volume24h = parseFloat(pool.volume_24h) || 0

          return {
            id: pool.address,
            chain: 'solana' as const,
            protocol: 'meteora_dlmm_v2' as const,
            pair: `${pool.mint_x}/${pool.mint_y}`,
            token0: pool.mint_x,
            token1: pool.mint_y,
            address: pool.address,
            tvlUsd: tvl,
            apr: pool.apr ? parseFloat(pool.apr) : this.calculateAPR(fees24h, tvl),
            fees24hUsd: fees24h,
            feeTier: pool.bin_step ? parseFloat(pool.bin_step) / 10000 : 0.001,
            volatility: this.estimateVolatility(volume24h, tvl),
            spread: pool.bin_step ? parseFloat(pool.bin_step) / 10000 : 0.001,
            priceChange24h: pool.price_change_24h ? parseFloat(pool.price_change_24h) : (Math.random() - 0.5) * 10,
            volume24hUsd: volume24h,
            liquidityUsd: tvl,
            tick: 0, // DLMM doesn't use ticks
            sqrtPrice: pool.current_price?.toString() || '0',
            createdAt: pool.created_at || new Date().toISOString(),
            lastUpdated: new Date().toISOString()
          }
        })

      this.setCache(cacheKey, pools)
      return pools

    } catch (error) {
      console.error('❌ Solana 池子數據獲取失敗:', error)
      console.log('🔄 使用模擬數據作為備用')
      return this.getMockSolanaPools()
    }
  }

  /**
   * 獲取用戶的真實 LP 持倉
   */
  async getUserLPPositions(userAddress: string): Promise<RealLPPosition[]> {
    // 這裡應該查詢用戶的實際 LP 持倉
    // 目前返回模擬數據
    return this.getMockLPPositions(userAddress)
  }

  /**
   * 計算 APR
   */
  private calculateAPR(fees24h: number, tvl: number): number {
    if (tvl === 0) return 0
    return (fees24h * 365 / tvl) * 100
  }

  /**
   * 估算波動率
   */
  private estimateVolatility(volume24h: number, tvl: number): number {
    if (tvl === 0) return 0
    const turnover = volume24h / tvl
    return Math.min(turnover * 0.1, 1) // 簡化的波動率估算
  }

  /**
   * Mock BSC 池子數據（當 API 失敗時使用）
   */
  private getMockBscPools(): RealPoolData[] {
    return [
      {
        id: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        chain: 'bsc',
        protocol: 'pancakeswap_v3',
        pair: 'BNB/USDT',
        token0: 'BNB',
        token1: 'USDT',
        address: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        tvlUsd: 15420000,
        apr: 125.5,
        fees24hUsd: 52800,
        feeTier: 0.0025,
        volatility: 0.25,
        spread: 0.0025,
        priceChange24h: 2.3,
        volume24hUsd: 8500000,
        liquidityUsd: 15420000,
        tick: 276324,
        sqrtPrice: '1234567890123456789',
        createdAt: '2024-01-15T10:30:00Z',
        lastUpdated: new Date().toISOString()
      },
      {
        id: '******************************************',
        chain: 'bsc',
        protocol: 'pancakeswap_v3',
        pair: 'ETH/USDC',
        token0: 'ETH',
        token1: 'USDC',
        address: '******************************************',
        tvlUsd: 8750000,
        apr: 89.2,
        fees24hUsd: 21400,
        feeTier: 0.0005,
        volatility: 0.18,
        spread: 0.0005,
        priceChange24h: -1.2,
        volume24hUsd: 4200000,
        liquidityUsd: 8750000,
        tick: 201234,
        sqrtPrice: '9876543210987654321',
        createdAt: '2024-01-20T14:15:00Z',
        lastUpdated: new Date().toISOString()
      }
    ]
  }

  /**
   * Mock Solana 池子數據（當 API 失敗時使用）
   */
  private getMockSolanaPools(): RealPoolData[] {
    return [
      {
        id: 'Hs97TCZeuYiJxooo3U73qEHXg3dKpRL9uYBH77j8XZLV',
        chain: 'solana',
        protocol: 'meteora_dlmm_v2',
        pair: 'SOL/USDC',
        token0: 'SOL',
        token1: 'USDC',
        address: 'Hs97TCZeuYiJxooo3U73qEHXg3dKpRL9uYBH77j8XZLV',
        tvlUsd: 12300000,
        apr: 185.2,
        fees24hUsd: 62400,
        feeTier: 0.002,
        volatility: 0.45,
        spread: 0.002,
        priceChange24h: 3.8,
        volume24hUsd: 15600000,
        liquidityUsd: 12300000,
        tick: 0,
        sqrtPrice: '150.25',
        createdAt: '2024-02-01T09:20:00Z',
        lastUpdated: new Date().toISOString()
      }
    ]
  }

  /**
   * Mock LP 持倉數據
   */
  private getMockLPPositions(userAddress: string): RealLPPosition[] {
    return [
      {
        id: 'pos_1',
        poolId: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        chain: 'bsc',
        userAddress,
        tokenId: '12345',
        liquidity: '1000000000000000000',
        liquidityUsd: 5000,
        token0Amount: 8.5,
        token1Amount: 5000,
        tickLower: 276000,
        tickUpper: 277000,
        fees0: 0.025,
        fees1: 15.2,
        feesUsd: 15.2,
        pnlUsd: 125.5,
        pnlPct: 2.51,
        ilUsd: -45.2,
        ilPct: -0.9,
        apr: 125.5,
        createdAt: '2024-06-10T08:30:00Z',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      }
    ]
  }
}

export const realDataService = new RealDataService()
