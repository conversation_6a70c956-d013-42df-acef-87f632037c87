#!/usr/bin/env python3
"""
測試 UnifiedWalletManager
驗證錢包管理功能
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加項目根目錄到 Python 路徑
sys.path.append(str(Path(__file__).parent))

# 載入環境變量
load_dotenv()

try:
    from backend.utils.wallet_manager import UnifiedWalletManager
    print("✅ UnifiedWalletManager 導入成功")
except ImportError as e:
    print(f"❌ UnifiedWalletManager 導入失敗: {e}")
    sys.exit(1)

async def test_wallet_manager_initialization():
    """測試錢包管理器初始化"""
    print("\n=== 測試錢包管理器初始化 ===")
    
    try:
        # 創建錢包管理器
        wallet_manager = UnifiedWalletManager()
        print("✅ UnifiedWalletManager 創建成功")
        
        # 檢查私鑰配置
        bsc_key_configured = bool(wallet_manager.private_keys.get('bsc'))
        solana_key_configured = bool(wallet_manager.private_keys.get('solana'))
        
        print(f"  BSC 私鑰配置: {'✅' if bsc_key_configured else '❌'}")
        print(f"  Solana 私鑰配置: {'✅' if solana_key_configured else '❌'}")
        
        # 檢查 RPC 配置
        bsc_rpc = wallet_manager.rpc_urls.get('bsc')
        solana_rpc = wallet_manager.rpc_urls.get('solana')
        
        print(f"  BSC RPC: {bsc_rpc}")
        print(f"  Solana RPC: {solana_rpc}")
        
        return wallet_manager
        
    except Exception as e:
        print(f"❌ 錢包管理器初始化失敗: {e}")
        return None

async def test_wallet_initialization(wallet_manager):
    """測試錢包初始化"""
    print("\n=== 測試錢包初始化 ===")
    
    if not wallet_manager:
        print("❌ 錢包管理器不存在，跳過測試")
        return False
    
    try:
        # 執行初始化
        init_result = await wallet_manager.initialize()
        
        print(f"  初始化結果: {'✅ 成功' if init_result['success'] else '❌ 失敗'}")
        
        if init_result['success']:
            # 檢查錢包連接狀態
            wallet_status = wallet_manager.get_wallet_status()
            
            bsc_connected = wallet_status['wallet_status']['bsc']['connected']
            solana_connected = wallet_status['wallet_status']['solana']['connected']
            
            print(f"  BSC 錢包連接: {'✅' if bsc_connected else '❌'}")
            print(f"  Solana 錢包連接: {'✅' if solana_connected else '❌'}")
            
            return bsc_connected and solana_connected
        else:
            print(f"  錯誤信息: {init_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 錢包初始化測試失敗: {e}")
        return False

async def test_private_key_validation(wallet_manager):
    """測試私鑰驗證"""
    print("\n=== 測試私鑰驗證 ===")
    
    if not wallet_manager:
        print("❌ 錢包管理器不存在，跳過測試")
        return False
    
    try:
        # 測試 BSC 私鑰驗證
        bsc_key = wallet_manager.private_keys.get('bsc')
        if bsc_key:
            bsc_valid = wallet_manager._is_valid_bsc_private_key(bsc_key)
            print(f"  BSC 私鑰格式: {'✅ 有效' if bsc_valid else '❌ 無效'}")
        else:
            print("  BSC 私鑰: ❌ 未配置")
            bsc_valid = False
        
        # 測試 Solana 私鑰驗證
        solana_key = wallet_manager.private_keys.get('solana')
        if solana_key:
            solana_valid = wallet_manager._is_valid_solana_private_key(solana_key)
            print(f"  Solana 私鑰格式: {'✅ 有效' if solana_valid else '❌ 無效'}")
        else:
            print("  Solana 私鑰: ❌ 未配置")
            solana_valid = False
        
        return bsc_valid and solana_valid
        
    except Exception as e:
        print(f"❌ 私鑰驗證測試失敗: {e}")
        return False

async def test_wallet_status(wallet_manager):
    """測試錢包狀態獲取"""
    print("\n=== 測試錢包狀態獲取 ===")
    
    if not wallet_manager:
        print("❌ 錢包管理器不存在，跳過測試")
        return False
    
    try:
        status = wallet_manager.get_wallet_status()
        
        print("  錢包狀態:")
        print(f"    BSC: {status['wallet_status']['bsc']}")
        print(f"    Solana: {status['wallet_status']['solana']}")
        print(f"    私鑰配置: {status['private_keys_configured']}")
        print(f"    RPC URLs: {status['rpc_urls']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 錢包狀態測試失敗: {e}")
        return False

async def test_transaction_signing_simulation(wallet_manager):
    """測試交易簽名（模擬）"""
    print("\n=== 測試交易簽名（模擬）===")
    
    if not wallet_manager:
        print("❌ 錢包管理器不存在，跳過測試")
        return False
    
    try:
        # 模擬 BSC 交易
        bsc_tx = {
            'to': '******************************************',
            'value': '1000000000000000000',  # 1 ETH in wei
            'gas': 21000,
            'gasPrice': 20000000000,  # 20 Gwei
            'nonce': 0,
            'data': '0x',
            'chain': 'bsc'
        }
        
        bsc_result = await wallet_manager.sign_transaction(bsc_tx)
        print(f"  BSC 交易簽名: {'✅ 成功' if bsc_result.get('success') else '❌ 失敗'}")
        
        # 模擬 Solana 交易
        solana_tx = {
            'instructions': [],
            'recent_blockhash': 'test_blockhash',
            'fee_payer': 'test_fee_payer',
            'chain': 'solana'
        }
        
        solana_result = await wallet_manager.sign_transaction(solana_tx)
        print(f"  Solana 交易簽名: {'✅ 成功' if solana_result.get('success') else '❌ 失敗'}")
        
        return bsc_result.get('success', False) and solana_result.get('success', False)
        
    except Exception as e:
        print(f"❌ 交易簽名測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 開始錢包管理器測試")
    
    results = {}
    
    # 測試 1: 錢包管理器初始化
    wallet_manager = await test_wallet_manager_initialization()
    results['wallet_manager_init'] = wallet_manager is not None
    
    # 測試 2: 錢包初始化
    results['wallet_initialization'] = await test_wallet_initialization(wallet_manager)
    
    # 測試 3: 私鑰驗證
    results['private_key_validation'] = await test_private_key_validation(wallet_manager)
    
    # 測試 4: 錢包狀態
    results['wallet_status'] = await test_wallet_status(wallet_manager)
    
    # 測試 5: 交易簽名（模擬）
    results['transaction_signing'] = await test_transaction_signing_simulation(wallet_manager)
    
    # 總結結果
    print("\n" + "="*50)
    print("📊 錢包管理器測試結果總結")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有錢包管理器測試通過！")
        return True
    else:
        print("⚠️ 部分測試失敗，需要檢查配置")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
