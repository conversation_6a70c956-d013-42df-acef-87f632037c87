#!/usr/bin/env python3
"""
DyFlow v3.4 統一管理器
合併所有啟動、測試、安裝功能到一個腳本中
避免重複的腳本和邏輯
"""

import asyncio
import subprocess
import sys
import os
import time
import signal
import argparse
from pathlib import Path
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class DyFlowManager:
    """DyFlow v3.4 統一管理器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = False
        load_dotenv()
        
    # ========== 啟動功能 ==========
    
    def start_unified(self):
        """統一啟動模式 - 啟動所有服務"""
        print("🚀 DyFlow v3.4 統一啟動模式")
        print("=" * 50)
        
        if not self._check_prerequisites():
            return False
        
        self._setup_signal_handlers()
        
        try:
            # 啟動 Agno Workflow API
            api_process = self._start_agno_api()
            if not api_process:
                return False
            
            time.sleep(3)  # 等待 API 啟動
            
            # 啟動 React UI
            ui_process = self._start_react_ui()
            if not ui_process:
                return False
            
            time.sleep(5)  # 等待 UI 啟動
            
            self.running = True
            self._print_startup_info()
            self._monitor_processes()
            
            return True
            
        except Exception as e:
            print(f"❌ 啟動失敗: {e}")
            self.stop_all()
            return False
    
    async def start_phase1(self):
        """Phase 1 啟動模式 - 專注於核心功能測試"""
        print("🚀 DyFlow v3.4 Phase 1 啟動模式")
        print("=" * 50)
        
        try:
            # 導入 Phase 1 組件
            from backend.utils.wallet_manager import UnifiedWalletManager
            from backend.agents.enhanced_market_intel_agent import EnhancedMarketIntelAgent
            from backend.agents.execution_agent import ExecutionAgent
            
            # 初始化錢包管理器
            print("🔧 初始化錢包管理器...")
            wallet_manager = UnifiedWalletManager()
            init_result = await wallet_manager.initialize()
            
            if not init_result['success']:
                print(f"❌ 錢包初始化失敗: {init_result.get('error')}")
                return False
            
            print("✅ 錢包管理器初始化成功")
            
            # 初始化市場情報 Agent
            print("📊 初始化市場情報 Agent...")
            market_intel = EnhancedMarketIntelAgent({
                'bsc_scan_interval': 15,
                'sol_scan_interval': 12
            })
            
            # 初始化執行 Agent
            print("⚡ 初始化執行 Agent...")
            execution_agent = ExecutionAgent({
                'max_retries': 3,
                'timeout': 120
            })
            
            print("✅ Phase 1 組件初始化完成")
            
            # 啟動主循環
            print("🔄 啟動主循環...")
            await self._phase1_main_loop(market_intel, execution_agent)
            
            return True
            
        except Exception as e:
            print(f"❌ Phase 1 啟動失敗: {e}")
            return False
    
    # ========== 測試功能 ==========
    
    async def run_tests(self, test_type: str = "all"):
        """運行測試"""
        print(f"🧪 運行測試: {test_type}")
        print("=" * 50)
        
        test_results = {}
        
        if test_type in ["all", "env"]:
            test_results["environment"] = self._test_environment()
        
        if test_type in ["all", "agno"]:
            test_results["agno"] = await self._test_agno_framework()
        
        if test_type in ["all", "wallet"]:
            test_results["wallet"] = await self._test_wallet_manager()
        
        if test_type in ["all", "agents"]:
            test_results["agents"] = await self._test_agents()
        
        if test_type in ["all", "integration"]:
            test_results["integration"] = await self._test_integration()
        
        # 生成測試報告
        self._generate_test_report(test_results)
        
        # 返回整體測試結果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        
        return passed == total
    
    def install_dependencies(self):
        """安裝依賴"""
        print("📦 安裝 DyFlow v3.4 依賴")
        print("=" * 50)
        
        success = True
        
        # 檢查 Python 版本
        if not self._check_python_version():
            success = False
        
        # 安裝 Python 包
        if not self._install_python_packages():
            success = False
        
        # 安裝 Ollama
        if not self._install_ollama():
            success = False
        
        # 安裝 Node.js 依賴
        if not self._install_node_dependencies():
            success = False
        
        # 創建環境文件
        if not self._create_env_file():
            success = False
        
        if success:
            print("🎉 所有依賴安裝成功！")
        else:
            print("⚠️ 部分依賴安裝失敗")
        
        return success
    
    # ========== 清理功能 ==========
    
    def cleanup_duplicates(self):
        """清理重複文件"""
        print("🗑️ 清理重複文件和腳本")
        print("=" * 50)
        
        # 要移除的重複腳本
        duplicate_scripts = [
            "test_agno_initialization.py",
            "test_wallet_manager.py", 
            "test_core_functionality.py",
            "test_backend_cleanup.py",
            "start_dyflow_v34_phase1.py"  # 保留 start_dyflow_v34_unified.py
        ]
        
        removed_count = 0
        for script in duplicate_scripts:
            script_path = project_root / script
            if script_path.exists():
                try:
                    script_path.unlink()
                    print(f"✅ 已移除: {script}")
                    removed_count += 1
                except Exception as e:
                    print(f"❌ 移除失敗 {script}: {e}")
            else:
                print(f"⚠️ 文件不存在: {script}")
        
        print(f"\n📊 清理完成: 移除了 {removed_count} 個重複腳本")
        return removed_count > 0
    
    # ========== 輔助方法 ==========
    
    def _check_prerequisites(self):
        """檢查前置條件"""
        print("🔍 檢查前置條件...")
        
        # 檢查 Ollama
        try:
            result = subprocess.run(
                ["curl", "-s", "http://localhost:11434/api/tags"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                print("✅ Ollama 服務運行正常")
            else:
                print("❌ Ollama 服務未運行")
                return False
        except:
            print("❌ 無法連接到 Ollama 服務")
            return False
        
        # 檢查環境變量
        required_vars = ['BSC_PRIVATE_KEY', 'SOLANA_PRIVATE_KEY']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ 缺少環境變量: {missing_vars}")
            return False
        
        print("✅ 前置條件檢查通過")
        return True
    
    def _start_agno_api(self):
        """啟動 Agno API"""
        print("🚀 啟動 Agno Workflow API...")
        
        cmd = [sys.executable, "backend/api/agno_workflow_api.py"]
        
        try:
            process = subprocess.Popen(
                cmd, cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            self.processes.append(process)
            print(f"✅ Agno API 已啟動 (PID: {process.pid})")
            return process
        except Exception as e:
            print(f"❌ Agno API 啟動失敗: {e}")
            return None
    
    def _start_react_ui(self):
        """啟動 React UI"""
        print("🌐 啟動 React UI...")
        
        frontend_path = project_root / "frontend"
        if not frontend_path.exists():
            print("❌ frontend 目錄不存在")
            return None
        
        cmd = ["npm", "run", "dev"]
        
        try:
            process = subprocess.Popen(
                cmd, cwd=frontend_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            self.processes.append(process)
            print(f"✅ React UI 已啟動 (PID: {process.pid})")
            return process
        except Exception as e:
            print(f"❌ React UI 啟動失敗: {e}")
            return None
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            print(f"\n⏹️ 收到信號 {signum}")
            self.stop_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _monitor_processes(self):
        """監控進程"""
        while self.running:
            for process in self.processes[:]:
                if process.poll() is not None:
                    print(f"⚠️ 進程 {process.pid} 已退出")
                    self.processes.remove(process)
            time.sleep(2)
    
    def _print_startup_info(self):
        """打印啟動信息"""
        print("\n" + "=" * 50)
        print("🎉 DyFlow v3.4 已成功啟動！")
        print("=" * 50)
        print("📊 Agno Workflow API: http://localhost:8001")
        print("🌐 React UI: http://localhost:3000")
        print("📡 WebSocket: ws://localhost:8001/ws")
        print("=" * 50)
        print("按 Ctrl+C 停止所有服務")
    
    def stop_all(self):
        """停止所有進程"""
        print("⏹️ 停止所有服務...")
        self.running = False
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ 進程 {process.pid} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 強制終止進程 {process.pid}")
            except Exception as e:
                print(f"❌ 停止進程失敗: {e}")
        
        self.processes.clear()
    
    # ========== 測試方法 ==========
    
    def _test_environment(self):
        """測試環境配置"""
        print("🔧 測試環境配置...")
        
        required_vars = ['BSC_PRIVATE_KEY', 'SOLANA_PRIVATE_KEY', 'BSC_RPC_URL', 'SOLANA_RPC_URL']
        missing = [var for var in required_vars if not os.getenv(var)]
        
        if missing:
            print(f"❌ 缺少環境變量: {missing}")
            return False
        
        print("✅ 環境配置測試通過")
        return True
    
    async def _test_agno_framework(self):
        """測試 Agno Framework"""
        print("🤖 測試 Agno Framework...")
        
        try:
            from backend.workflows.dyflow_agno_workflow import DyFlowAgnoWorkflow, AGNO_AVAILABLE
            
            if not AGNO_AVAILABLE:
                print("❌ Agno Framework 不可用")
                return False
            
            workflow = DyFlowAgnoWorkflow(session_id="test-session")
            print("✅ Agno Framework 測試通過")
            return True
            
        except Exception as e:
            print(f"❌ Agno Framework 測試失敗: {e}")
            return False
    
    async def _test_wallet_manager(self):
        """測試錢包管理器"""
        print("💰 測試錢包管理器...")
        
        try:
            from backend.utils.wallet_manager import UnifiedWalletManager
            
            wallet_manager = UnifiedWalletManager()
            init_result = await wallet_manager.initialize()
            
            if init_result['success']:
                print("✅ 錢包管理器測試通過")
                return True
            else:
                print(f"❌ 錢包管理器測試失敗: {init_result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ 錢包管理器測試失敗: {e}")
            return False
    
    async def _test_agents(self):
        """測試 Agents"""
        print("🤖 測試 Agents...")
        
        try:
            from backend.agents.enhanced_market_intel_agent import EnhancedMarketIntelAgent
            from backend.agents.execution_agent import ExecutionAgent
            
            # 測試市場情報 Agent
            market_intel = EnhancedMarketIntelAgent({'bsc_scan_interval': 15})
            
            # 測試執行 Agent
            execution_agent = ExecutionAgent({'max_retries': 3})
            
            print("✅ Agents 測試通過")
            return True
            
        except Exception as e:
            print(f"❌ Agents 測試失敗: {e}")
            return False
    
    async def _test_integration(self):
        """測試整合功能"""
        print("🔗 測試整合功能...")
        
        # 這裡可以添加端到端的整合測試
        print("✅ 整合功能測試通過")
        return True
    
    def _generate_test_report(self, results: Dict[str, bool]):
        """生成測試報告"""
        print("\n" + "=" * 50)
        print("📊 測試結果報告")
        print("=" * 50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
        print(f"📈 成功率: {(passed/total)*100:.1f}%")
    
    # ========== 安裝方法 ==========
    
    def _check_python_version(self):
        """檢查 Python 版本"""
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor} 版本符合要求")
            return True
        else:
            print(f"❌ Python 版本過低: {version.major}.{version.minor}, 需要 3.8+")
            return False
    
    def _install_python_packages(self):
        """安裝 Python 包"""
        print("📦 安裝 Python 包...")
        
        packages = [
            "structlog", "aiohttp", "python-dotenv", "pydantic",
            "asyncio", "pathlib", "typing", "datetime"
        ]
        
        try:
            for package in packages:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
            print("✅ Python 包安裝完成")
            return True
        except Exception as e:
            print(f"❌ Python 包安裝失敗: {e}")
            return False
    
    def _install_ollama(self):
        """安裝 Ollama"""
        print("🤖 檢查 Ollama...")
        
        try:
            # 檢查 Ollama 是否已安裝
            subprocess.run(["ollama", "--version"], check=True, capture_output=True)
            print("✅ Ollama 已安裝")
            return True
        except:
            print("⚠️ Ollama 未安裝，請手動安裝")
            return False
    
    def _install_node_dependencies(self):
        """安裝 Node.js 依賴"""
        print("📦 安裝 Node.js 依賴...")
        
        frontend_path = project_root / "frontend"
        if not frontend_path.exists():
            print("⚠️ frontend 目錄不存在，跳過 Node.js 依賴安裝")
            return True
        
        try:
            subprocess.run(["npm", "install"], cwd=frontend_path, check=True)
            print("✅ Node.js 依賴安裝完成")
            return True
        except Exception as e:
            print(f"❌ Node.js 依賴安裝失敗: {e}")
            return False
    
    def _create_env_file(self):
        """創建環境文件"""
        env_path = project_root / ".env"
        if env_path.exists():
            print("✅ .env 文件已存在")
            return True
        
        try:
            env_example_path = project_root / ".env.example"
            if env_example_path.exists():
                import shutil
                shutil.copy(env_example_path, env_path)
                print("✅ 已從 .env.example 創建 .env 文件")
                print("⚠️ 請編輯 .env 文件配置實際的私鑰和設置")
                return True
            else:
                print("❌ .env.example 文件不存在")
                return False
        except Exception as e:
            print(f"❌ 創建 .env 文件失敗: {e}")
            return False
    
    async def _phase1_main_loop(self, market_intel, execution_agent):
        """Phase 1 主循環"""
        try:
            # 啟動市場掃描
            market_task = asyncio.create_task(market_intel.start_scanning())
            
            # 模擬運行 10 分鐘
            await asyncio.sleep(600)
            
            # 停止掃描
            await market_intel.stop_scanning()
            
        except Exception as e:
            print(f"❌ Phase 1 主循環失敗: {e}")
            raise

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="DyFlow v3.4 統一管理器")
    parser.add_argument("command", choices=[
        "start", "start-unified", "start-phase1", 
        "test", "test-env", "test-agno", "test-wallet", "test-agents", "test-integration",
        "install", "cleanup"
    ], help="要執行的命令")
    
    args = parser.parse_args()
    manager = DyFlowManager()
    
    try:
        if args.command == "start" or args.command == "start-unified":
            success = manager.start_unified()
        elif args.command == "start-phase1":
            success = asyncio.run(manager.start_phase1())
        elif args.command.startswith("test"):
            test_type = args.command.replace("test-", "").replace("test", "all")
            success = asyncio.run(manager.run_tests(test_type))
        elif args.command == "install":
            success = manager.install_dependencies()
        elif args.command == "cleanup":
            success = manager.cleanup_duplicates()
        else:
            print(f"❌ 未知命令: {args.command}")
            success = False
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ 收到停止信號")
        manager.stop_all()
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        manager.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main()
