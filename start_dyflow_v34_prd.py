#!/usr/bin/env python3
"""
DyFlow v3.4 PRD 完整啟動腳本
符合 PRD 規範的 8-Phase 啟動序列

PRD Section 4 - Startup Phases (Guard-Rail):
Phase 0: Config + Vault OK
Phase 1: Health Check (5 targets latency ≤ 300 ms, score ≥ 0.9)
Phase 2: UI Startup (Core → /health 200)
Phase 3: Wallet Test (≥ 1 keystore decrypt OK)
Phase 4: Onboarding (OnboardingAgent 完成 LaunchConfirmed)
Phase 5: Intel (bus.pool emits ≥ 1 event)
Phase 6: Portfolio (NAV lock acquired)
Phase 7: Strategy+Exec (首 Tx 成功)
Phase 8: Risk+Fee (IL_net & VaR within limits)
"""

import asyncio
import sys
import os
import signal
import structlog
from datetime import datetime
from typing import Dict, Any

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入 PRD 規範的 Agents
from backend.agents.core_agent import CoreAgent, get_core_agent
from backend.agents.supervisor_agent import SupervisorAgent
from backend.agents.health_guard_agent import HealthGuardAgent
from backend.agents.onboarding_agent import OnboardingAgent
from backend.agents.enhanced_market_intel_agent import EnhancedMarketIntelAgent
from backend.agents.strategy_agent import StrategyAgent
from backend.agents.execution_agent import ExecutionAgent
from backend.agents.risk_sentinel_agno import RiskSentinelAgnoAgent as RiskSentinelAgent
from backend.agents.fee_collector_agent import FeeCollectorAgent

logger = structlog.get_logger(__name__)

class DyFlowV34System:
    """
    DyFlow v3.4 系統管理器
    符合 PRD 規範的完整系統實現
    """
    
    def __init__(self):
        self.config = self._load_config()
        
        # PRD 規範的 9 個 Agents
        self.agents = {}
        self.core_agent = None
        self.supervisor = None
        
        # 系統狀態
        self.is_running = False
        self.startup_completed = False
        
        logger.info("dyflow_v34_system_initialized")
    
    def _load_config(self) -> Dict[str, Any]:
        """加載系統配置"""
        return {
            # 核心配置
            "api_host": "0.0.0.0",
            "api_port": 8001,
            
            # Agent 配置
            "bsc_scan_interval": 15,  # PRD: BSC 15s
            "sol_scan_interval": 12,  # PRD: SOL 12s
            "risk_check_interval": 10,  # PRD: 10s loop per position
            "fee_collection_enabled": True,
            
            # 健康檢查配置
            "health_targets": [
                {"name": "ollama", "url": "http://localhost:11434/api/tags"},
                {"name": "bsc_rpc", "url": "https://bsc-dataseed1.binance.org/"},
                {"name": "pancake_subgraph", "url": "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"},
                {"name": "meteora_api", "url": "https://dammv2-api.meteora.ag/pair/all"},
                {"name": "coingecko", "url": "https://api.coingecko.com/api/v3/ping"}
            ],
            
            # 風險管理配置
            "risk_limits": {
                "il_cut": -0.08,  # PRD: -8% IL 熔斷
                "var_cut": 0.04,  # PRD: VaR_95 > 4%
                "sigma_cut": 0.05
            }
        }
    
    async def initialize_agents(self):
        """初始化所有 Agents - PRD Section 3"""
        logger.info("initializing_prd_agents")
        
        try:
            # 1. CoreAgent - 唯一對外接口
            self.core_agent = get_core_agent()
            
            # 2. SupervisorAgent - 8-Phase 管理
            self.supervisor = SupervisorAgent(self.config)
            self.agents["supervisor"] = self.supervisor
            
            # 3. HealthGuardAgent - Phase 1
            self.agents["health_guard"] = HealthGuardAgent(self.config)
            
            # 4. OnboardingAgent - Phase 4
            self.agents["onboarding"] = OnboardingAgent(self.config)
            
            # 5. MarketIntelAgent - Phase 5
            self.agents["market_intel"] = EnhancedMarketIntelAgent(self.config)
            
            # 6. StrategyAgent - Phase 6
            self.agents["strategy"] = StrategyAgent()
            
            # 7. ExecutionAgent - Phase 7
            self.agents["execution"] = ExecutionAgent()
            
            # 8. RiskSentinelAgent - Phase 8
            self.agents["risk_sentinel"] = RiskSentinelAgent()
            
            # 9. FeeCollectorAgent - Phase 8
            self.agents["fee_collector"] = FeeCollectorAgent(self.config)
            
            # 設置 Agno 上下文 (模擬)
            mock_ctx = MockAgnoContext()
            for agent in self.agents.values():
                if hasattr(agent, 'set_context'):
                    agent.set_context(mock_ctx)
            
            logger.info("prd_agents_initialized", agent_count=len(self.agents))
            
        except Exception as e:
            logger.error("agent_initialization_failed", error=str(e))
            raise
    
    async def start_system(self):
        """啟動系統 - PRD Section 4 & 7"""
        logger.info("starting_dyflow_v34_system")
        
        try:
            # 初始化 Agents
            await self.initialize_agents()
            
            # 啟動 CoreAgent HTTP 服務器
            core_task = asyncio.create_task(
                self.core_agent.start_server(
                    host=self.config["api_host"],
                    port=self.config["api_port"]
                )
            )
            
            # 啟動 SupervisorAgent 8-Phase 序列
            supervisor_task = asyncio.create_task(
                self.supervisor.start_supervision()
            )
            
            # 啟動其他 Agents
            agent_tasks = []
            
            # HealthGuardAgent
            if "health_guard" in self.agents:
                task = asyncio.create_task(
                    self.agents["health_guard"].start_monitoring()
                )
                agent_tasks.append(task)
            
            # MarketIntelAgent
            if "market_intel" in self.agents:
                task = asyncio.create_task(
                    self.agents["market_intel"].start_scanning()
                )
                agent_tasks.append(task)
            
            # FeeCollectorAgent
            if "fee_collector" in self.agents:
                task = asyncio.create_task(
                    self.agents["fee_collector"].start_collection_loop()
                )
                agent_tasks.append(task)
            
            self.is_running = True
            
            logger.info("dyflow_v34_system_started",
                       core_port=self.config["api_port"],
                       agents_count=len(self.agents))
            
            # 等待所有任務
            await asyncio.gather(
                core_task,
                supervisor_task,
                *agent_tasks,
                return_exceptions=True
            )
            
        except Exception as e:
            logger.error("system_startup_failed", error=str(e))
            await self.stop_system()
            raise
    
    async def stop_system(self):
        """停止系統"""
        logger.info("stopping_dyflow_v34_system")
        
        self.is_running = False
        
        # 停止所有 Agents
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'stop_monitoring'):
                    await agent.stop_monitoring()
                elif hasattr(agent, 'stop_scanning'):
                    await agent.stop_scanning()
                elif hasattr(agent, 'stop_collection_loop'):
                    await agent.stop_collection_loop()
                elif hasattr(agent, 'stop_supervision'):
                    await agent.stop_supervision()
                    
                logger.info("agent_stopped", agent=agent_name)
                
            except Exception as e:
                logger.error("agent_stop_failed", agent=agent_name, error=str(e))
        
        logger.info("dyflow_v34_system_stopped")
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            "system": "DyFlow v3.4",
            "status": "running" if self.is_running else "stopped",
            "startup_completed": self.startup_completed,
            "current_phase": self.supervisor.current_phase.value if self.supervisor else 0,
            "agents": {
                name: agent.get_metrics() if hasattr(agent, 'get_metrics') else {"status": "unknown"}
                for name, agent in self.agents.items()
            },
            "timestamp": datetime.now().isoformat()
        }

class MockAgnoContext:
    """模擬 Agno 上下文"""
    
    async def publish(self, topic: str, data: Dict[str, Any]):
        """模擬發佈事件"""
        logger.info("event_published", topic=topic, data_keys=list(data.keys()))
    
    async def reply(self, status: str):
        """模擬回覆"""
        logger.info("agent_reply", status=status)

# 全局系統實例
dyflow_system = None

async def main():
    """主函數"""
    global dyflow_system
    
    # 設置日誌
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    logger.info("dyflow_v34_prd_startup_initiated")
    
    try:
        # 創建系統實例
        dyflow_system = DyFlowV34System()
        
        # 設置信號處理
        def signal_handler(signum, frame):
            logger.info("shutdown_signal_received", signal=signum)
            if dyflow_system:
                asyncio.create_task(dyflow_system.stop_system())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 啟動系統
        await dyflow_system.start_system()
        
    except KeyboardInterrupt:
        logger.info("keyboard_interrupt_received")
    except Exception as e:
        logger.error("system_error", error=str(e))
    finally:
        if dyflow_system:
            await dyflow_system.stop_system()

if __name__ == "__main__":
    asyncio.run(main())
