"""
DyFlow v3.3 Agno Workflow API
連接 React UI 到真實的 Agno Workflow
替代模擬數據，實現真實的 7 個 Agents + 8-phase 啟動序列
"""

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
import json
import structlog
from datetime import datetime
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.ollama import Ollama
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

# DyFlow 服務導入
try:
    from src.services.real_data_service import real_data_service
    REAL_DATA_AVAILABLE = True
except ImportError:
    REAL_DATA_AVAILABLE = False

logger = structlog.get_logger(__name__)

# ========== API 模型 ==========

class PhaseStatus(BaseModel):
    """階段狀態"""
    phase_id: int
    phase_name: str
    agent_name: str
    status: str  # pending, running, completed, failed
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    duration_seconds: Optional[float] = None
    progress_percentage: float = 0.0

class SystemStatus(BaseModel):
    """系統狀態"""
    overall_status: str
    current_phase: int
    total_phases: int = 9
    progress_percentage: float
    active_agents: List[str]
    phases: List[PhaseStatus]

class AgentInfo(BaseModel):
    """Agent 信息"""
    name: str
    status: str
    phase: int
    last_activity: Optional[str] = None
    message_count: int = 0

# ========== DyFlow Agno API ==========

class AgnoWorkflowAPI:
    """DyFlow v3.3 Agno API 服務"""
    
    def __init__(self):
        self.app = FastAPI(title="DyFlow v3.3 Agno API")
        
        # CORS 設置
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["http://localhost:3000"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 初始化狀態
        self.current_phase = 0
        self.workflow_running = False
        self.trading_mode = "paused"  # paused, exit_only, active
        self.agents_status = {}
        self.phase_results = []
        self.websocket_connections = []

        # 池子掃描狀態
        self.last_pool_data = {"bsc": [], "solana": []}
        self.pool_scan_running = False
        
        # 初始化 7 個 Agents (根據 PRD v3.3)
        self._initialize_agents()

        # 初始化主 DyFlow Agent 用於聊天對話
        self._initialize_main_dyflow_agent()

        # 設置路由
        self._setup_routes()
    
    def _initialize_agents(self):
        """初始化 7 個 DyFlow Agents"""
        if not AGNO_AVAILABLE:
            logger.warning("agno_not_available_using_mock")
            self._initialize_mock_agents()
            return
        
        try:
            # 根據 PRD v3.3 定義的 7 個 Agents
            agent_configs = [
                ("SupervisorAgent", 0, "System initialization and lifecycle management"),
                ("HealthGuardAgent", 1, "System health monitoring and validation"),
                ("MarketIntelAgent", 4, "Market data collection and pool scanning"),
                ("PortfolioManagerAgent", 5, "Portfolio management and NAV calculation"),
                ("StrategyAgent", 6, "LP strategy generation and optimization"),
                ("ExecutionAgent", 7, "Transaction execution and monitoring"),
                ("RiskSentinelAgent", 8, "Risk monitoring and portfolio protection")
            ]
            
            self.agents = {}
            
            for agent_name, phase, description in agent_configs:
                try:
                    agent = Agent(
                        name=agent_name,
                        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                        instructions=[
                            f"You are the {agent_name} for DyFlow v3.3 system.",
                            f"Responsible for Phase {phase}: {description}.",
                            "Provide brief status updates and coordinate with other agents.",
                            "This system uses Agno Framework instead of NATS message bus."
                        ]
                    )
                    
                    self.agents[agent_name] = agent
                    self.agents_status[agent_name] = {
                        "status": "initialized",
                        "phase": phase,
                        "last_activity": datetime.now().isoformat(),
                        "message_count": 0
                    }
                    
                except Exception as e:
                    logger.error("agent_initialization_failed", 
                               agent=agent_name, error=str(e))
            
            logger.info("dyflow_agents_initialized", 
                       count=len(self.agents),
                       agents=list(self.agents.keys()))
            
        except Exception as e:
            logger.error("agents_initialization_failed", error=str(e))
            self._initialize_mock_agents()
    
    def _initialize_mock_agents(self):
        """初始化模擬 Agents (當 Agno 不可用時)"""
        agent_names = [
            "SupervisorAgent", "HealthGuardAgent", "MarketIntelAgent",
            "PortfolioManagerAgent", "StrategyAgent", "ExecutionAgent", "RiskSentinelAgent"
        ]
        
        self.agents = {}
        for i, name in enumerate(agent_names):
            self.agents_status[name] = {
                "status": "mock",
                "phase": i if i < 2 else i + 2,  # 跳過 Phase 2, 3
                "last_activity": datetime.now().isoformat(),
                "message_count": 0
            }

    def _initialize_main_dyflow_agent(self):
        """初始化主 DyFlow Agent 用於聊天對話 (根據 PRD v3.4)"""
        if not AGNO_AVAILABLE:
            logger.warning("agno_not_available_core_agent_mock")
            self.core_agent = None
            return

        try:
            # 根據 PRD v3.4 創建 CoreAgent
            self.core_agent = Agent(
                name="CoreAgent",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "你是 DyFlow v3.4 系統的 CoreAgent，負責與用戶交互。",
                    "你管理整個 24/7 自動化 LP 策略系統，包括 BSC (PancakeSwap v3) 和 Solana (Meteora DLMM v2)。",
                    "你的職責包括：",
                    "1. 分析市場機會和池子數據",
                    "2. 管理 LP 持倉和投資組合",
                    "3. 執行風險管理和監控",
                    "4. 協調其他 Agents (OnboardingAgent, MarketIntelAgent, StrategyAgent, ExecutionAgent, RiskSentinelAgent)",
                    "5. 回答用戶關於系統狀態、交易策略、風險評估的問題",
                    "請用專業但友好的中文回答用戶問題，提供準確的市場分析和投資建議。",
                    "當用戶詢問系統狀態時，基於當前的 agents_status 和 trading_mode 提供實時信息。",
                    "支持的交易模式：active (活躍交易), exit_only (僅退出), paused (暫停)。",
                    "風險管理：IL 熔斷線 -8%, VaR 95% 熔斷線 4%。"
                ]
            )

            logger.info("core_agent_initialized", model="qwen2.5:3b")

        except Exception as e:
            logger.error("core_agent_initialization_failed", error=str(e))
            self.core_agent = None
    
    def _setup_routes(self):
        """設置 API 路由"""
        
        @self.app.get("/api/system/status")
        async def get_system_status():
            """獲取系統狀態"""
            try:
                # 計算進度
                completed_phases = len([p for p in self.phase_results if p.get("status") == "completed"])
                progress = (completed_phases / 9) * 100
                
                # 確定整體狀態
                if self.workflow_running:
                    overall_status = "running"
                elif completed_phases == 9:
                    overall_status = "completed"
                elif any(p.get("status") == "failed" for p in self.phase_results):
                    overall_status = "failed"
                else:
                    overall_status = "idle"
                
                # 生成階段狀態
                phases = []
                phase_names = [
                    "System Initialization", "Health Check", "UI Startup", "Wallet Test",
                    "Market Intelligence", "Portfolio Management", "Strategy Generation",
                    "Transaction Execution", "Risk Monitoring"
                ]
                
                agent_mapping = {
                    0: "SupervisorAgent", 1: "HealthGuardAgent", 2: "WebUI", 3: "WalletProbe",
                    4: "MarketIntelAgent", 5: "PortfolioManagerAgent", 6: "StrategyAgent",
                    7: "ExecutionAgent", 8: "RiskSentinelAgent"
                }
                
                for i in range(9):
                    phase_result = next((p for p in self.phase_results if p.get("phase_id") == i), None)
                    
                    if phase_result:
                        status = phase_result["status"]
                        started_at = phase_result.get("started_at")
                        completed_at = phase_result.get("completed_at")
                        duration = phase_result.get("duration_seconds")
                    else:
                        status = "running" if i == self.current_phase else "pending"
                        started_at = datetime.now().isoformat() if i == self.current_phase else None
                        completed_at = None
                        duration = None
                    
                    phases.append(PhaseStatus(
                        phase_id=i,
                        phase_name=phase_names[i],
                        agent_name=agent_mapping[i],
                        status=status,
                        started_at=started_at,
                        completed_at=completed_at,
                        duration_seconds=duration,
                        progress_percentage=(i / 9) * 100 if status == "completed" else 0
                    ))
                
                return SystemStatus(
                    overall_status=overall_status,
                    current_phase=self.current_phase,
                    total_phases=9,
                    progress_percentage=progress,
                    active_agents=list(self.agents_status.keys()),
                    phases=phases
                )
                
            except Exception as e:
                logger.error("get_system_status_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/agents")
        async def get_agents():
            """獲取 Agents 狀態"""
            try:
                agents_info = []
                for name, status in self.agents_status.items():
                    agents_info.append(AgentInfo(
                        name=name,
                        status=status["status"],
                        phase=status["phase"],
                        last_activity=status["last_activity"],
                        message_count=status["message_count"]
                    ))
                
                return {"agents": agents_info, "total": len(agents_info)}
                
            except Exception as e:
                logger.error("get_agents_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/workflow/start")
        async def start_workflow():
            """啟動 DyFlow v3.3 完整工作流程"""
            try:
                if self.workflow_running:
                    return {"message": "DyFlow workflow already running", "status": "running"}

                logger.info("dyflow_workflow_start_requested")

                # 重置狀態
                self.workflow_running = True
                self.current_phase = 0
                self.phase_results = []

                # 更新所有 Agent 狀態為啟動中
                for agent_name in self.agents_status:
                    self.agents_status[agent_name]["status"] = "starting"
                    self.agents_status[agent_name]["last_activity"] = datetime.now().isoformat()

                # 啟動完整的 8-phase 序列
                asyncio.create_task(self._run_complete_workflow())

                # 廣播啟動事件
                await self._broadcast_workflow_event("workflow_started", {
                    "message": "DyFlow v3.3 Agent 系統啟動",
                    "total_phases": 9,
                    "active_agents": list(self.agents_status.keys())
                })

                return {
                    "message": "DyFlow v3.3 workflow started successfully",
                    "status": "running",
                    "total_phases": 9,
                    "active_agents": len(self.agents_status)
                }

            except Exception as e:
                logger.error("start_workflow_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/workflow/stop")
        async def stop_workflow():
            """停止 Workflow"""
            try:
                self.workflow_running = False
                return {"message": "Workflow stopped", "status": "stopped"}
                
            except Exception as e:
                logger.error("stop_workflow_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/trading_mode")
        async def set_trading_mode(request: dict):
            """設置交易模式 - DyFlow 核心控制"""
            try:
                mode = request.get("mode", "paused")

                if mode not in ["active", "exit_only", "paused"]:
                    raise HTTPException(status_code=400, detail="Invalid trading mode")

                self.trading_mode = mode

                # 廣播模式變更到所有 Agent
                await self._broadcast_workflow_event("trading_mode_changed", {
                    "mode": mode,
                    "message": f"交易模式已變更為: {mode}",
                    "timestamp": datetime.now().isoformat()
                })

                logger.info("trading_mode_changed", mode=mode)

                return {
                    "message": f"Trading mode set to {mode}",
                    "mode": mode,
                    "status": "success"
                }

            except Exception as e:
                logger.error("set_trading_mode_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/pools")
        async def get_pools():
            """獲取池子數據 - MarketIntelAgent 掃描結果"""
            try:
                # 如果有真實數據，使用緩存的池子數據
                if REAL_DATA_AVAILABLE and self.last_pool_data:
                    bsc_pools = self.last_pool_data.get("bsc", [])
                    sol_pools = self.last_pool_data.get("solana", [])

                    # 合併並格式化池子數據
                    all_pools = []

                    for pool in bsc_pools:
                        formatted_pool = {
                            "id": pool.get("pool_id", ""),
                            "chain": "bsc",
                            "protocol": "pancakeswap_v3",
                            "pair": pool.get("pair", ""),
                            "tvl_usd": pool.get("tvl_usd", 0),
                            "apr": pool.get("fee_apr", 0),
                            "fees_24h": pool.get("fees_24h", 0),
                            "fee_tvl_ratio": pool.get("fee_tvl_ratio", 0),
                            "risk_level": pool.get("risk_level", "medium"),
                            "strategy_recommendation": pool.get("strategy_type", "SPOT_BALANCED"),
                            "scan_time": pool.get("last_update", datetime.now().isoformat())
                        }
                        all_pools.append(formatted_pool)

                    for pool in sol_pools:
                        formatted_pool = {
                            "id": pool.get("pool_id", ""),
                            "chain": "solana",
                            "protocol": "meteora_dlmm_v2",
                            "pair": pool.get("pair", ""),
                            "tvl_usd": pool.get("tvl_usd", 0),
                            "apr": pool.get("fee_apr", 0),
                            "fees_24h": pool.get("fees_24h", 0),
                            "fee_tvl_ratio": pool.get("fee_tvl_ratio", 0),
                            "risk_level": pool.get("risk_level", "medium"),
                            "strategy_recommendation": pool.get("strategy_type", "SPOT_IMBALANCED_DAMM"),
                            "scan_time": pool.get("last_update", datetime.now().isoformat())
                        }
                        all_pools.append(formatted_pool)

                    return {
                        "pools": all_pools,
                        "total": len(all_pools),
                        "bsc_count": len(bsc_pools),
                        "sol_count": len(sol_pools),
                        "scan_timestamp": datetime.now().isoformat(),
                        "market_intel_agent": "active",
                        "data_source": "real_apis"
                    }

                # Fallback 到模擬數據
                mock_pools = [
                    {
                        "id": "bsc_pool_1",
                        "chain": "bsc",
                        "protocol": "pancakeswap_v3",
                        "pair": "BNB/USDT",
                        "tvl_usd": 15000000,
                        "apr": 125.5,
                        "fees_24h": 75000,
                        "fee_tvl_ratio": 0.05,
                        "risk_level": "medium",
                        "strategy_recommendation": "CURVE_BALANCED",
                        "scan_time": datetime.now().isoformat()
                    },
                    {
                        "id": "sol_pool_1",
                        "chain": "solana",
                        "protocol": "meteora_dlmm_v2",
                        "pair": "SOL/USDC",
                        "tvl_usd": 12000000,
                        "apr": 185.2,
                        "fees_24h": 60000,
                        "fee_tvl_ratio": 0.08,
                        "risk_level": "high",
                        "strategy_recommendation": "SPOT_IMBALANCED_DAMM",
                        "scan_time": datetime.now().isoformat()
                    }
                ]

                return {
                    "pools": mock_pools,
                    "total": len(mock_pools),
                    "scan_timestamp": datetime.now().isoformat(),
                    "market_intel_agent": "mock",
                    "data_source": "mock"
                }

            except Exception as e:
                logger.error("get_pools_failed", error=str(e))
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/agent/chat")
        async def agent_chat(request: dict):
            """DyFlow CoreAgent 聊天對話 (根據 PRD v3.4)"""
            try:
                message = request.get("message", "").strip()
                if not message:
                    raise HTTPException(status_code=400, detail="Message is required")

                logger.info("agent_chat_request", message=message[:100])

                # 如果 CoreAgent 不可用，使用 fallback 回應
                if not self.core_agent:
                    fallback_response = self._generate_fallback_response(message)
                    return {
                        "response": fallback_response,
                        "agent": "CoreAgent (Fallback)",
                        "timestamp": datetime.now().isoformat(),
                        "mode": "fallback"
                    }

                # 構建上下文信息
                context = self._build_agent_context()

                # 構建完整的提示
                full_prompt = f"""
當前系統狀態：
- 交易模式: {self.trading_mode}
- 工作流程運行: {'是' if self.workflow_running else '否'}
- 當前階段: {self.current_phase}/9
- 活躍 Agents: {len(self.agents_status)}

用戶問題: {message}

請基於當前系統狀態回答用戶問題。
"""

                # 調用 CoreAgent (同步調用，不需要 await)
                response = self.core_agent.run(full_prompt)

                logger.info("agent_chat_response",
                           message_length=len(message),
                           response_length=len(str(response)))

                return {
                    "response": response.content if hasattr(response, 'content') else str(response),
                    "agent": "CoreAgent",
                    "timestamp": datetime.now().isoformat(),
                    "mode": "agno",
                    "context": context
                }

            except Exception as e:
                logger.error("agent_chat_failed", error=str(e))

                # 返回錯誤回應而不是拋出異常
                return {
                    "response": f"抱歉，我暫時無法處理您的請求。錯誤信息：{str(e)}",
                    "agent": "CoreAgent (Error)",
                    "timestamp": datetime.now().isoformat(),
                    "mode": "error"
                }

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket 連接 - 實時 Agent 通訊"""
            await websocket.accept()
            self.websocket_connections.append(websocket)

            logger.info("websocket_connected",
                       total_connections=len(self.websocket_connections))

            try:
                # 發送初始狀態
                await self._send_initial_status(websocket)

                while True:
                    # 接收客戶端消息
                    try:
                        data = await asyncio.wait_for(websocket.receive_text(), timeout=1.0)
                        message = json.loads(data)

                        # 處理客戶端命令
                        await self._handle_websocket_message(websocket, message)

                    except asyncio.TimeoutError:
                        # 定期發送狀態更新
                        await self._send_status_update(websocket)

                    except json.JSONDecodeError:
                        logger.warning("invalid_websocket_message", data=data)

            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
                logger.info("websocket_disconnected",
                           remaining_connections=len(self.websocket_connections))

    async def _send_initial_status(self, websocket: WebSocket):
        """發送初始狀態到新連接的 WebSocket"""
        try:
            status_data = {
                "overall_status": "running" if self.workflow_running else "idle",
                "current_phase": self.current_phase,
                "trading_mode": self.trading_mode,
                "active_agents": list(self.agents_status.keys()),
                "phase_results": self.phase_results,
                "agents_status": self.agents_status
            }

            await websocket.send_text(json.dumps({
                "type": "initial_status",
                "data": status_data,
                "timestamp": datetime.now().isoformat()
            }))

        except Exception as e:
            logger.error("send_initial_status_failed", error=str(e))

    async def _send_status_update(self, websocket: WebSocket):
        """發送狀態更新到 WebSocket"""
        try:
            completed_phases = len([p for p in self.phase_results if p.get("status") == "completed"])
            progress = (completed_phases / 9) * 100

            status_data = {
                "overall_status": "running" if self.workflow_running else ("completed" if completed_phases == 9 else "idle"),
                "current_phase": self.current_phase,
                "progress_percentage": progress,
                "trading_mode": self.trading_mode,
                "active_agents": list(self.agents_status.keys()),
                "phase_results": self.phase_results,
                "agents_status": self.agents_status
            }

            await websocket.send_text(json.dumps({
                "type": "status_update",
                "data": status_data,
                "timestamp": datetime.now().isoformat()
            }))

        except Exception as e:
            logger.error("send_status_update_failed", error=str(e))

    async def _handle_websocket_message(self, websocket: WebSocket, message: dict):
        """處理 WebSocket 消息"""
        try:
            message_type = message.get("type")

            if message_type == "force_update":
                # 強制更新狀態
                await self._send_status_update(websocket)

            elif message_type == "agent_command":
                # 處理 Agent 命令
                command = message.get("command")

                if command == "start_workflow":
                    if not self.workflow_running:
                        asyncio.create_task(self._run_complete_workflow())

                elif command == "stop_workflow":
                    self.workflow_running = False

                elif command == "set_trading_mode":
                    mode = message.get("mode", "paused")
                    self.trading_mode = mode
                    await self._broadcast_workflow_event("trading_mode_changed", {
                        "mode": mode,
                        "message": f"交易模式已變更為: {mode}"
                    })

                elif command == "emergency_exit":
                    # 緊急退出所有持倉
                    await self._broadcast_workflow_event("emergency_exit", {
                        "message": "緊急退出指令已發出",
                        "action": "exit_all_positions"
                    })

                # 發送命令響應
                await websocket.send_text(json.dumps({
                    "type": "command_response",
                    "command": command,
                    "status": "executed",
                    "timestamp": datetime.now().isoformat()
                }))

            else:
                logger.warning("unknown_websocket_message_type", type=message_type)

        except Exception as e:
            logger.error("handle_websocket_message_failed", error=str(e))

    def _generate_fallback_response(self, message: str) -> str:
        """生成 fallback 回應 (當 Agno 不可用時)"""
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in ['狀態', '系統', 'status']):
            return f"""📊 **DyFlow v3.4 系統狀態**

🔄 **工作流程**: {'運行中' if self.workflow_running else '待機中'}
⚙️ **交易模式**: {self.trading_mode}
📈 **當前階段**: {self.current_phase}/9
🤖 **活躍 Agents**: {len(self.agents_status)}

系統正在使用 fallback 模式運行，建議檢查 Ollama 服務是否正常運行。"""

        elif any(keyword in message_lower for keyword in ['池子', '掃描', 'pool', 'scan']):
            return """🔍 **池子掃描功能**

MarketIntelAgent 負責掃描 BSC 和 Solana 的高質量池子：
• BSC: PancakeSwap V3 池子
• Solana: Meteora DLMM v2 池子

篩選條件：TVL >= $10M, Fee/TVL >= 5%, 24h費用 > $5

請使用 "一鍵啟動" 來激活完整的池子掃描功能。"""

        elif any(keyword in message_lower for keyword in ['風險', 'risk', '監控']):
            return """🛡️ **風險管理系統**

RiskSentinelAgent 提供全面的風險監控：
• IL 熔斷線: -8% (自動退出)
• VaR 95% 熔斷線: 4%
• 實時持倉監控
• 自動風險評估

系統會在風險超標時自動觸發退出機制。"""

        else:
            return f"""🤖 **DyFlow v3.4 AI Assistant**

收到您的指令："{message}"

我是 DyFlow 系統的 CoreAgent，負責管理整個 24/7 自動化 LP 策略系統。

**主要功能**：
• 🔍 市場機會分析
• 💼 LP 持倉管理
• 🛡️ 風險監控
• ⚙️ 系統協調

目前系統運行在 fallback 模式，建議檢查 Ollama qwen2.5:3b 服務狀態。"""

    def _build_agent_context(self) -> dict:
        """構建 Agent 上下文信息"""
        return {
            "trading_mode": self.trading_mode,
            "workflow_running": self.workflow_running,
            "current_phase": self.current_phase,
            "total_phases": 9,
            "active_agents": len(self.agents_status),
            "agents_status": {name: status["status"] for name, status in self.agents_status.items()},
            "completed_phases": len([p for p in self.phase_results if p.get("status") == "completed"]),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _run_complete_workflow(self):
        """運行完整的 DyFlow v3.3 工作流程"""
        try:
            logger.info("dyflow_complete_workflow_started")

            # 8-phase 啟動序列
            phase_configs = [
                ("SupervisorAgent", "System Initialization", "讀取 YAML/ENV 配置，初始化 Vault 密鑰分發"),
                ("HealthGuardAgent", "Health Check", "檢查 RPC/Subgraph/DB/UI 健康狀態，要求 ≥ 90% 健康"),
                ("WebUI", "UI Startup", "啟動 WebUI 和 PrometheusExporter"),
                ("WalletProbe", "Wallet Test", "執行 MPC 簽名和 nonce 測試"),
                ("MarketIntelAgent", "Market Intelligence", "啟動市場情報收集，掃描 BSC 和 Solana 池子"),
                ("PortfolioManagerAgent", "Portfolio Management", "初始化投資組合管理，確保 NAV ≥ 0"),
                ("StrategyAgent", "Strategy Generation", "生成 LP 策略計劃，產生 LPPlan.approved"),
                ("ExecutionAgent", "Transaction Execution", "執行交易，確保至少 1 筆交易成功廣播"),
                ("RiskSentinelAgent", "Risk Monitoring", "啟動風險監控，確保 IL_net 和 VaR 均在限制範圍內")
            ]

            for phase_id, (agent_name, phase_name, description) in enumerate(phase_configs):
                if not self.workflow_running:
                    logger.info("workflow_stopped_by_user", phase=phase_id)
                    break

                self.current_phase = phase_id

                # 開始階段
                phase_result = {
                    "phase_id": phase_id,
                    "phase_name": phase_name,
                    "agent_name": agent_name,
                    "status": "running",
                    "started_at": datetime.now().isoformat(),
                    "description": description
                }

                logger.info("phase_started",
                           phase_id=phase_id,
                           phase_name=phase_name,
                           agent=agent_name)

                # 更新 Agent 狀態
                if agent_name in self.agents_status:
                    self.agents_status[agent_name]["status"] = "active"
                    self.agents_status[agent_name]["last_activity"] = datetime.now().isoformat()

                # 廣播階段開始事件
                await self._broadcast_workflow_event("phase_started", {
                    "phase_id": phase_id,
                    "phase_name": phase_name,
                    "agent_name": agent_name,
                    "description": description
                })

                # 執行階段邏輯
                success = await self._execute_phase_logic(phase_id, agent_name, phase_name)

                # 完成階段
                phase_result.update({
                    "status": "completed" if success else "failed",
                    "completed_at": datetime.now().isoformat(),
                    "duration_seconds": 3.0,
                    "success": success
                })

                self.phase_results.append(phase_result)

                # 更新 Agent 狀態
                if agent_name in self.agents_status:
                    self.agents_status[agent_name]["status"] = "completed" if success else "failed"
                    self.agents_status[agent_name]["message_count"] += 1

                # 廣播階段完成事件
                await self._broadcast_workflow_event("phase_completed", {
                    "phase_id": phase_id,
                    "phase_name": phase_name,
                    "agent_name": agent_name,
                    "success": success,
                    "duration": 3.0
                })

                if not success:
                    logger.error("phase_failed", phase_id=phase_id, agent=agent_name)
                    break

                logger.info("phase_completed",
                           phase_id=phase_id,
                           phase_name=phase_name,
                           success=success)

            # 工作流程完成
            self.workflow_running = False

            # 廣播完成事件
            await self._broadcast_workflow_event("workflow_completed", {
                "message": "DyFlow v3.3 工作流程完成",
                "total_phases": len(phase_configs),
                "completed_phases": len([p for p in self.phase_results if p["status"] == "completed"]),
                "success": all(p["status"] == "completed" for p in self.phase_results)
            })

            logger.info("dyflow_complete_workflow_finished",
                       total_phases=len(phase_configs),
                       success_count=len([p for p in self.phase_results if p["status"] == "completed"]))

        except Exception as e:
            logger.error("workflow_execution_failed", error=str(e))
            self.workflow_running = False

            # 廣播錯誤事件
            await self._broadcast_workflow_event("workflow_error", {
                "error": str(e),
                "phase": self.current_phase
            })
    
    async def _execute_phase_logic(self, phase_id: int, agent_name: str, phase_name: str) -> bool:
        """執行具體的階段邏輯"""
        try:
            # 模擬階段執行時間
            await asyncio.sleep(2)

            # 根據階段執行不同邏輯
            if phase_id == 0:  # SupervisorAgent - 系統初始化
                logger.info("supervisor_initializing_system")
                return True

            elif phase_id == 1:  # HealthGuardAgent - 健康檢查
                logger.info("health_guard_checking_connections")
                # 模擬健康檢查
                return True

            elif phase_id == 4:  # MarketIntelAgent - 市場情報
                logger.info("market_intel_scanning_pools")
                # 模擬池子掃描
                return True

            elif phase_id == 5:  # PortfolioManagerAgent - 投資組合管理
                logger.info("portfolio_manager_calculating_nav")
                # 模擬 NAV 計算
                return True

            elif phase_id == 6:  # StrategyAgent - 策略生成
                logger.info("strategy_agent_generating_plans")
                # 模擬策略生成
                return True

            elif phase_id == 7:  # ExecutionAgent - 交易執行
                logger.info("execution_agent_processing_transactions")
                # 模擬交易執行
                return True

            elif phase_id == 8:  # RiskSentinelAgent - 風險監控
                logger.info("risk_sentinel_monitoring_positions")
                # 模擬風險監控
                return True

            else:
                # 其他階段（UI、錢包測試等）
                return True

        except Exception as e:
            logger.error("phase_execution_failed",
                        phase_id=phase_id,
                        agent=agent_name,
                        error=str(e))
            return False

    async def _broadcast_workflow_event(self, event_type: str, data: dict):
        """廣播工作流程事件到所有 WebSocket 連接"""
        if not self.websocket_connections:
            return

        try:
            message = json.dumps({
                "type": event_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            })

            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message)
                except:
                    self.websocket_connections.remove(websocket)

        except Exception as e:
            logger.error("broadcast_workflow_event_failed",
                        event_type=event_type,
                        error=str(e))

    async def _broadcast_update(self):
        """廣播狀態更新到所有 WebSocket 連接"""
        if not self.websocket_connections:
            return

        try:
            # 獲取當前系統狀態
            completed_phases = len([p for p in self.phase_results if p.get("status") == "completed"])
            progress = (completed_phases / 9) * 100

            status_data = {
                "overall_status": "running" if self.workflow_running else "completed",
                "current_phase": self.current_phase,
                "progress_percentage": progress,
                "active_agents": list(self.agents_status.keys()),
                "phase_results": self.phase_results,
                "agents_status": self.agents_status
            }

            message = json.dumps({
                "type": "status_update",
                "data": status_data,
                "timestamp": datetime.now().isoformat()
            })

            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message)
                except:
                    self.websocket_connections.remove(websocket)

        except Exception as e:
            logger.error("broadcast_update_failed", error=str(e))

    async def start_pool_scanning(self):
        """啟動池子掃描任務 (在事件循環中調用)"""
        if not REAL_DATA_AVAILABLE:
            logger.warning("real_data_service_not_available_skipping_pool_scan")
            return

        # 啟動後台任務
        asyncio.create_task(self._pool_scanning_loop())
        logger.info("pool_scanning_task_started")

    async def _pool_scanning_loop(self):
        """池子掃描循環 - 每30秒掃描一次"""
        self.pool_scan_running = True

        while self.pool_scan_running:
            try:
                # 獲取最新的池子數據
                all_pools = await real_data_service.get_all_pools()

                # 更新緩存
                self.last_pool_data = all_pools

                # 廣播池子數據更新
                await self._broadcast_pool_data_update()

                # 等待 30 秒
                await asyncio.sleep(30)

            except Exception as e:
                logger.error("pool_scanning_loop_error", error=str(e))
                await asyncio.sleep(10)

    async def _broadcast_pool_data_update(self):
        """廣播池子數據更新到所有 WebSocket 連接"""
        try:
            bsc_pools = self.last_pool_data.get("bsc", [])
            sol_pools = self.last_pool_data.get("solana", [])

            # 轉換為前端格式
            formatted_bsc_pools = []
            for pool in bsc_pools[:20]:  # 只發送前 20 個
                formatted_pool = {
                    "id": pool.get("pool_id", ""),
                    "chain": "bsc",
                    "pair": pool.get("pair", ""),
                    "tvl_usd": pool.get("tvl_usd", 0),
                    "volume_24h": pool.get("volume_24h", 0),
                    "fees_24h": pool.get("fees_24h", 0),
                    "fee_apr": pool.get("fee_apr", 0),
                    "total_apr": pool.get("total_apr", 0),
                    "risk_level": pool.get("risk_level", "medium"),
                    "strategy_type": pool.get("strategy_type", "SPOT_BALANCED"),
                    "last_update": pool.get("last_update", "")
                }
                formatted_bsc_pools.append(formatted_pool)

            formatted_sol_pools = []
            for pool in sol_pools[:20]:  # 只發送前 20 個
                formatted_pool = {
                    "id": pool.get("pool_id", ""),
                    "chain": "solana",
                    "pair": pool.get("pair", ""),
                    "tvl_usd": pool.get("tvl_usd", 0),
                    "volume_24h": pool.get("volume_24h", 0),
                    "fees_24h": pool.get("fees_24h", 0),
                    "fee_apr": pool.get("fee_apr", 0),
                    "total_apr": pool.get("total_apr", 0),
                    "risk_level": pool.get("risk_level", "medium"),
                    "strategy_type": pool.get("strategy_type", "SPOT_IMBALANCED_DAMM"),
                    "last_update": pool.get("last_update", "")
                }
                formatted_sol_pools.append(formatted_pool)

            # 構建廣播消息
            message = {
                "type": "pool_data_update",
                "data": {
                    "bsc_pools": formatted_bsc_pools,
                    "sol_pools": formatted_sol_pools,
                    "bsc_count": len(bsc_pools),
                    "sol_count": len(sol_pools),
                    "timestamp": datetime.now().isoformat()
                }
            }

            # 廣播到所有 WebSocket 連接
            message_json = json.dumps(message)
            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message_json)
                except:
                    self.websocket_connections.remove(websocket)

            logger.info("pool_data_broadcasted",
                       bsc_pools=len(formatted_bsc_pools),
                       sol_pools=len(formatted_sol_pools),
                       clients=len(self.websocket_connections))

        except Exception as e:
            logger.error("broadcast_pool_data_failed", error=str(e))

# ========== 啟動服務 ==========

def create_app():
    """創建 FastAPI 應用"""
    api = AgnoWorkflowAPI()
    return api.app

async def start_app_with_pool_scanning():
    """啟動應用並開始池子掃描"""
    api = AgnoWorkflowAPI()
    await api.start_pool_scanning()
    return api.app

if __name__ == "__main__":
    import uvicorn

    print("🚀 啟動 DyFlow v3.3 Agno Workflow API")
    print("✅ 7 個 Agents (根據 PRD v3.3)")
    print("✅ 8-phase 啟動序列")
    print("❌ 移除 NATS 依賴")
    print("🌐 API: http://localhost:8001")

    # 創建 API 實例
    api_instance = AgnoWorkflowAPI()
    app = api_instance.app

    # 設置啟動事件
    @app.on_event("startup")
    async def startup_event():
        """應用啟動時執行"""
        await api_instance.start_pool_scanning()

    uvicorn.run(app, host="0.0.0.0", port=8001)
