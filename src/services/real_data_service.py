#!/usr/bin/env python3
"""
DyFlow v3.3 真實數據服務
整合 PancakeSwap V3 Subgraph、Meteora DLMM v2 API 和 CoinGecko API
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

logger = structlog.get_logger(__name__)

class RealDataService:
    """真實數據服務 - 獲取真實的池子和價格數據"""
    
    def __init__(self):
        self.name = "RealDataService"
        
        # API 端點配置
        self.endpoints = {
            "pancake_subgraph": "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ",
            "meteora_dlmm_api": "https://dlmm-api.meteora.ag",
            "meteora_damm_api": "https://dammv2-api.meteora.ag",
            "coingecko_api": "https://api.coingecko.com/api/v3"
        }
        
        # API 密鑰
        self.api_keys = {
            "thegraph": "9731921233db132a98c2325878e6c153"
        }
        
        # 緩存
        self.cache = {
            "bsc_pools": [],
            "sol_pools": [],
            "prices": {},
            "last_update": None
        }
        
        logger.info("real_data_service_initialized")
    
    async def get_bsc_pools(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """獲取 BSC PancakeSwap V3 真實池子數據"""
        logger.info("fetching_bsc_pools", get_all=True)
        
        # GraphQL 查詢 - 修復變量使用
        query = """
        query GetPools($first: Int!) {
          pools(
            first: $first
            orderBy: totalValueLockedUSD
            orderDirection: desc
            where: {
              totalValueLockedUSD_gt: "1000000"
            }
          ) {
            id
            token0 {
              id
              symbol
              name
              decimals
            }
            token1 {
              id
              symbol
              name
              decimals
            }
            feeTier
            totalValueLockedUSD
            volumeUSD
            feesUSD
            txCount
            createdAtTimestamp
            poolDayData(first: 1, orderBy: date, orderDirection: desc) {
              date
              volumeUSD
              feesUSD
              tvlUSD
            }
          }
        }
        """
        
        variables = {"first": limit or 100}
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_keys['thegraph']}"
                }
                
                payload = {
                    "query": query,
                    "variables": variables
                }
                
                async with session.post(
                    self.endpoints["pancake_subgraph"],
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data.get("data", {}).get("pools", [])
                        
                        # 處理池子數據
                        processed_pools = []
                        for pool in pools:
                            processed_pool = await self._process_bsc_pool(pool)
                            if processed_pool:
                                processed_pools.append(processed_pool)
                        
                        self.cache["bsc_pools"] = processed_pools
                        logger.info("bsc_pools_fetched", count=len(processed_pools))
                        return processed_pools
                    else:
                        logger.error("bsc_pools_fetch_failed", status=response.status)
                        return self.cache["bsc_pools"]  # 返回緩存數據
                        
        except Exception as e:
            logger.error("bsc_pools_fetch_error", error=str(e))
            return self.cache["bsc_pools"]  # 返回緩存數據
    
    async def _process_bsc_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 BSC 池子數據"""
        try:
            token0 = pool.get("token0")
            token1 = pool.get("token1")

            # 檢查必要字段
            if not token0 or not token1:
                return None
            
            # 計算基本指標
            tvl_usd = float(pool["totalValueLockedUSD"])
            volume_24h = float(pool.get("volumeUSD", 0))
            fees_24h = float(pool.get("feesUSD", 0))
            
            # 獲取最新日數據
            day_data = pool.get("poolDayData", [])
            if day_data:
                latest_day = day_data[0]
                volume_24h = float(latest_day.get("volumeUSD", volume_24h))
                fees_24h = float(latest_day.get("feesUSD", fees_24h))
            
            # 計算 APR
            fee_apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
            
            # 計算風險等級
            risk_level = "low"
            if fee_apr > 100:
                risk_level = "high"
            elif fee_apr > 50:
                risk_level = "medium"
            
            # 計算波動率 (簡化)
            volatility = min(fee_apr / 100, 1.0)  # 基於 APR 估算波動率
            
            return {
                "pool_id": pool["id"],
                "chain": "bsc",
                "pair": f"{token0['symbol']}/{token1['symbol']}",
                "token0": token0,
                "token1": token1,
                "fee_tier": int(pool["feeTier"]) / 10000,  # 轉換為百分比
                "tvl_usd": tvl_usd,
                "volume_24h": volume_24h,
                "fees_24h": fees_24h,
                "fee_apr": fee_apr,
                "total_apr": fee_apr,  # 簡化，只考慮手續費 APR
                "risk_level": risk_level,
                "volatility": volatility,
                "tx_count": int(pool.get("txCount", 0)),
                "created_at": int(pool.get("createdAtTimestamp", 0)),
                "fee_tvl_ratio": (fees_24h / tvl_usd * 100) if tvl_usd > 0 else 0,
                "strategy_type": self._determine_strategy_type(token0["symbol"], token1["symbol"]),
                "last_update": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("bsc_pool_processing_error", pool_id=pool.get("id"), error=str(e))
            return None
    
    async def get_sol_pools(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """獲取 Solana Meteora DLMM v2 真實池子數據"""
        logger.info("fetching_sol_pools", get_all=True)
        
        try:
            async with aiohttp.ClientSession() as session:
                # 獲取所有 DLMM 池子
                async with session.get(
                    f"{self.endpoints['meteora_dlmm_api']}/pair/all",
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Meteora DLMM API 直接返回池子數組
                        pools = data if isinstance(data, list) else []
                        
                        # 過濾和排序
                        filtered_pools = []
                        for pool in pools:
                            if self._should_include_sol_pool(pool):
                                processed_pool = await self._process_sol_pool(pool)
                                if processed_pool:
                                    filtered_pools.append(processed_pool)
                        
                        # 按 TVL 排序
                        filtered_pools.sort(key=lambda x: x["tvl_usd"], reverse=True)

                        # 如果指定了限制，則應用限制
                        if limit:
                            result = filtered_pools[:limit]
                        else:
                            result = filtered_pools

                        self.cache["sol_pools"] = result
                        logger.info("sol_pools_fetched", count=len(result), total_available=len(filtered_pools))
                        return result
                    else:
                        logger.error("sol_pools_fetch_failed", status=response.status)
                        return self.cache["sol_pools"]
                        
        except Exception as e:
            logger.error("sol_pools_fetch_error", error=str(e))
            return self.cache["sol_pools"]
    
    def _should_include_sol_pool(self, pool: Dict[str, Any]) -> bool:
        """判斷是否應該包含此 SOL 池子"""
        try:
            # 檢查 TVL - 降低門檻
            tvl = float(pool.get("liquidity", 0))
            if tvl < 10000:  # 至少 $10K TVL
                return False

            # 檢查是否有交易量
            volume_24h = float(pool.get("trade_volume_24h", 0))
            if volume_24h < 1000:  # 至少 $1K 24h 交易量
                return False

            # 檢查是否為主要代幣對 - 更寬鬆的條件
            token_x = pool.get("mint_x", "")
            token_y = pool.get("mint_y", "")

            # 主要代幣地址 (Solana)
            major_tokens = {
                "So11111111111111111111111111111111111111112",  # SOL
                "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
            }

            # 至少包含一個主要代幣，或者 TVL 足夠大
            if token_x not in major_tokens and token_y not in major_tokens:
                if tvl < 100000:  # 如果沒有主要代幣，至少需要 $100K TVL
                    return False

            return True

        except Exception:
            return False
    
    async def _process_sol_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 SOL 池子數據"""
        try:
            # 獲取代幣信息
            token_x_symbol = pool.get("name", "").split("-")[0] if "-" in pool.get("name", "") else "Unknown"
            token_y_symbol = pool.get("name", "").split("-")[1] if "-" in pool.get("name", "") else "Unknown"
            
            # 基本數據 - 使用正確的字段名
            tvl_usd = float(pool.get("liquidity", 0))
            volume_24h = float(pool.get("trade_volume_24h", 0))
            fees_24h = float(pool.get("fees_24h", 0))
            
            # 計算 APR
            fee_apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
            
            # 計算風險等級
            risk_level = "low"
            if fee_apr > 200:
                risk_level = "high"
            elif fee_apr > 100:
                risk_level = "medium"
            
            # 計算波動率
            volatility = min(fee_apr / 200, 1.0)
            
            return {
                "pool_id": pool.get("address", ""),
                "chain": "solana",
                "pair": f"{token_x_symbol}/{token_y_symbol}",
                "token_x": {
                    "address": pool.get("mint_x", ""),
                    "symbol": token_x_symbol,
                    "decimals": pool.get("decimals_x", 9)
                },
                "token_y": {
                    "address": pool.get("mint_y", ""),
                    "symbol": token_y_symbol,
                    "decimals": pool.get("decimals_y", 9)
                },
                "bin_step": pool.get("bin_step", 0),
                "tvl_usd": tvl_usd,
                "volume_24h": volume_24h,
                "fees_24h": fees_24h,
                "fee_apr": fee_apr,
                "total_apr": fee_apr,
                "risk_level": risk_level,
                "volatility": volatility,
                "fee_tvl_ratio": (fees_24h / tvl_usd * 100) if tvl_usd > 0 else 0,
                "strategy_type": self._determine_strategy_type(token_x_symbol, token_y_symbol),
                "last_update": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("sol_pool_processing_error", pool_id=pool.get("address"), error=str(e))
            return None
    
    def _determine_strategy_type(self, token0: str, token1: str) -> str:
        """根據代幣對確定策略類型"""
        stable_tokens = {"USDC", "USDT", "DAI", "BUSD", "USD1"}
        
        if token0 in stable_tokens and token1 in stable_tokens:
            return "CURVE_BALANCED"  # 穩定幣對
        elif token0 in stable_tokens or token1 in stable_tokens:
            return "SPOT_IMBALANCED_DAMM"  # 一個穩定幣
        else:
            return "BID_ASK_BALANCED"  # 兩個非穩定幣
    
    async def get_token_prices(self, tokens: List[str]) -> Dict[str, float]:
        """獲取代幣價格"""
        logger.info("fetching_token_prices", tokens=tokens)
        
        try:
            # CoinGecko API
            token_ids = ",".join(tokens)
            url = f"{self.endpoints['coingecko_api']}/simple/price"
            params = {
                "ids": token_ids,
                "vs_currencies": "usd"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        prices = {}
                        for token, price_data in data.items():
                            prices[token] = price_data.get("usd", 0)
                        
                        self.cache["prices"].update(prices)
                        return prices
                    else:
                        logger.error("price_fetch_failed", status=response.status)
                        return self.cache["prices"]
                        
        except Exception as e:
            logger.error("price_fetch_error", error=str(e))
            return self.cache["prices"]
    
    async def get_all_pools(self) -> Dict[str, List[Dict[str, Any]]]:
        """獲取所有鏈的池子數據"""
        logger.info("fetching_all_pools")
        
        # 並行獲取數據
        bsc_task = asyncio.create_task(self.get_bsc_pools())
        sol_task = asyncio.create_task(self.get_sol_pools())
        
        bsc_pools, sol_pools = await asyncio.gather(bsc_task, sol_task)
        
        result = {
            "bsc": bsc_pools,
            "solana": sol_pools
        }
        
        self.cache["last_update"] = datetime.now().isoformat()
        
        logger.info("all_pools_fetched", 
                   bsc_count=len(bsc_pools), 
                   sol_count=len(sol_pools))
        
        return result
    
    def get_cache_status(self) -> Dict[str, Any]:
        """獲取緩存狀態"""
        return {
            "bsc_pools_count": len(self.cache["bsc_pools"]),
            "sol_pools_count": len(self.cache["sol_pools"]),
            "prices_count": len(self.cache["prices"]),
            "last_update": self.cache["last_update"]
        }

# 全局實例
real_data_service = RealDataService()
