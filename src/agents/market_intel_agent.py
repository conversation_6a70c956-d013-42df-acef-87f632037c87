"""
MarketIntelAgent - DyFlow v3.3 市場情報收集
負責池掃描、評分，實現 1️⃣ 掃描線 (bus.pool)
使用 Agno Framework 通訊，15秒間隔掃描
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime
import json
import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from services.real_data_service import real_data_service
except ImportError:
    # 如果導入失敗，創建一個模擬的服務
    real_data_service = None

from ..events.dyflow_events import (
    EventType, PoolEvent, event_bus, create_pool_event
)

logger = structlog.get_logger(__name__)

class MarketIntelAgent:
    """
    市場情報收集 Agent
    負責掃描 BSC 和 Solana 池子，評分並推送事件
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_running = False
        self.pool_rankings = {}
        self.last_scan_time = None
        self.scan_interval = config.get('scan_interval', 30)  # 30秒掃描一次
        
        # 工具初始化
        self.tools = {}
        self._init_tools()
    
    def _init_tools(self):
        """初始化掃描工具"""
        try:
            from ..tools.damm_scanner_tool import DammScannerTool
            from ..tools.chain_scanner_tool import ChainScannerTool
            
            self.tools['damm_scanner'] = DammScannerTool(self.config.get('damm_scanner', {}))
            self.tools['chain_scanner'] = ChainScannerTool(self.config.get('chain_scanner', {}))
            
            logger.info("market_intel_tools_initialized", tools=list(self.tools.keys()))
        except Exception as e:
            logger.error("tools_initialization_failed", error=str(e))
    
    async def start_pool_scanning(self):
        """開始池子掃描"""
        self.is_running = True
        logger.info("market_intel_scanning_started")
        
        while self.is_running:
            try:
                await self._scan_and_rank_pools()
                await asyncio.sleep(self.scan_interval)
            except Exception as e:
                logger.error("pool_scanning_error", error=str(e))
                await asyncio.sleep(5)
    
    async def stop_scanning(self):
        """停止池子掃描"""
        self.is_running = False
        logger.info("market_intel_scanning_stopped")
    
    async def _scan_and_rank_pools(self):
        """掃描並排名池子 - 使用真實數據"""
        logger.info("scanning_pools_started_with_real_data")
        scan_start = datetime.now()

        # 使用真實數據服務獲取池子數據
        if real_data_service:
            try:
                # 獲取真實池子數據
                all_pools_data = await real_data_service.get_all_pools()
                bsc_pools = all_pools_data.get("bsc", [])
                sol_pools = all_pools_data.get("solana", [])

                # 合併所有池子
                all_pools = []

                # 處理 BSC 池子
                for pool in bsc_pools:
                    standardized_pool = self._standardize_bsc_pool(pool)
                    all_pools.append(standardized_pool)

                # 處理 SOL 池子
                for pool in sol_pools:
                    standardized_pool = self._standardize_sol_pool(pool)
                    all_pools.append(standardized_pool)

                logger.info("real_pools_fetched",
                           bsc_count=len(bsc_pools),
                           sol_count=len(sol_pools),
                           total_count=len(all_pools))

            except Exception as e:
                logger.error("real_data_fetch_failed", error=str(e))
                # 回退到模擬數據
                all_pools = await self._get_fallback_pools()
        else:
            # 回退到原有的掃描方法
            all_pools = await self._get_fallback_pools()

        # 評分和排名
        ranked_pools = await self._rank_pools(all_pools)

        # 更新狀態
        self.pool_rankings = {
            'pools': ranked_pools,
            'total_count': len(ranked_pools),
            'scan_time': scan_start.isoformat(),
            'scan_duration': (datetime.now() - scan_start).total_seconds()
        }
        self.last_scan_time = scan_start

        # 使用 Agno 通訊發送池事件
        await self._send_pool_events_via_agno(ranked_pools)

        logger.info("pool_scanning_completed",
                   pools_found=len(ranked_pools),
                   duration=self.pool_rankings['scan_duration'])

    def _standardize_bsc_pool(self, pool: Dict[str, Any]) -> Dict[str, Any]:
        """標準化 BSC 池子數據"""
        return {
            'id': pool.get('pool_id'),
            'chain': 'bsc',
            'protocol': 'pancakeswap_v3',
            'pair': pool.get('pair'),
            'token0': pool.get('token0', {}),
            'token1': pool.get('token1', {}),
            'tvl_usd': pool.get('tvl_usd', 0),
            'volume_24h_usd': pool.get('volume_24h', 0),
            'fees_24h_usd': pool.get('fees_24h', 0),
            'fee_tier': pool.get('fee_tier', 0.003),
            'apr': pool.get('fee_apr', 0),
            'total_apr': pool.get('total_apr', 0),
            'risk_level': pool.get('risk_level', 'medium'),
            'volatility': pool.get('volatility', 0.02),
            'strategy_type': pool.get('strategy_type', 'SPOT_BALANCED'),
            'fee_tvl_ratio': pool.get('fee_tvl_ratio', 0),
            'created_at': pool.get('created_at'),
            'last_update': pool.get('last_update'),
            'raw_data': pool
        }

    def _standardize_sol_pool(self, pool: Dict[str, Any]) -> Dict[str, Any]:
        """標準化 SOL 池子數據"""
        return {
            'id': pool.get('pool_id'),
            'chain': 'solana',
            'protocol': 'meteora_damm_v2',
            'pair': pool.get('pair'),
            'token0': pool.get('token_x', {}),
            'token1': pool.get('token_y', {}),
            'tvl_usd': pool.get('tvl_usd', 0),
            'volume_24h_usd': pool.get('volume_24h', 0),
            'fees_24h_usd': pool.get('fees_24h', 0),
            'fee_rate': pool.get('bin_step', 0),
            'apr': pool.get('fee_apr', 0),
            'total_apr': pool.get('total_apr', 0),
            'risk_level': pool.get('risk_level', 'medium'),
            'volatility': pool.get('volatility', 0.02),
            'strategy_type': pool.get('strategy_type', 'SPOT_IMBALANCED_DAMM'),
            'fee_tvl_ratio': pool.get('fee_tvl_ratio', 0),
            'created_at': pool.get('created_at'),
            'last_update': pool.get('last_update'),
            'raw_data': pool
        }

    async def _get_fallback_pools(self) -> List[Dict[str, Any]]:
        """獲取回退池子數據（模擬數據）"""
        logger.info("using_fallback_pool_data")

        # 並發掃描 BSC 和 Solana 池子
        tasks = []

        # BSC 池子掃描
        if 'chain_scanner' in self.tools:
            tasks.append(self._scan_bsc_pools())

        # Solana DAMM v2 池子掃描
        if 'damm_scanner' in self.tools:
            tasks.append(self._scan_solana_pools())

        # 等待掃描完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 合併結果
        all_pools = []
        for result in results:
            if isinstance(result, list):
                all_pools.extend(result)
            elif isinstance(result, Exception):
                logger.error("pool_scan_failed", error=str(result))

        return all_pools

    async def _scan_bsc_pools(self) -> List[Dict[str, Any]]:
        """掃描 BSC 池子"""
        try:
            chain_scanner = self.tools.get('chain_scanner')
            if not chain_scanner:
                return []
            
            # 掃描 PancakeSwap v3 池子
            bsc_pools = await chain_scanner.scan_pancake_pools()
            
            # 標準化池子數據
            standardized_pools = []
            for pool in bsc_pools:
                standardized_pool = {
                    'id': pool.get('id'),
                    'chain': 'bsc',
                    'protocol': 'pancakeswap_v3',
                    'token0': pool.get('token0', {}),
                    'token1': pool.get('token1', {}),
                    'tvl_usd': pool.get('tvl_usd', 0),
                    'volume_24h_usd': pool.get('volume_24h_usd', 0),
                    'fees_24h_usd': pool.get('fees_24h_usd', 0),
                    'fee_tier': pool.get('fee_tier', 3000),
                    'apr': pool.get('apr', 0),
                    'created_at': pool.get('created_at'),
                    'raw_data': pool
                }
                standardized_pools.append(standardized_pool)
            
            logger.info("bsc_pools_scanned", count=len(standardized_pools))
            return standardized_pools
            
        except Exception as e:
            logger.error("bsc_pool_scan_failed", error=str(e))
            return []
    
    async def _scan_solana_pools(self) -> List[Dict[str, Any]]:
        """掃描 Solana DAMM v2 池子"""
        try:
            damm_scanner = self.tools.get('damm_scanner')
            if not damm_scanner:
                return []
            
            # 掃描 Meteora DAMM v2 池子
            solana_pools = await damm_scanner.scan_damm_pools()
            
            # 標準化池子數據
            standardized_pools = []
            for pool in solana_pools:
                standardized_pool = {
                    'id': pool.get('id'),
                    'chain': 'solana',
                    'protocol': 'meteora_damm_v2',
                    'token0': pool.get('token_x', {}),
                    'token1': pool.get('token_y', {}),
                    'tvl_usd': pool.get('tvl', 0),
                    'volume_24h_usd': pool.get('volume_24h', 0),
                    'fees_24h_usd': pool.get('fees_24h', 0),
                    'fee_rate': pool.get('fee_rate', 0),
                    'apr': pool.get('apr', 0),
                    'created_at': pool.get('created_at'),
                    'raw_data': pool
                }
                standardized_pools.append(standardized_pool)
            
            logger.info("solana_pools_scanned", count=len(standardized_pools))
            return standardized_pools
            
        except Exception as e:
            logger.error("solana_pool_scan_failed", error=str(e))
            return []
    
    async def _rank_pools(self, pools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        對池子進行評分和排名
        實現 PRD v3.3 的策略映射矩陣
        """
        ranked_pools = []
        
        for pool in pools:
            try:
                # 計算池子評分
                score = await self._calculate_pool_score(pool)
                
                # 確定策略類型
                strategy_type = await self._determine_strategy_type(pool)
                
                # 添加評分和策略信息
                pool['score'] = score
                pool['strategy_type'] = strategy_type
                pool['rank'] = 0  # 稍後設置
                
                ranked_pools.append(pool)
                
            except Exception as e:
                logger.error("pool_ranking_failed", pool_id=pool.get('id'), error=str(e))
        
        # 按評分排序
        ranked_pools.sort(key=lambda x: x['score'], reverse=True)
        
        # 設置排名
        for i, pool in enumerate(ranked_pools):
            pool['rank'] = i + 1
        
        return ranked_pools
    
    async def _calculate_pool_score(self, pool: Dict[str, Any]) -> float:
        """
        計算池子評分
        基於 TVL、交易量、費用、APR 等因素
        """
        try:
            tvl = pool.get('tvl_usd', 0)
            volume_24h = pool.get('volume_24h_usd', 0)
            fees_24h = pool.get('fees_24h_usd', 0)
            apr = pool.get('apr', 0)
            
            # 基礎分數計算
            tvl_score = min(tvl / 1000000, 1.0)  # TVL 標準化到 1M USD
            volume_score = min(volume_24h / 100000, 1.0)  # 交易量標準化到 100K USD
            fee_score = min(fees_24h / 1000, 1.0)  # 費用標準化到 1K USD
            apr_score = min(apr / 100, 1.0)  # APR 標準化到 100%
            
            # 加權計算總分
            total_score = (
                tvl_score * 0.3 +
                volume_score * 0.25 +
                fee_score * 0.25 +
                apr_score * 0.2
            )
            
            # 應用鏈特定調整
            if pool.get('chain') == 'solana':
                total_score *= 1.1  # Solana 池子加分
            
            return min(total_score, 1.0)
            
        except Exception as e:
            logger.error("pool_score_calculation_failed", error=str(e))
            return 0.0
    
    async def _determine_strategy_type(self, pool: Dict[str, Any]) -> str:
        """
        根據 PRD v3.3 策略映射矩陣確定策略類型
        """
        try:
            tvl = pool.get('tvl_usd', 0)
            volume_24h = pool.get('volume_24h_usd', 0)
            fees_24h = pool.get('fees_24h_usd', 0)
            
            # 計算費率比例
            fee_tvl_ratio = (fees_24h / tvl) if tvl > 0 else 0
            
            # 模擬波動率 (實際應該從 vol_oracle 獲取)
            volatility = 0.02  # 2% 默認波動率
            
            # 根據 PRD v3.3 策略映射矩陣
            if volatility <= 0.01 and fee_tvl_ratio < 0.002:  # σ ≤ 1% ∧ Fee/TVL < 0.2%
                return 'SPOT_BALANCED'
            elif 0.01 < volatility <= 0.03:  # 1-3% 波動率
                return 'CURVE_BALANCED'
            elif volume_24h > tvl * 0.1:  # 高交易量
                return 'BID_ASK_BALANCED'
            elif volatility > 0.03 or fee_tvl_ratio >= 0.004 or tvl < 1000000:  # 高波動或低市值
                return 'SPOT_IMBALANCED_DAMM'
            else:
                return 'SPOT_BALANCED'  # 默認策略
                
        except Exception as e:
            logger.error("strategy_determination_failed", error=str(e))
            return 'SPOT_BALANCED'
    
    async def _send_pool_events_via_agno(self, pools: List[Dict[str, Any]]):
        """使用 Agno Framework 發送池事件"""
        try:
            from ..events.dyflow_events import PoolEvent, event_bus, EventType

            # 為每個池子創建標準化的 bus.pool 事件
            for pool in pools[:10]:  # 限制前10個池子避免事件過多
                pool_event = PoolEvent(
                    pool_id=pool.get('pool_id', pool.get('pair', 'unknown')),
                    chain=pool.get('chain', 'unknown'),
                    tvl=pool.get('tvl_usd', 0),
                    fee_tvl_pct=pool.get('fee_tvl_ratio', 0),
                    sigma=pool.get('volatility', 0.02),
                    spread=pool.get('spread', 0.0003)
                )

                # 發佈到事件總線
                event_bus.publish(EventType.POOL_DISCOVERED, pool_event)

            logger.info("pool_events_sent_via_agno",
                       pools_count=min(len(pools), 10),
                       total_pools=len(pools))

            # 保存最後的事件用於調試
            self.last_pool_event = {
                'type': 'pool_ranking_update',
                'timestamp': datetime.now().isoformat(),
                'pools_sent': min(len(pools), 10),
                'total_count': len(pools),
                'source_agent': 'MarketIntelAgent'
            }

        except Exception as e:
            logger.error("agno_pool_event_send_failed", error=str(e))
    
    def get_pool_rankings(self) -> Dict[str, Any]:
        """獲取當前池子排名"""
        return self.pool_rankings
    
    def get_metrics(self) -> Dict[str, Any]:
        """獲取指標用於監控"""
        return {
            'dyflow_market_intel_pools_total': len(self.pool_rankings.get('pools', [])),
            'dyflow_market_intel_last_scan': self.last_scan_time.timestamp() if self.last_scan_time else 0,
            'dyflow_market_intel_scan_duration': self.pool_rankings.get('scan_duration', 0)
        }
