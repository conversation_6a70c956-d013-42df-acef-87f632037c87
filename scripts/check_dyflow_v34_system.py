#!/usr/bin/env python3
"""
DyFlow v3.4 系統檢查腳本
符合 Ultimate PRD 規範的完整系統檢查
"""

import os
import sys
import json
import yaml
import requests
from pathlib import Path
from datetime import datetime

def print_header(title):
    """打印標題"""
    print("\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)

def print_section(title):
    """打印段落標題"""
    print(f"\n{'-' * 60}")
    print(f"  {title}")
    print(f"{'-' * 60}")

def check_agno_framework():
    """檢查 Agno Framework"""
    print_section("Agno Framework v3.4 檢查")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        print("✅ Agno Framework: 已安裝且可用")
        
        # 檢查 Ollama 連接
        try:
            model = Ollama(id="qwen2.5:3b", host="http://localhost:11434")
            print("✅ Ollama 模型: 配置正確")
        except Exception as e:
            print(f"⚠️ Ollama 模型: 配置問題 ({e})")
        
        return True
    except ImportError as e:
        print(f"❌ Agno Framework: 未安裝 ({e})")
        return False

def check_ollama_service():
    """檢查 Ollama 服務"""
    print_section("Ollama 服務檢查")
    
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Ollama 服務: 運行中 (v{version_info.get('version', 'unknown')})")
            
            # 檢查可用模型
            try:
                models_response = requests.get('http://localhost:11434/api/tags', timeout=5)
                if models_response.status_code == 200:
                    models = models_response.json().get('models', [])
                    model_names = [m['name'] for m in models]
                    
                    required_models = ['qwen2.5:3b', 'qwen3:latest']
                    available_required = [m for m in required_models if any(m in name for name in model_names)]
                    
                    print(f"✅ 可用模型: {len(model_names)} 個")
                    print(f"✅ 必需模型: {len(available_required)}/{len(required_models)} 個可用")
                    
                    if len(available_required) < len(required_models):
                        missing = [m for m in required_models if not any(m in name for name in model_names)]
                        print(f"⚠️ 缺少模型: {missing}")
                        print("建議運行: ollama pull qwen2.5:3b && ollama pull qwen3:latest")
                        
            except Exception as e:
                print(f"⚠️ 模型檢查失敗: {e}")
            
            return True
        else:
            print(f"❌ Ollama 服務: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama 服務: 無法連接 ({e})")
        print("建議運行: ollama serve")
        return False

def check_v34_workflow_config():
    """檢查 v3.4 工作流配置"""
    print_section("DyFlow v3.4 工作流配置檢查")
    
    config_file = Path("workflow/dyflow_v34.yaml")
    
    if not config_file.exists():
        print("❌ workflow/dyflow_v34.yaml: 文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ workflow/dyflow_v34.yaml: 文件存在且可解析")
        
        # 檢查必需的 v3.4 配置段
        required_sections = {
            "description": "工作流描述",
            "globals": "全局配置", 
            "kpi_targets": "KPI 目標",
            "nodes": "節點定義",
            "event_topics": "事件主題",
            "security": "安全配置",
            "websocket": "WebSocket 配置",
            "api_endpoints": "API 端點"
        }
        
        missing_sections = []
        for section, desc in required_sections.items():
            if section in config:
                print(f"✅ {desc}: 已配置")
            else:
                print(f"❌ {desc}: 缺失")
                missing_sections.append(section)
        
        # 檢查 OnboardingAgent 節點
        nodes = config.get("nodes", [])
        onboarding_node = next((n for n in nodes if n.get("id") == "onboarding"), None)
        
        if onboarding_node:
            print("✅ OnboardingAgent: 節點已定義")
            if onboarding_node.get("class") == "OnboardingAgent":
                print("✅ OnboardingAgent: 類名正確")
            else:
                print("❌ OnboardingAgent: 類名錯誤")
        else:
            print("❌ OnboardingAgent: 節點未定義")
            missing_sections.append("onboarding_node")
        
        # 檢查狀態機配置
        exec_node = next((n for n in nodes if n.get("id") == "exec"), None)
        risk_node = next((n for n in nodes if n.get("id") == "risk"), None)
        
        if exec_node and "state_machine" in exec_node:
            print("✅ ExecutionAgent: 狀態機已配置")
        else:
            print("❌ ExecutionAgent: 狀態機未配置")
        
        if risk_node and "state_machine" in risk_node:
            print("✅ RiskSentinelAgent: 狀態機已配置")
        else:
            print("❌ RiskSentinelAgent: 狀態機未配置")
        
        # 檢查 API 端點
        api_endpoints = config.get("api_endpoints", [])
        required_endpoints = ["/quickscan", "/confirm", "/trading_mode"]
        
        existing_endpoints = [ep.get("path") for ep in api_endpoints]
        missing_endpoints = [ep for ep in required_endpoints if ep not in existing_endpoints]
        
        if not missing_endpoints:
            print("✅ API 端點: 全部已配置")
        else:
            print(f"❌ API 端點: 缺少 {missing_endpoints}")
        
        return len(missing_sections) == 0 and len(missing_endpoints) == 0
        
    except Exception as e:
        print(f"❌ 配置解析失敗: {e}")
        return False

def check_v34_agents():
    """檢查 v3.4 Agent 實現"""
    print_section("DyFlow v3.4 Agent 實現檢查")
    
    agents = {
        "OnboardingAgent": "src/agents/onboarding_agent.py",
        "ExecutionAgent": "src/agents/execution_agent.py", 
        "RiskSentinelAgent": "src/agents/risk_sentinel_agno.py"
    }
    
    all_good = True
    
    for agent_name, agent_path in agents.items():
        if Path(agent_path).exists():
            print(f"✅ {agent_name}: 文件存在")
            
            # 檢查特定功能
            try:
                with open(agent_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if agent_name == "OnboardingAgent":
                    required_methods = ["trigger_quick_scan", "launch_confirmed", "QuickScanResult", "LaunchProposal"]
                elif agent_name == "ExecutionAgent":
                    required_methods = ["State", "transition_state", "can_transition"]
                elif agent_name == "RiskSentinelAgent":
                    required_methods = ["State", "WATCHING", "BREACH", "EMIT_EXIT"]
                
                missing_methods = [m for m in required_methods if m not in content]
                
                if not missing_methods:
                    print(f"✅ {agent_name}: 功能完整")
                else:
                    print(f"❌ {agent_name}: 缺少功能 {missing_methods}")
                    all_good = False
                    
            except Exception as e:
                print(f"⚠️ {agent_name}: 檢查失敗 ({e})")
        else:
            print(f"❌ {agent_name}: 文件不存在")
            all_good = False
    
    return all_good

def check_avro_schemas():
    """檢查 Avro Schema"""
    print_section("Avro Schema 檢查")
    
    schema_file = Path("schemas/dyflow_events.avsc")
    
    if not schema_file.exists():
        print("❌ schemas/dyflow_events.avsc: 文件不存在")
        return False
    
    try:
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        print("✅ Avro Schema: 文件存在且可解析")
        
        # 檢查命名空間
        if schema.get("namespace") == "dyflow.events":
            print("✅ 命名空間: dyflow.events")
        else:
            print("❌ 命名空間: 不正確")
            return False
        
        # 檢查必需的事件類型
        required_types = [
            "PoolEvent", "LPPlan", "TxEvent", "ExitRequest",
            "LaunchEvent", "QuickScanResult", "LaunchProposal"
        ]
        
        schema_types = [t.get("name") for t in schema.get("types", [])]
        missing_types = [t for t in required_types if t not in schema_types]
        
        if not missing_types:
            print(f"✅ 事件類型: 全部 {len(required_types)} 個已定義")
        else:
            print(f"❌ 事件類型: 缺少 {missing_types}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Schema 解析失敗: {e}")
        return False

def check_react_ui():
    """檢查 React UI"""
    print_section("React UI v3.4 檢查")
    
    ui_components = {
        "LaunchWizard": "react-ui/src/components/LaunchWizard/index.tsx",
        "DashboardLayout": "react-ui/src/components/layout/DashboardLayout.tsx",
        "useAgno Store": "react-ui/src/store/useAgno.ts",
        "wsHub": "react-ui/src/lib/wsHub.ts"
    }
    
    all_good = True
    
    for component_name, component_path in ui_components.items():
        if Path(component_path).exists():
            print(f"✅ {component_name}: 文件存在")
            
            # 檢查 v3.4 特定功能
            try:
                with open(component_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if component_name == "LaunchWizard":
                    required_features = ["QuickScanResult", "LaunchProposal", "四步驟", "/api/quickscan"]
                elif component_name == "useAgno Store":
                    required_features = ["wizardState", "setWizardState", "openWizard"]
                else:
                    required_features = []
                
                if required_features:
                    missing_features = [f for f in required_features if f not in content]
                    
                    if not missing_features:
                        print(f"✅ {component_name}: v3.4 功能完整")
                    else:
                        print(f"❌ {component_name}: 缺少 v3.4 功能 {missing_features}")
                        all_good = False
                        
            except Exception as e:
                print(f"⚠️ {component_name}: 檢查失敗 ({e})")
        else:
            print(f"❌ {component_name}: 文件不存在")
            all_good = False
    
    # 檢查 package.json
    package_file = Path("react-ui/package.json")
    if package_file.exists():
        print("✅ package.json: 存在")
        
        # 檢查是否已構建
        dist_dir = Path("react-ui/dist")
        if dist_dir.exists():
            print("✅ 構建產物: dist 目錄存在")
        else:
            print("⚠️ 構建產物: 需要運行 npm run build")
    else:
        print("❌ package.json: 不存在")
        all_good = False
    
    return all_good

def check_system_ports():
    """檢查系統端口"""
    print_section("系統端口檢查")
    
    ports_to_check = {
        11434: "Ollama 服務",
        8001: "Agno Workflow API", 
        3000: "React UI Dev Server",
        3001: "React UI Production"
    }
    
    for port, service in ports_to_check.items():
        try:
            response = requests.get(f'http://localhost:{port}', timeout=2)
            print(f"✅ 端口 {port}: {service} 運行中")
        except requests.exceptions.ConnectionError:
            print(f"⚠️ 端口 {port}: {service} 未運行")
        except Exception as e:
            print(f"⚠️ 端口 {port}: {service} 檢查失敗 ({e})")

def main():
    """主檢查函數"""
    print_header("DyFlow v3.4 Ultimate System Check")
    print(f"檢查時間: {datetime.now().isoformat()}")
    print(f"工作目錄: {os.getcwd()}")
    print(f"Python: {sys.version}")
    
    # 執行所有檢查
    checks = [
        ("Agno Framework", check_agno_framework),
        ("Ollama 服務", check_ollama_service),
        ("v3.4 工作流配置", check_v34_workflow_config),
        ("v3.4 Agent 實現", check_v34_agents),
        ("Avro Schema", check_avro_schemas),
        ("React UI v3.4", check_react_ui)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}: 檢查異常 ({e})")
            results.append((check_name, False))
    
    # 檢查端口
    check_system_ports()
    
    # 總結
    print_header("檢查結果總結")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{check_name:<20} {status}")
    
    print(f"\n總計: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 DyFlow v3.4 系統檢查全部通過！")
        print("🚀 系統已準備好啟動")
        print("\n📋 啟動順序:")
        print("1. python start_dyflow_v34.py")
        print("2. cd react-ui && npm run dev")
    else:
        print(f"\n⚠️ 還有 {total - passed} 項檢查未通過")
        print("請根據上述結果進行修復")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
