#!/usr/bin/env python3
"""
DyFlow v3.3 真實數據版本啟動腳本
使用真實的 PancakeSwap V3 和 Meteora DLMM v2 數據
"""

import asyncio
import sys
import os
import signal
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 導入 DyFlow 組件
try:
    from src.services.real_data_service import real_data_service
    from src.agents.market_intel_agent import MarketIntelAgent
    from src.agents.execution_agent import ExecutionAgent
    from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
    from src.utils.helpers import get_utc_timestamp
except ImportError as e:
    print(f"❌ 導入 DyFlow 組件失敗: {e}")
    sys.exit(1)

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = structlog.get_logger(__name__)

class RealDataDyFlowOrchestrator:
    """DyFlow v3.3 真實數據協調器"""
    
    def __init__(self):
        self.name = "DyFlow v3.3 Real Data Orchestrator"
        self.running = False
        self.agents: Dict[str, Any] = {}
        self.real_data_cache = {
            "bsc_pools": [],
            "sol_pools": [],
            "last_update": None
        }
        
        # 真實數據更新間隔
        self.data_refresh_interval = 60  # 60 秒更新一次真實數據
        
    async def initialize(self):
        """初始化系統"""
        logger.info("dyflow_v33_real_data_initializing")
        
        try:
            # 測試真實數據服務
            await self._test_real_data_service()
            
            # 初始化 Agents
            await self._initialize_agents()
            
            # 設置信號處理
            self._setup_signal_handlers()
            
            logger.info("dyflow_v33_real_data_initialized_successfully")
            return True
            
        except Exception as e:
            logger.error("dyflow_v33_real_data_initialization_failed", error=str(e))
            return False
    
    async def _test_real_data_service(self):
        """測試真實數據服務"""
        logger.info("testing_real_data_service")
        
        # 測試獲取 BSC 池子 - 獲取所有池子
        bsc_pools = await real_data_service.get_bsc_pools()
        logger.info("bsc_pools_test", count=len(bsc_pools))

        # 測試獲取 SOL 池子 - 獲取所有池子
        sol_pools = await real_data_service.get_sol_pools()
        logger.info("sol_pools_test", count=len(sol_pools))
        
        if len(bsc_pools) == 0 and len(sol_pools) == 0:
            logger.warning("no_real_data_available", message="將使用模擬數據")
        else:
            logger.info("real_data_service_working", 
                       bsc_pools=len(bsc_pools), 
                       sol_pools=len(sol_pools))
    
    async def _initialize_agents(self):
        """初始化所有 Agent"""
        
        # MarketIntelAgent
        market_intel_config = {
            "scan_interval": 15,  # 15 秒掃描一次
            "use_real_data": True
        }
        self.agents["market_intel"] = MarketIntelAgent(market_intel_config)
        logger.info("market_intel_agent_initialized")
        
        # ExecutionAgent
        self.agents["execution"] = ExecutionAgent()
        logger.info("execution_agent_initialized")
        
        # RiskSentinelAgent
        risk_config = {
            "monitor_interval": 10,
            "il_fuse": -0.08,
            "var_threshold": 0.04,
            "enable_agno": True,
            "model_name": "qwen2.5:3b"
        }
        self.agents["risk_sentinel"] = RiskSentinelAgnoAgent(risk_config)
        logger.info("risk_sentinel_agent_initialized")
    
    async def start_real_data_workflow(self):
        """啟動真實數據工作流程"""
        logger.info("dyflow_v33_real_data_workflow_starting")
        self.running = True
        
        try:
            # 啟動真實數據更新循環
            data_task = asyncio.create_task(self._real_data_update_loop())
            
            # 啟動 MarketIntel 掃描
            market_intel_task = asyncio.create_task(
                self.agents["market_intel"].start_pool_scanning()
            )
            
            # 啟動風險監控
            risk_task = asyncio.create_task(self._start_risk_monitoring())
            
            # 等待所有任務
            await asyncio.gather(
                data_task,
                market_intel_task, 
                risk_task,
                return_exceptions=True
            )
                
        except Exception as e:
            logger.error("real_data_workflow_execution_failed", error=str(e))
            self.running = False
    
    async def _real_data_update_loop(self):
        """真實數據更新循環"""
        logger.info("real_data_update_loop_starting")
        
        while self.running:
            try:
                # 獲取真實數據
                all_pools = await real_data_service.get_all_pools()
                
                # 更新緩存
                self.real_data_cache["bsc_pools"] = all_pools.get("bsc", [])
                self.real_data_cache["sol_pools"] = all_pools.get("solana", [])
                self.real_data_cache["last_update"] = get_utc_timestamp()
                
                # 記錄統計
                bsc_count = len(self.real_data_cache["bsc_pools"])
                sol_count = len(self.real_data_cache["sol_pools"])
                
                logger.info("real_data_updated", 
                           bsc_pools=bsc_count,
                           sol_pools=sol_count,
                           total_pools=bsc_count + sol_count)
                
                # 顯示一些池子信息
                if bsc_count > 0:
                    top_bsc = self.real_data_cache["bsc_pools"][0]
                    logger.info("top_bsc_pool", 
                               pair=top_bsc.get("pair"),
                               tvl=f"${top_bsc.get('tvl_usd', 0):,.0f}",
                               apr=f"{top_bsc.get('fee_apr', 0):.1f}%")
                
                if sol_count > 0:
                    top_sol = self.real_data_cache["sol_pools"][0]
                    logger.info("top_sol_pool", 
                               pair=top_sol.get("pair"),
                               tvl=f"${top_sol.get('tvl_usd', 0):,.0f}",
                               apr=f"{top_sol.get('fee_apr', 0):.1f}%")
                
                # 等待下一次更新
                await asyncio.sleep(self.data_refresh_interval)
                
            except Exception as e:
                logger.error("real_data_update_error", error=str(e))
                await asyncio.sleep(30)  # 錯誤時等待 30 秒
    
    async def _start_risk_monitoring(self):
        """啟動風險監控"""
        logger.info("risk_monitoring_starting")
        
        while self.running:
            try:
                # 模擬風險監控
                risk_update = {
                    "ilNet": -1.9,
                    "var95": 2.8,
                    "maxDrawdown": -3.2,
                    "riskScore": 75,
                    "healthScore": 85,
                    "timestamp": get_utc_timestamp()
                }
                
                logger.debug("risk_monitoring_update", **risk_update)
                
                # 等待下一個監控週期
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error("risk_monitoring_error", error=str(e))
                await asyncio.sleep(5)
    
    def get_real_data_summary(self) -> Dict[str, Any]:
        """獲取真實數據摘要"""
        bsc_pools = self.real_data_cache["bsc_pools"]
        sol_pools = self.real_data_cache["sol_pools"]
        
        return {
            "bsc_pools_count": len(bsc_pools),
            "sol_pools_count": len(sol_pools),
            "total_pools": len(bsc_pools) + len(sol_pools),
            "bsc_total_tvl": sum(pool.get("tvl_usd", 0) for pool in bsc_pools),
            "sol_total_tvl": sum(pool.get("tvl_usd", 0) for pool in sol_pools),
            "last_update": self.real_data_cache["last_update"],
            "data_source": "real_apis"
        }
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info("shutdown_signal_received", signal=signum)
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def shutdown(self):
        """關閉系統"""
        logger.info("dyflow_v33_real_data_shutting_down")
        self.running = False
        
        # 停止 MarketIntel 掃描
        if "market_intel" in self.agents:
            await self.agents["market_intel"].stop_scanning()
        
        # 清理資源
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'cleanup'):
                    await agent.cleanup()
                logger.info("agent_cleaned_up", agent=agent_name)
            except Exception as e:
                logger.error("agent_cleanup_failed", agent=agent_name, error=str(e))
        
        logger.info("dyflow_v33_real_data_shutdown_completed")

async def main():
    """主函數"""
    print("🚀 DyFlow v3.3 真實數據版本啟動中...")
    print("📊 使用真實的 PancakeSwap V3 和 Meteora DLMM v2 數據")
    print("⚠️  交易執行部分為模擬，其他數據均為真實")
    
    orchestrator = RealDataDyFlowOrchestrator()
    
    try:
        # 初始化
        if not await orchestrator.initialize():
            print("❌ 初始化失敗")
            return 1
        
        print("✅ 初始化完成")
        print("📋 開始真實數據工作流程...")
        
        # 啟動工作流程
        await orchestrator.start_real_data_workflow()
        
        print("🎉 DyFlow v3.3 真實數據版本運行中...")
        print("📊 數據來源:")
        print("   - BSC: PancakeSwap V3 Subgraph")
        print("   - SOL: Meteora DLMM v2 API")
        print("   - 價格: CoinGecko API")
        print("按 Ctrl+C 停止")
        
        # 保持運行
        while orchestrator.running:
            # 每 30 秒顯示一次數據摘要
            summary = orchestrator.get_real_data_summary()
            print(f"\n📈 數據摘要 ({summary['last_update']}):")
            print(f"   BSC 池子: {summary['bsc_pools_count']} 個 (TVL: ${summary['bsc_total_tvl']:,.0f})")
            print(f"   SOL 池子: {summary['sol_pools_count']} 個 (TVL: ${summary['sol_total_tvl']:,.0f})")
            print(f"   總計: {summary['total_pools']} 個池子")
            
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號")
    except Exception as e:
        print(f"❌ 運行錯誤: {e}")
        return 1
    finally:
        await orchestrator.shutdown()
        print("👋 DyFlow v3.3 真實數據版本已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
