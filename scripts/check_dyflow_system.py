#!/usr/bin/env python3
"""
DyFlow v3.4 統一系統檢查腳本
整合所有系統檢查功能
"""

import sys
import os
import json
import yaml
import requests
from pathlib import Path
from datetime import datetime

def print_header(title):
    """打印標題"""
    print("\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)

def print_section(title):
    """打印段落標題"""
    print(f"\n{'-' * 60}")
    print(f"  {title}")
    print(f"{'-' * 60}")

def check_agno_framework():
    """檢查 Agno Framework"""
    print_section("Agno Framework 檢查")
    
    try:
        import agno
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        print("✅ Agno Framework: 已安裝且可用")
        return True
    except ImportError as e:
        print(f"❌ Agno Framework: 未安裝 ({e})")
        return False

def check_ollama_service():
    """檢查 Ollama 服務"""
    print_section("Ollama 服務檢查")
    
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Ollama 服務: 運行中 (v{version_info.get('version', 'unknown')})")
            
            # 檢查模型
            models_response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if models_response.status_code == 200:
                models = models_response.json().get('models', [])
                model_names = [m['name'] for m in models]
                print(f"✅ 可用模型: {len(model_names)} 個")
                
                qwen_models = [m for m in model_names if 'qwen' in m.lower()]
                if qwen_models:
                    print(f"✅ Qwen 模型: {', '.join(qwen_models)}")
                else:
                    print("❌ Qwen 模型: 未找到")
                    print("建議運行: ollama pull qwen2.5:3b")
            return True
        else:
            print(f"❌ Ollama 服務: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama 服務: 無法連接 ({e})")
        return False

def check_dyflow_api():
    """檢查 DyFlow API"""
    print_section("DyFlow API 檢查")
    
    try:
        response = requests.get('http://localhost:8001/api/system/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ DyFlow API: 運行中")
            print(f"  狀態: {data.get('overall_status')}")
            print(f"  階段: {data.get('current_phase')}")
            print(f"  活躍 Agents: {len(data.get('active_agents', []))}")
            return True
        else:
            print(f"❌ DyFlow API: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ DyFlow API: 無法連接 ({e})")
        return False

def check_react_ui():
    """檢查 React UI"""
    print_section("React UI 檢查")
    
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            print("✅ React UI: 運行中")
            return True
        else:
            print(f"❌ React UI: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ React UI: 無法連接 ({e})")
        return False

def main():
    """主檢查函數"""
    print_header("DyFlow v3.4 統一系統檢查")
    print(f"檢查時間: {datetime.now().isoformat()}")
    print(f"工作目錄: {os.getcwd()}")
    
    # 執行所有檢查
    checks = [
        ("Agno Framework", check_agno_framework),
        ("Ollama 服務", check_ollama_service),
        ("DyFlow API", check_dyflow_api),
        ("React UI", check_react_ui)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}: 檢查異常 ({e})")
            results.append((check_name, False))
    
    # 總結
    print_header("檢查結果總結")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{check_name:<20} {status}")
    
    print(f"\n總計: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 DyFlow v3.4 系統檢查全部通過！")
    else:
        print(f"\n⚠️ 還有 {total - passed} 項檢查未通過")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
