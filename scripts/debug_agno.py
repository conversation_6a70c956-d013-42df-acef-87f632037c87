#!/usr/bin/env python3
"""
調試 Agno Framework 問題
"""

import sys
import os

def test_basic_import():
    """測試基本導入"""
    print("🔍 測試基本導入...")
    
    try:
        import agno
        print(f"✅ agno 模塊: {agno.__file__}")
        
        # 檢查 agno 目錄內容
        import pkgutil
        print("\n📦 agno 子模塊:")
        for importer, modname, ispkg in pkgutil.iter_modules(agno.__path__):
            print(f"  - {modname} ({'package' if ispkg else 'module'})")
        
        return True
    except Exception as e:
        print(f"❌ 基本導入失敗: {e}")
        return False

def test_agent_import():
    """測試 Agent 導入"""
    print("\n🤖 測試 Agent 導入...")
    
    try:
        # 嘗試不同的導入方式
        import agno.agent
        print("✅ agno.agent 模塊導入成功")
        
        from agno.agent import Agent
        print("✅ Agent 類導入成功")
        
        return True
    except Exception as e:
        print(f"❌ Agent 導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_import():
    """測試模型導入"""
    print("\n🧠 測試模型導入...")
    
    try:
        from agno.models.ollama import Ollama
        print("✅ Ollama 模型導入成功")
        return True
    except Exception as e:
        print(f"❌ Ollama 模型導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_approach():
    """測試替代方案"""
    print("\n🔄 測試替代方案...")
    
    # 檢查是否可以使用現有的 Agent 實現
    try:
        sys.path.append('/Users/<USER>/Documents/dyflow_new/src')
        from agents.planner_agno import PlannerAgnoAgent
        print("✅ 現有 PlannerAgnoAgent 可用")
        
        # 測試創建
        planner = PlannerAgnoAgent("TestPlanner", {})
        print("✅ PlannerAgnoAgent 創建成功")
        
        return True
    except Exception as e:
        print(f"❌ 替代方案失敗: {e}")
        return False

def main():
    print("=" * 50)
    print("🔧 Agno Framework 調試")
    print("=" * 50)
    
    results = []
    
    # 測試序列
    tests = [
        ("基本導入", test_basic_import),
        ("Agent 導入", test_agent_import),
        ("模型導入", test_model_import),
        ("替代方案", test_alternative_approach)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 50)
    print("📊 調試結果總結")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
    
    # 建議
    passed = sum(1 for _, result in results if result)
    print(f"\n📈 總體: {passed}/{len(results)} 測試通過")
    
    if passed == 0:
        print("\n💡 建議:")
        print("1. 重新安裝 Agno: pip uninstall agno && pip install agno")
        print("2. 檢查 Python 環境")
        print("3. 使用現有的 Agent 實現")
    elif passed < len(results):
        print("\n💡 建議:")
        print("1. 部分功能可用，可以繼續開發")
        print("2. 使用可用的組件")
        print("3. 逐步修復問題")
    else:
        print("\n🎉 所有測試通過！可以使用 Agno Framework")

if __name__ == "__main__":
    main()
