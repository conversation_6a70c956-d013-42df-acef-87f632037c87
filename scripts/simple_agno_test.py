#!/usr/bin/env python3
"""
簡單的 Agno Framework 測試
驗證基本功能和 Agent 通訊
"""

import sys
import os
from datetime import datetime

def main():
    print("=" * 50)
    print("🧪 DyFlow Agno Framework 測試")
    print("=" * 50)
    print(f"時間: {datetime.now()}")
    print(f"Python: {sys.version_info}")
    print(f"工作目錄: {os.getcwd()}")
    print()
    
    # 測試 1: Agno 導入
    print("📦 測試 1: Agno Framework 導入")
    try:
        import agno
        print("✅ agno 模塊導入成功")
        print(f"   路徑: {agno.__file__}")
        
        from agno.agent import Agent
        print("✅ Agent 類導入成功")
        
        from agno.models.ollama import Ollama
        print("✅ Ollama 模型導入成功")
        
    except Exception as e:
        print(f"❌ 導入失敗: {e}")
        return False
    
    # 測試 2: Ollama 連接
    print("\n🔗 測試 2: Ollama 服務器連接")
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            version_data = response.json()
            print(f"✅ Ollama 服務器運行中")
            print(f"   版本: {version_data.get('version', 'unknown')}")
        else:
            print(f"❌ Ollama 服務器響應異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama 連接失敗: {e}")
        return False
    
    # 測試 3: Agent 創建
    print("\n🤖 測試 3: Agent 創建")
    try:
        agent = Agent(
            name="TestAgent",
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=["You are a test agent for DyFlow system validation."]
        )
        print("✅ Agent 創建成功")
        print(f"   名稱: {agent.name}")
        print(f"   模型: {agent.model}")
        
    except Exception as e:
        print(f"❌ Agent 創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 測試 4: Agent 執行
    print("\n💬 測試 4: Agent 執行")
    try:
        print("🔄 發送測試消息...")
        response = agent.run("Hello! Please respond with 'DyFlow Agent Test Successful' to confirm you are working.")
        
        if response and response.content:
            print("✅ Agent 執行成功")
            print(f"   響應長度: {len(response.content)} 字符")
            print(f"   響應內容: {response.content[:100]}...")
        else:
            print("❌ Agent 執行失敗 - 無響應")
            return False
            
    except Exception as e:
        print(f"❌ Agent 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 測試 5: 多 Agent 協作 (如果支持)
    print("\n👥 測試 5: 多 Agent 協作")
    try:
        # 檢查是否有 Team 支持
        try:
            from agno.team import Team
            team_available = True
        except ImportError:
            print("⚠️  Team 類不可用，跳過協作測試")
            team_available = False
        
        if team_available:
            agent2 = Agent(
                name="Agent2",
                model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
                instructions=["You are the second test agent."]
            )
            
            team = Team(
                name="TestTeam",
                mode="coordinate",
                model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
                members=[agent, agent2],
                instructions=["Coordinate test agents for system validation."]
            )
            
            print("✅ Team 創建成功")
            
            team_response = team.run("Test team coordination")
            if team_response and team_response.content:
                print("✅ Team 協作成功")
                print(f"   響應: {team_response.content[:100]}...")
            else:
                print("❌ Team 協作失敗")
        
    except Exception as e:
        print(f"❌ 多 Agent 協作測試失敗: {e}")
        # 不返回 False，因為這不是關鍵功能
    
    print("\n" + "=" * 50)
    print("🎉 所有關鍵測試通過！")
    print("✅ Agno Framework 可以用於 DyFlow 系統")
    print("✅ Agent 通訊功能正常")
    print("✅ 可以替代 NATS 消息總線")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🚀 建議: 使用 Agno Framework 重構 DyFlow 架構")
            sys.exit(0)
        else:
            print("\n❌ 測試失敗，需要修復問題")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
