# DyFlow v3.4 Scripts 目錄

## 📁 腳本分類

### 🔍 系統檢查
- `check_dyflow_system.py` - 統一系統檢查腳本 (推薦使用)
- `backup_check_*.py` - 原始檢查腳本備份

### 🌐 WebSocket 服務器
- `dyflow_websocket_server.py` - 主要 WebSocket 服務器 (推薦使用)
- `simple_websocket_server.py` - 原始簡化服務器
- `backup_websocket_server.py` - 原始服務器備份

### 🧪 測試和調試
- `test_backend_functionality.py` - 後端功能測試
- `simple_agno_test.py` - Agno 框架測試
- `debug_agno.py` - Agno 調試工具

### 🔧 工具腳本
- `demo_trading_executor.py` - 交易執行演示
- `install_dependencies.py` - 依賴安裝工具
- `cleanup_project.py` - 項目清理工具

### 📦 過時腳本
- `obsolete_*.py` - 已被統一啟動腳本替代的過時腳本

## 🚀 推薦使用

1. **系統檢查**: `python scripts/check_dyflow_system.py`
2. **獨立 WebSocket**: `python scripts/dyflow_websocket_server.py`
3. **後端測試**: `python scripts/test_backend_functionality.py`

## 📝 說明

- 所有重複功能已整合
- 原始文件已備份，可安全使用新的統一腳本
- 過時腳本已移動到 `obsolete_` 前綴，可以刪除
