#!/usr/bin/env python3
"""
DyFlow v3.4 後端功能完整測試腳本
測試所有核心後端功能，確保系統準備就緒
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class DyFlowBackendTester:
    """DyFlow 後端功能測試器"""
    
    def __init__(self):
        self.api_base = "http://localhost:8001"
        self.test_results = {}
        
    async def run_all_tests(self):
        """運行所有後端測試"""
        print("🧪 DyFlow v3.4 後端功能完整測試")
        print("=" * 50)
        
        tests = [
            ("API 連接測試", self.test_api_connection),
            ("系統狀態測試", self.test_system_status),
            ("Agents 狀態測試", self.test_agents_status),
            ("池子數據測試", self.test_pools_data),
            ("聊天功能測試", self.test_chat_functionality),
            ("工作流啟動測試", self.test_workflow_start),
            ("WebSocket 連接測試", self.test_websocket_connection),
            ("真實數據服務測試", self.test_real_data_service),
            ("池子掃描工具測試", self.test_pool_scanner_tools),
            ("數據庫工具測試", self.test_database_tools)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            try:
                result = await test_func()
                self.test_results[test_name] = {"status": "✅ PASS", "result": result}
                print(f"✅ {test_name}: PASS")
            except Exception as e:
                self.test_results[test_name] = {"status": "❌ FAIL", "error": str(e)}
                print(f"❌ {test_name}: FAIL - {e}")
        
        # 生成測試報告
        await self.generate_test_report()
    
    async def test_api_connection(self):
        """測試 API 基本連接"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/") as response:
                if response.status == 200:
                    return "API 服務正常運行"
                else:
                    raise Exception(f"API 連接失敗: {response.status}")
    
    async def test_system_status(self):
        """測試系統狀態 API"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/api/system/status") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "overall_status": data.get("overall_status"),
                        "current_phase": data.get("current_phase"),
                        "active_agents": len(data.get("active_agents", [])),
                        "total_phases": data.get("total_phases")
                    }
                else:
                    raise Exception(f"系統狀態 API 失敗: {response.status}")
    
    async def test_agents_status(self):
        """測試 Agents 狀態 API"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/api/agents") as response:
                if response.status == 200:
                    data = await response.json()
                    agents = data.get("agents", [])
                    return {
                        "total_agents": len(agents),
                        "agent_names": [agent.get("name") for agent in agents],
                        "initialized_agents": len([a for a in agents if a.get("status") == "initialized"])
                    }
                else:
                    raise Exception(f"Agents 狀態 API 失敗: {response.status}")
    
    async def test_pools_data(self):
        """測試池子數據 API"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/api/pools") as response:
                if response.status == 200:
                    data = await response.json()
                    pools = data.get("pools", [])
                    return {
                        "total_pools": data.get("total", 0),
                        "bsc_pools": data.get("bsc_count", 0),
                        "sol_pools": data.get("sol_count", 0),
                        "data_source": data.get("data_source"),
                        "sample_pool": pools[0] if pools else None
                    }
                else:
                    raise Exception(f"池子數據 API 失敗: {response.status}")
    
    async def test_chat_functionality(self):
        """測試聊天功能 API"""
        async with aiohttp.ClientSession() as session:
            payload = {"message": "系統狀態如何？"}
            async with session.post(f"{self.api_base}/api/agent/chat", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "response_received": bool(data.get("response")),
                        "response_length": len(data.get("response", "")),
                        "agent_name": data.get("agent_name"),
                        "timestamp": data.get("timestamp")
                    }
                else:
                    raise Exception(f"聊天功能 API 失敗: {response.status}")
    
    async def test_workflow_start(self):
        """測試工作流啟動 API"""
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.api_base}/api/workflow/start") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "workflow_started": data.get("success", False),
                        "message": data.get("message"),
                        "phases_count": data.get("phases_count", 0)
                    }
                else:
                    raise Exception(f"工作流啟動 API 失敗: {response.status}")
    
    async def test_websocket_connection(self):
        """測試 WebSocket 連接"""
        import websockets
        try:
            async with websockets.connect(f"ws://localhost:8001/ws") as websocket:
                # 發送測試消息
                await websocket.send(json.dumps({"type": "ping"}))
                
                # 等待響應 (超時 5 秒)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    return {
                        "connection_successful": True,
                        "response_received": bool(response)
                    }
                except asyncio.TimeoutError:
                    return {
                        "connection_successful": True,
                        "response_received": False,
                        "note": "連接成功但無響應 (正常)"
                    }
        except Exception as e:
            raise Exception(f"WebSocket 連接失敗: {e}")
    
    async def test_real_data_service(self):
        """測試真實數據服務"""
        try:
            from src.services.real_data_service import real_data_service
            
            # 測試緩存狀態
            cache_status = real_data_service.get_cache_status()
            
            # 測試 BSC 池子獲取
            bsc_pools = await real_data_service.get_bsc_pools(limit=5)
            
            # 測試 Solana 池子獲取
            sol_pools = await real_data_service.get_sol_pools(limit=5)
            
            return {
                "service_available": True,
                "cache_status": cache_status,
                "bsc_pools_count": len(bsc_pools),
                "sol_pools_count": len(sol_pools),
                "last_update": cache_status.get("last_update")
            }
        except Exception as e:
            raise Exception(f"真實數據服務測試失敗: {e}")
    
    async def test_pool_scanner_tools(self):
        """測試池子掃描工具"""
        try:
            from src.tools.enhanced_pool_scanner import EnhancedPoolScanner
            
            scanner = EnhancedPoolScanner({})
            scan_stats = scanner.get_scan_stats()
            cached_pools = scanner.get_cached_pools()
            
            return {
                "scanner_available": True,
                "scan_stats": scan_stats,
                "cached_bsc_pools": len(cached_pools.get("bsc", [])),
                "cached_sol_pools": len(cached_pools.get("solana", []))
            }
        except Exception as e:
            raise Exception(f"池子掃描工具測試失敗: {e}")
    
    async def test_database_tools(self):
        """測試數據庫工具"""
        try:
            from src.tools.supabase_db_tool import SupabaseDBTool
            
            # 創建工具實例
            db_tool = SupabaseDBTool()
            
            return {
                "db_tool_available": True,
                "tool_name": db_tool.name if hasattr(db_tool, 'name') else "SupabaseDBTool"
            }
        except Exception as e:
            # 數據庫工具可能需要配置，所以失敗是正常的
            return {
                "db_tool_available": False,
                "note": "需要 Supabase 配置",
                "error": str(e)
            }
    
    async def generate_test_report(self):
        """生成測試報告"""
        print("\n" + "=" * 50)
        print("📊 DyFlow v3.4 後端功能測試報告")
        print("=" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, result in self.test_results.items():
            status = result["status"]
            print(f"{status} {test_name}")
            
            if "✅" in status:
                passed += 1
            else:
                failed += 1
                if "error" in result:
                    print(f"    錯誤: {result['error']}")
        
        print("\n" + "=" * 50)
        print(f"📈 測試總結: {passed} 通過, {failed} 失敗")
        print(f"📅 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 核心功能狀態
        print("\n🎯 核心功能狀態:")
        core_functions = [
            "API 連接測試",
            "池子數據測試", 
            "聊天功能測試",
            "真實數據服務測試"
        ]
        
        for func in core_functions:
            if func in self.test_results:
                status = self.test_results[func]["status"]
                print(f"  {status} {func}")
        
        # 建議
        print("\n💡 建議:")
        if failed == 0:
            print("  ✅ 所有後端功能正常，可以開始前端開發")
        else:
            print("  ⚠️  請修復失敗的功能後再進行前端開發")
            print("  🔧 重點關注數據源連接和 WebSocket 功能")

async def main():
    """主函數"""
    tester = DyFlowBackendTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
