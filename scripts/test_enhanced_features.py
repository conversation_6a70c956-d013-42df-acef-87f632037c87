#!/usr/bin/env python3
"""
測試 DyFlow v3.4 增強功能
1. Pool Scanner - 兩層混合輪詢 + WS 架構
2. Agent Timeline Card - 帶 ACK 狀態的實作
"""

import asyncio
import json
import websockets
import aiohttp
from datetime import datetime
import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.enhanced_market_intel_agent import EnhancedMarketIntelAgent
from backend.agents.execution_agent import ExecutionAgent
from backend.agents.strategy_agent import StrategyAgent

class MockAgnoContext:
    """模擬 Agno 上下文"""
    
    def __init__(self):
        self.published_events = []
        self.replies = []
    
    async def publish(self, topic: str, data: dict):
        """模擬發佈事件"""
        event = {
            "topic": topic,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        self.published_events.append(event)
        print(f"📤 Published to {topic}: {json.dumps(data, indent=2)}")
    
    async def reply(self, status: str):
        """模擬回覆"""
        reply = {
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        self.replies.append(reply)
        print(f"✅ Reply sent: {status}")

async def test_enhanced_market_intel_agent():
    """測試增強的 MarketIntelAgent"""
    print("\n" + "="*60)
    print("🧪 測試增強的 MarketIntelAgent")
    print("="*60)
    
    # 創建 Agent 和模擬上下文
    config = {
        'bsc_scan_interval': 5,  # 5秒測試間隔
        'sol_scan_interval': 4   # 4秒測試間隔
    }
    
    agent = EnhancedMarketIntelAgent(config)
    ctx = MockAgnoContext()
    agent.set_context(ctx)
    
    print("✅ EnhancedMarketIntelAgent 初始化完成")
    
    # 測試 HTTP 快照獲取
    print("\n📊 測試 BSC 池子快照獲取...")
    bsc_pools = await agent._fetch_bsc_snapshot()
    print(f"✅ BSC 池子數量: {len(bsc_pools)}")
    
    print("\n📊 測試 Solana 池子快照獲取...")
    sol_pools = await agent._fetch_sol_snapshot()
    print(f"✅ Solana 池子數量: {len(sol_pools)}")
    
    # 測試差分檢測
    print("\n🔍 測試差分檢測...")
    all_pools = bsc_pools + sol_pools
    events = await agent._diff_and_build(all_pools)
    print(f"✅ 生成事件數量: {len(events)}")
    
    # 測試價格追蹤器
    print("\n📈 測試價格追蹤器...")
    for i in range(5):
        pool_id = f"test_pool_{i}"
        price = 100.0 + i * 0.5
        agent.price_tracker.update(pool_id, price)
        
        if agent.price_tracker.changed(pool_id):
            snapshot = agent.price_tracker.snapshot(pool_id)
            print(f"📊 池子 {pool_id} 快照: {snapshot}")
    
    # 檢查發佈的事件
    print(f"\n📤 總共發佈事件: {len(ctx.published_events)}")
    for event in ctx.published_events[:3]:  # 顯示前3個事件
        print(f"   - {event['topic']}: {event['data'].get('pool_id', 'N/A')}")
    
    print("✅ EnhancedMarketIntelAgent 測試完成")
    return True

async def test_agent_ack_functionality():
    """測試 Agent ACK 功能"""
    print("\n" + "="*60)
    print("🧪 測試 Agent ACK 功能")
    print("="*60)
    
    # 測試 ExecutionAgent ACK
    print("\n🔧 測試 ExecutionAgent ACK...")
    execution_agent = ExecutionAgent()
    ctx = MockAgnoContext()
    execution_agent.set_context(ctx)
    
    # 模擬狀態轉換
    execution_agent.transition_state(execution_agent.State.SIGN, "test_transition")
    await execution_agent.reply_ack("ACK")
    
    print("✅ ExecutionAgent ACK 測試完成")
    
    # 測試 StrategyAgent ACK
    print("\n📋 測試 StrategyAgent ACK...")
    strategy_agent = StrategyAgent()
    ctx2 = MockAgnoContext()
    strategy_agent.set_context(ctx2)
    
    await strategy_agent.reply_ack("ACK")
    
    print("✅ StrategyAgent ACK 測試完成")
    
    # 檢查回覆
    total_replies = len(ctx.replies) + len(ctx2.replies)
    total_events = len(ctx.published_events) + len(ctx2.published_events)
    
    print(f"\n📊 ACK 測試統計:")
    print(f"   - 總回覆數: {total_replies}")
    print(f"   - 總事件數: {total_events}")
    
    return True

async def test_websocket_integration():
    """測試 WebSocket 整合"""
    print("\n" + "="*60)
    print("🧪 測試 WebSocket 整合")
    print("="*60)
    
    try:
        # 測試連接到 DyFlow API WebSocket
        uri = "ws://localhost:8001/ws"
        print(f"🔌 嘗試連接到: {uri}")
        
        async with websockets.connect(uri, timeout=5) as websocket:
            print("✅ WebSocket 連接成功")
            
            # 發送測試消息
            test_message = {
                "type": "test_enhanced_features",
                "data": {
                    "test_name": "enhanced_market_intel_test",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 測試消息已發送")
            
            # 等待回應
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3)
                response_data = json.loads(response)
                print(f"📥 收到回應: {response_data.get('type', 'unknown')}")
            except asyncio.TimeoutError:
                print("⏰ 等待回應超時（正常，因為是測試消息）")
            
        print("✅ WebSocket 整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket 連接失敗: {str(e)}")
        print("💡 請確保 DyFlow API 正在運行 (python backend/api/agno_workflow_api.py)")
        return False

async def test_api_endpoints():
    """測試 API 端點"""
    print("\n" + "="*60)
    print("🧪 測試 API 端點")
    print("="*60)
    
    base_url = "http://localhost:8001"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 測試狀態端點
            async with session.get(f"{base_url}/api/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API 狀態: {data.get('status', 'unknown')}")
                    print(f"   - 當前階段: {data.get('current_phase', 'N/A')}")
                    print(f"   - 活躍 Agents: {data.get('active_agents', 'N/A')}")
                else:
                    print(f"❌ API 狀態檢查失敗: {response.status}")
            
            # 測試池子數據端點
            async with session.get(f"{base_url}/api/pools") as response:
                if response.status == 200:
                    data = await response.json()
                    bsc_count = len(data.get('bsc_pools', []))
                    sol_count = len(data.get('sol_pools', []))
                    print(f"✅ 池子數據: BSC {bsc_count}, SOL {sol_count}")
                else:
                    print(f"❌ 池子數據獲取失敗: {response.status}")
        
        print("✅ API 端點測試完成")
        return True
        
    except Exception as e:
        print(f"❌ API 測試失敗: {str(e)}")
        return False

async def main():
    """主測試函數"""
    print("🚀 DyFlow v3.4 增強功能測試開始")
    print(f"⏰ 測試時間: {datetime.now().isoformat()}")
    
    test_results = []
    
    # 執行所有測試
    tests = [
        ("Enhanced MarketIntelAgent", test_enhanced_market_intel_agent),
        ("Agent ACK 功能", test_agent_ack_functionality),
        ("WebSocket 整合", test_websocket_integration),
        ("API 端點", test_api_endpoints)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 開始測試: {test_name}")
            result = await test_func()
            test_results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name} 測試{'通過' if result else '失敗'}")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {str(e)}")
            test_results.append((test_name, False))
    
    # 測試總結
    print("\n" + "="*60)
    print("📊 測試結果總結")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 總計: {passed}/{total} ({passed/total*100:.1f}%) 測試通過")
    
    if passed == total:
        print("🎉 所有增強功能測試通過！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關組件")

if __name__ == "__main__":
    asyncio.run(main())
