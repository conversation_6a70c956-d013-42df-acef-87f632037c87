#!/usr/bin/env python3
"""
DyFlow v3.4 項目清理和整合腳本
清理重複文件，整合功能，保持項目結構清潔
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class DyFlowProjectCleaner:
    """DyFlow 項目清理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "backup_before_cleanup"
        self.removed_files = []
        self.moved_files = []
        
    def run_cleanup(self):
        """執行完整清理"""
        print("🧹 DyFlow v3.4 項目清理和整合")
        print("=" * 50)
        
        # 創建備份目錄
        self.create_backup()
        
        # 清理重複的啟動腳本
        self.cleanup_startup_scripts()
        
        # 清理重複的文檔
        self.cleanup_documentation()
        
        # 清理測試和調試腳本
        self.cleanup_test_scripts()
        
        # 清理過時的配置文件
        self.cleanup_config_files()
        
        # 清理緩存和臨時文件
        self.cleanup_cache_files()
        
        # 生成清理報告
        self.generate_cleanup_report()
        
    def create_backup(self):
        """創建備份"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        self.backup_dir.mkdir()
        print(f"📦 創建備份目錄: {self.backup_dir}")
        
    def cleanup_startup_scripts(self):
        """清理重複的啟動腳本"""
        print("\n🚀 清理啟動腳本...")
        
        # 要移除的重複啟動腳本
        scripts_to_remove = [
            "start_dyflow_agno.py",
            "start_dyflow_v33_complete.py", 
            "start_dyflow_v34.py",
            "dyflow.py",
            "dyflow_main.py",
            "dyflow_real_data_backend.py"
        ]
        
        # 保留的主要腳本
        keep_scripts = [
            "start_dyflow_v34_unified.py"  # 主要啟動腳本
        ]
        
        for script in scripts_to_remove:
            script_path = self.project_root / script
            if script_path.exists():
                # 備份到 backup 目錄
                backup_path = self.backup_dir / script
                shutil.copy2(script_path, backup_path)
                
                # 移除原文件
                script_path.unlink()
                self.removed_files.append(script)
                print(f"  ❌ 移除: {script}")
        
        print(f"  ✅ 保留主要啟動腳本: {', '.join(keep_scripts)}")
        
    def cleanup_documentation(self):
        """清理重複的文檔"""
        print("\n📚 清理文檔...")
        
        # 要移除的重複文檔
        docs_to_remove = [
            "PROJECT_INTEGRATION_SUMMARY.md",
            "PROJECT_STRUCTURE.md", 
            "REAL_API_INTEGRATION_SUMMARY.md"
        ]
        
        # 保留的主要文檔
        keep_docs = [
            "README.md",
            "PROJECT_ARCHITECTURE_V34.md"
        ]
        
        for doc in docs_to_remove:
            doc_path = self.project_root / doc
            if doc_path.exists():
                # 備份
                backup_path = self.backup_dir / doc
                shutil.copy2(doc_path, backup_path)
                
                # 移除
                doc_path.unlink()
                self.removed_files.append(doc)
                print(f"  ❌ 移除: {doc}")
        
        print(f"  ✅ 保留主要文檔: {', '.join(keep_docs)}")
        
    def cleanup_test_scripts(self):
        """清理測試和調試腳本"""
        print("\n🧪 清理測試和調試腳本...")
        
        # 要移除的測試腳本
        test_scripts_to_remove = [
            "check_agno_workflow.py",
            "check_dyflow_v34_system.py",
            "debug_agno.py",
            "simple_agno_test.py",
            "demo_trading_executor.py",
            "test_backend_functionality.py"
        ]
        
        for script in test_scripts_to_remove:
            script_path = self.project_root / script
            if script_path.exists():
                # 移動到 scripts 目錄而不是刪除
                scripts_dir = self.project_root / "scripts"
                scripts_dir.mkdir(exist_ok=True)
                
                target_path = scripts_dir / script
                shutil.move(str(script_path), str(target_path))
                self.moved_files.append(f"{script} -> scripts/{script}")
                print(f"  📁 移動到 scripts/: {script}")
        
    def cleanup_config_files(self):
        """清理過時的配置文件"""
        print("\n⚙️  清理配置文件...")
        
        # 檢查 web_ui 目錄中的重複文件
        web_ui_files_to_remove = [
            "web_ui/fixed_agno_api.py",
            "web_ui/react_integration.py", 
            "web_ui/unified_app.py"
        ]
        
        # 保留的主要文件
        keep_web_ui = [
            "web_ui/agno_workflow_api.py",
            "web_ui/real_data_fetcher.py"
        ]
        
        for file_path in web_ui_files_to_remove:
            full_path = self.project_root / file_path
            if full_path.exists():
                # 備份
                backup_path = self.backup_dir / file_path.replace("/", "_")
                shutil.copy2(full_path, backup_path)
                
                # 移除
                full_path.unlink()
                self.removed_files.append(file_path)
                print(f"  ❌ 移除: {file_path}")
        
        print(f"  ✅ 保留主要 web_ui 文件: {', '.join(keep_web_ui)}")
        
    def cleanup_cache_files(self):
        """清理緩存和臨時文件"""
        print("\n🗑️  清理緩存文件...")
        
        # 清理 __pycache__ 目錄
        cache_dirs = list(self.project_root.rglob("__pycache__"))
        for cache_dir in cache_dirs:
            if cache_dir.is_dir():
                shutil.rmtree(cache_dir)
                print(f"  🗑️  清理: {cache_dir.relative_to(self.project_root)}")
        
        # 清理 .pyc 文件
        pyc_files = list(self.project_root.rglob("*.pyc"))
        for pyc_file in pyc_files:
            pyc_file.unlink()
            print(f"  🗑️  清理: {pyc_file.relative_to(self.project_root)}")
        
        # 清理日誌文件
        log_files = list(self.project_root.rglob("*.log"))
        for log_file in log_files:
            if log_file.stat().st_size > 10 * 1024 * 1024:  # 大於 10MB
                log_file.unlink()
                print(f"  🗑️  清理大日誌: {log_file.relative_to(self.project_root)}")
        
    def generate_cleanup_report(self):
        """生成清理報告"""
        print("\n" + "=" * 50)
        print("📊 清理報告")
        print("=" * 50)
        
        print(f"🗑️  移除文件數量: {len(self.removed_files)}")
        for file in self.removed_files:
            print(f"   - {file}")
        
        print(f"\n📁 移動文件數量: {len(self.moved_files)}")
        for file in self.moved_files:
            print(f"   - {file}")
        
        print(f"\n📦 備份位置: {self.backup_dir}")
        print(f"📅 清理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 生成新的項目結構
        self.show_clean_structure()
        
    def show_clean_structure(self):
        """顯示清理後的項目結構"""
        print("\n🏗️  清理後的項目結構:")
        print("=" * 30)
        
        structure = """
dyflow_new/
├── 🚀 start_dyflow_v34_unified.py     # 主要啟動腳本
├── 📋 README.md                       # 項目說明
├── 📋 PROJECT_ARCHITECTURE_V34.md     # 架構文檔
│
├── 📂 src/                            # 核心源代碼
│   ├── 🤖 agents/                     # 7 個 DyFlow Agents
│   ├── 🔧 tools/                      # Agent 工具集
│   ├── 📊 services/                   # 核心服務
│   └── 🛠️ utils/                      # 工具類
│
├── 📂 web_ui/                         # 後端 API
│   ├── 🌐 agno_workflow_api.py        # 主 API
│   └── real_data_fetcher.py           # 數據獲取器
│
├── 📂 react-ui/                       # 前端 React UI
│   └── 📂 src/components/             # UI 組件
│
├── 📂 scripts/                        # 工具腳本
│   ├── 測試腳本
│   └── 工具腳本
│
├── 📂 config/                         # 配置文件
├── 📂 workflow/                       # 工作流配置
└── 📂 tests/                          # 測試文件
        """
        
        print(structure)
        
        print("\n💡 建議:")
        print("  ✅ 使用 start_dyflow_v34_unified.py 啟動系統")
        print("  ✅ 查看 PROJECT_ARCHITECTURE_V34.md 了解架構")
        print("  ✅ 測試腳本已移動到 scripts/ 目錄")
        print("  ✅ 備份文件保存在 backup_before_cleanup/ 目錄")

def main():
    """主函數"""
    cleaner = DyFlowProjectCleaner()
    
    # 確認清理
    print("⚠️  即將清理項目中的重複文件")
    print("📦 所有移除的文件將備份到 backup_before_cleanup/ 目錄")
    
    response = input("\n是否繼續清理？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 清理已取消")
        return
    
    try:
        cleaner.run_cleanup()
        print("\n🎉 項目清理完成！")
        print("💡 現在可以使用 start_dyflow_v34_unified.py 啟動系統")
    except Exception as e:
        print(f"\n❌ 清理過程中出現錯誤: {e}")
        print("📦 請檢查 backup_before_cleanup/ 目錄恢復文件")

if __name__ == "__main__":
    main()
