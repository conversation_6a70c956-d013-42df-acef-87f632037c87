# DyFlow v3.4 + Agno Framework Environment Configuration
# Copy this file to .env and fill in your actual values

# ========== 錢包私鑰配置 (Phase 1 新增) ==========
# BSC 私鑰 (0x 開頭的 64 字符十六進制)
BSC_PRIVATE_KEY=0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# Solana 私鑰 (base58 編碼)
SOLANA_PRIVATE_KEY=5KQwrPbwdL6PhXujxW37FSSQZ1JiwsST4cqQzDeyXtrzLwgdj

# ========== RPC 端點配置 (Phase 1 新增) ==========
# BSC RPC URL
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Solana RPC URL
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# ========== Application Settings ==========
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO

# ========== Database Configuration ==========
# Supabase Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# ========== AI Model API Keys ==========
# OpenAI (required for Agno Framework)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude (optional, for multi-model support)
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here

# ========== Blockchain RPC Endpoints ==========
# BSC (Binance Smart Chain)
BSC_RPC_URL=https://bsc-dataseed1.binance.org/
BSC_BACKUP_RPC_URL=https://bsc-dataseed2.binance.org/

# Solana
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_BACKUP_RPC_URL=https://solana-api.projectserum.com

# ========== API Keys ==========
# The Graph (for PancakeSwap subgraph)
GRAPH_API_KEY=your_graph_api_key_here

# 1inch API (for BSC swaps)
ONEINCH_API_KEY=your_1inch_api_key_here

# CoinGecko (for price data)
COINGECKO_API_KEY=your_coingecko_api_key_here

# ========== Wallet Configuration ==========
# WARNING: Never commit real private keys to version control
# Use secure key management in production

# Test wallets (for development/testing only)
TEST_BSC_PRIVATE_KEY=your_test_bsc_private_key_here
TEST_SOLANA_PRIVATE_KEY=your_test_solana_private_key_here

# Production wallet addresses (public keys only)
PROD_BSC_WALLET_ADDRESS=0x...
PROD_SOLANA_WALLET_ADDRESS=...

# ========== Monitoring & Alerting ==========
# Telegram Bot (for alerts)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Email (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password_here

# ========== Strategy Configuration ==========
# Maximum position size in USD
MAX_POSITION_SIZE_USD=10000

# Minimum pool TVL for consideration
MIN_POOL_TVL_USD=20000

# Maximum FDV for meme coins
MAX_FDV_USD=1000000

# IL fuse threshold (percentage)
IL_FUSE_THRESHOLD=-8.0

# VaR fuse threshold (percentage)
VAR_FUSE_THRESHOLD=4.0

# ========== Feature Flags ==========
# Enable/disable specific features
ENABLE_AGNO_FRAMEWORK=true
ENABLE_AI_REASONING=true
ENABLE_MEMORY_STORAGE=true
ENABLE_MARKET_DATA_TOOLS=true
ENABLE_CIRCUIT_BREAKERS=true
ENABLE_EMERGENCY_EXIT=true
ENABLE_PROMETHEUS_METRICS=true
ENABLE_TELEGRAM_ALERTS=true

# ========== Development Settings ==========
# Mock mode (for testing without real transactions)
MOCK_MODE=false

# Dry run mode (log actions without executing)
DRY_RUN=false

# Test mode (use test networks)
TEST_MODE=false
