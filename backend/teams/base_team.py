"""
DyFlow基础团队 - 基于Agno Teams
LP监控和自动调整的核心团队架构
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

# Agno Framework imports
try:
    from agno.team import Team
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.tools.reasoning import ReasoningTools
    from agno.storage.sqlite import SqliteStorage
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # Fallback classes
    class Team:
        def __init__(self, *args, **kwargs):
            pass

logger = structlog.get_logger(__name__)


class DyFlowBaseTeam(Team):
    """DyFlow基础团队类 - 继承自Agno Team"""
    
    def __init__(
        self,
        name: str,
        description: str = "",
        storage: Optional[SqliteStorage] = None,
        debug_mode: bool = False
    ):
        if not AGNO_AVAILABLE:
            raise ImportError("Agno Framework is required for teams")
        
        # 初始化存储
        if storage is None:
            storage = SqliteStorage(
                table_name=f"{name}_sessions",
                db_file=f"data/agno_memory/{name}_team.db",
                auto_upgrade_schema=True,
                mode='team'
            )
        
        super().__init__(
            name=name,
            description=description,
            storage=storage,
            debug_mode=debug_mode
        )
        
        self.logger = structlog.get_logger(f"team.{name}")
        
        # DyFlow特定配置
        self.chain_configs = {
            'bsc': {
                'rpc_url': 'https://bsc-dataseed1.binance.org/',
                'chain_id': 56,
                'protocols': ['PancakeSwap', 'Uniswap', 'BiSwap'],
                'subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
                'api_key': '9731921233db132a98c2325878e6c153'
            },
            'solana': {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'protocols': ['Meteora', 'Orca', 'Raydium']
            }
        }
        
        # 监控配置
        self.monitoring_config = {
            'check_interval_seconds': 60,  # 1分钟检查一次
            'risk_threshold': 0.05,        # 5%风险阈值
            'rebalance_threshold': 0.10,   # 10%重新平衡阈值
            'max_slippage': 0.01,          # 1%最大滑点
            'min_tvl': 50000,              # 最小TVL $50K
            'min_volume_24h': 10000        # 最小24h交易量 $10K
        }
    
    def setup_lp_monitoring_context(self, chains: List[str], pools: List[str] = None):
        """设置LP监控上下文"""
        context = {
            "chains": chains,
            "pools": pools or [],
            "monitoring_start": datetime.now().isoformat(),
            "chain_configs": {chain: self.chain_configs.get(chain, {}) for chain in chains},
            "monitoring_config": self.monitoring_config
        }
        
        # 设置团队上下文
        self.add_context(context)
        
        self.logger.info("lp_monitoring_context_setup", 
                        chains=chains, 
                        pools_count=len(pools) if pools else 0)
    
    def get_chain_config(self, chain: str) -> Dict[str, Any]:
        """获取链配置"""
        return self.chain_configs.get(chain, {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.monitoring_config
    
    def update_monitoring_config(self, **kwargs):
        """更新监控配置"""
        self.monitoring_config.update(kwargs)
        self.logger.info("monitoring_config_updated", config=self.monitoring_config)
    
    def create_data_scout_agent(self) -> Agent:
        """创建数据收集Agent"""
        return Agent(
            name="DataScout",
            role="数据收集专家",
            model=OpenAIChat(id="gpt-4o"),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=[
                "你是DyFlow的数据收集专家。",
                "负责从各个链上收集LP池子数据。",
                "专注于获取准确、实时的池子信息。",
                "包括TVL、交易量、手续费率等关键指标。",
                "确保数据质量和完整性。"
            ],
            reasoning=True,
            show_tool_calls=False,
            markdown=True
        )
    
    def create_risk_analyst_agent(self) -> Agent:
        """创建风险分析Agent"""
        return Agent(
            name="RiskAnalyst",
            role="风险分析专家",
            model=OpenAIChat(id="gpt-4o"),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=[
                "你是DyFlow的风险分析专家。",
                "负责评估LP池子的各种风险。",
                "包括无常损失、流动性风险、价格风险等。",
                "提供详细的风险评估报告。",
                "建议风险缓解策略。"
            ],
            reasoning=True,
            show_tool_calls=False,
            markdown=True
        )
    
    def create_strategy_advisor_agent(self) -> Agent:
        """创建策略顾问Agent"""
        return Agent(
            name="StrategyAdvisor",
            role="策略顾问专家",
            model=OpenAIChat(id="gpt-4o"),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=[
                "你是DyFlow的策略顾问专家。",
                "负责制定LP调整和对冲策略。",
                "基于市场条件和风险评估提供建议。",
                "优化LP范围设置和重新平衡时机。",
                "确保策略的可执行性和有效性。"
            ],
            reasoning=True,
            show_tool_calls=False,
            markdown=True
        )
    
    def create_execution_manager_agent(self) -> Agent:
        """创建执行管理Agent"""
        return Agent(
            name="ExecutionManager",
            role="执行管理专家",
            model=OpenAIChat(id="gpt-4o"),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=[
                "你是DyFlow的执行管理专家。",
                "负责协调和执行LP调整操作。",
                "监控交易执行状态和结果。",
                "处理执行过程中的异常情况。",
                "确保操作的安全性和准确性。"
            ],
            reasoning=True,
            show_tool_calls=False,
            markdown=True
        )


class DyFlowTeamMixin:
    """DyFlow团队混入类 - 提供通用功能"""
    
    @staticmethod
    def create_pool_analysis_context(pools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建池子分析上下文"""
        return {
            "pools_data": pools_data,
            "pools_count": len(pools_data),
            "total_tvl": sum(float(pool.get('tvl_usd', 0)) for pool in pools_data),
            "total_volume_24h": sum(float(pool.get('volume_24h', 0)) for pool in pools_data),
            "avg_fee_rate": sum(float(pool.get('fee_tier', 0)) for pool in pools_data) / len(pools_data) if pools_data else 0,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def create_risk_assessment_context(
        risk_level: str, 
        alerts: List[Dict[str, Any]],
        positions: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建风险评估上下文"""
        return {
            "risk_level": risk_level,
            "alerts": alerts,
            "alerts_count": len(alerts),
            "critical_alerts": [a for a in alerts if a.get('severity') == 'critical'],
            "positions": positions or [],
            "positions_count": len(positions) if positions else 0,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def create_strategy_context(
        current_positions: List[Dict[str, Any]], 
        market_conditions: Dict[str, Any],
        recommended_actions: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建策略上下文"""
        return {
            "current_positions": current_positions,
            "market_conditions": market_conditions,
            "recommended_actions": recommended_actions or [],
            "positions_count": len(current_positions),
            "actions_count": len(recommended_actions) if recommended_actions else 0,
            "timestamp": datetime.now().isoformat()
        }


def create_team_storage(team_name: str) -> SqliteStorage:
    """创建团队存储"""
    return SqliteStorage(
        db_file=f"data/agno_memory/{team_name}_team.db",
        auto_upgrade_schema=True
    )


def get_default_agent_config() -> Dict[str, Any]:
    """获取默认Agent配置"""
    return {
        "model": "gpt-4o",
        "temperature": 0.1,
        "max_tokens": 2000,
        "timeout": 30,
        "reasoning": True,
        "show_tool_calls": False,
        "markdown": True
    }


# 团队工厂函数
def create_lp_monitoring_team(
    chains: List[str], 
    pools: List[str] = None,
    debug_mode: bool = False
) -> 'LPMonitoringTeam':
    """创建LP监控团队"""
    from .lp_monitoring_team import LPMonitoringTeam
    
    team = LPMonitoringTeam(
        name="lp_monitoring_team",
        description="LP池子监控和数据收集团队",
        debug_mode=debug_mode
    )
    
    team.setup_lp_monitoring_context(chains, pools)
    return team


def create_lp_adjustment_team(
    positions: List[Dict[str, Any]],
    debug_mode: bool = False
) -> 'LPAdjustmentTeam':
    """创建LP调整团队"""
    from .lp_adjustment_team import LPAdjustmentTeam
    
    team = LPAdjustmentTeam(
        name="lp_adjustment_team", 
        description="LP位置自动调整和重新平衡团队",
        debug_mode=debug_mode
    )
    
    # 设置位置上下文
    context = DyFlowTeamMixin.create_strategy_context(positions, {})
    team.add_context(context)
    
    return team


def create_risk_management_team(
    risk_level: str = "medium",
    debug_mode: bool = False
) -> 'RiskManagementTeam':
    """创建风险管理团队"""
    from .risk_management_team import RiskManagementTeam
    
    team = RiskManagementTeam(
        name="risk_management_team",
        description="风险监控和自动对冲团队", 
        debug_mode=debug_mode
    )
    
    # 设置风险上下文
    context = DyFlowTeamMixin.create_risk_assessment_context(risk_level, [])
    team.add_context(context)
    
    return team
