"""
Meteora DLMM Integration - 真實API連接
基於 Meteora 官方 API 文檔: https://docs.meteora.ag/
API 端點: https://dlmm-api.meteora.ag/
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog

from ..utils.models import PoolMetrics
from ..utils.exceptions import DataProviderException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)


class MeteoraIntegration:
    """Meteora DLMM API 集成"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get('endpoint', 'https://dlmm-api.meteora.ag')
        self.min_tvl = config.get('min_tvl', 50000)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # API 端點 - 使用正確的Meteora DLMM API
        self.endpoints = {
            'pools': f"{self.base_url}/pair/all",
            'pool_info': f"{self.base_url}/pair",
            'pool_bins': f"{self.base_url}/pair",
            'pool_stats': f"{self.base_url}/pair"
        }
        
        logger.info("meteora_integration_initialized", 
                   base_url=self.base_url, 
                   min_tvl=self.min_tvl)
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'DyFlow-Meteora-Integration/3.0'
                }
            )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def get_all_pools(self) -> List[Dict[str, Any]]:
        """獲取所有 DLMM 池子"""
        try:
            logger.debug("fetching_meteora_pools")
            
            async with self.session.get(self.endpoints['pools']) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("meteora_pools_api_failed", 
                               status=response.status, 
                               error=error_text)
                    raise DataProviderException(f"Meteora API 錯誤: {response.status}")
                
                data = await response.json()
                # Meteora API直接返回列表
                pools = data if isinstance(data, list) else data.get('data', [])

                # 過濾低TVL池子
                filtered_pools = []
                for pool in pools:
                    tvl_usd = float(pool.get('liquidity', 0))  # 使用liquidity字段
                    if tvl_usd >= self.min_tvl:
                        filtered_pools.append(pool)
                
                logger.info("meteora_pools_fetched", 
                           total=len(pools), 
                           filtered=len(filtered_pools))
                
                return filtered_pools
                
        except Exception as e:
            logger.error("meteora_pools_fetch_failed", error=str(e))
            raise DataProviderException(f"獲取 Meteora 池子失敗: {e}")
    
    async def get_pool_details(self, pool_address: str) -> Optional[Dict[str, Any]]:
        """獲取特定池子詳情"""
        try:
            logger.debug("fetching_meteora_pool_details", pool=pool_address)
            
            url = f"{self.endpoints['pool_info']}/{pool_address}"
            
            async with self.session.get(url) as response:
                if response.status == 404:
                    logger.warning("meteora_pool_not_found", pool=pool_address)
                    return None
                
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("meteora_pool_details_api_failed", 
                               pool=pool_address,
                               status=response.status, 
                               error=error_text)
                    return None
                
                data = await response.json()
                pool_data = data.get('data')
                
                if pool_data:
                    logger.debug("meteora_pool_details_fetched", pool=pool_address)
                
                return pool_data
                
        except Exception as e:
            logger.error("meteora_pool_details_fetch_failed", 
                        pool=pool_address, 
                        error=str(e))
            return None
    
    async def get_pool_bins(self, pool_address: str, limit: int = 10) -> List[Dict[str, Any]]:
        """獲取池子的價格區間 (bins)"""
        try:
            logger.debug("fetching_meteora_pool_bins", pool=pool_address, limit=limit)
            
            url = f"{self.endpoints['pool_bins']}/{pool_address}"
            params = {'limit': limit}
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("meteora_pool_bins_api_failed", 
                               pool=pool_address,
                               status=response.status, 
                               error=error_text)
                    return []
                
                data = await response.json()
                bins = data.get('data', [])
                
                logger.debug("meteora_pool_bins_fetched", 
                           pool=pool_address, 
                           bins_count=len(bins))
                
                return bins
                
        except Exception as e:
            logger.error("meteora_pool_bins_fetch_failed", 
                        pool=pool_address, 
                        error=str(e))
            return []
    
    async def get_pools_with_high_apr(self, min_apr: float = 0.05) -> List[Dict[str, Any]]:
        """獲取高 APR 池子"""
        try:
            all_pools = await self.get_all_pools()
            high_apr_pools = []
            
            for pool in all_pools:
                # 計算 APR (基於手續費收入)
                apr = await self._calculate_pool_apr(pool)
                if apr >= min_apr:
                    pool['calculated_apr'] = apr
                    high_apr_pools.append(pool)
            
            # 按 APR 排序
            high_apr_pools.sort(key=lambda x: x.get('calculated_apr', 0), reverse=True)
            
            logger.info("meteora_high_apr_pools_found", 
                       count=len(high_apr_pools), 
                       min_apr=min_apr)
            
            return high_apr_pools
            
        except Exception as e:
            logger.error("meteora_high_apr_pools_fetch_failed", error=str(e))
            raise DataProviderException(f"獲取高 APR 池子失敗: {e}")
    
    async def convert_to_pool_metrics(self, pool_data: Dict[str, Any]) -> PoolMetrics:
        """將 Meteora 池子數據轉換為標準 PoolMetrics"""
        try:
            # 提取基本信息
            pool_address = pool_data.get('address', '')
            name = pool_data.get('name', '')
            
            # 代幣信息 - 使用正確的字段名
            token_x = pool_data.get('mint_x', '')
            token_y = pool_data.get('mint_y', '')

            # 流動性和交易量 - 使用正確的字段名
            tvl = float(pool_data.get('liquidity', 0))
            volume_24h = float(pool_data.get('volume_24h', 0))
            
            # 計算 APR
            apr = await self._calculate_pool_apr(pool_data)
            
            # 手續費
            fee_rate = float(pool_data.get('bin_step', 25)) / 10000  # 轉換為百分比

            # 創建 PoolMetrics
            metrics = PoolMetrics(
                address=pool_address,
                name=name,
                token_a_symbol='TokenX',  # 簡化處理
                token_b_symbol='TokenY',
                token_a_address=token_x,
                token_b_address=token_y,
                tvl_usd=tvl,
                volume_24h_usd=volume_24h,
                apr=apr,
                fee_rate=fee_rate,
                protocol='Meteora DLMM',
                chain='solana',
                last_updated=get_utc_timestamp()
            )
            
            logger.debug("meteora_pool_metrics_converted", 
                        pool=pool_address, 
                        tvl=tvl, 
                        apr=apr)
            
            return metrics
            
        except Exception as e:
            logger.error("meteora_pool_metrics_conversion_failed", 
                        pool_data=pool_data, 
                        error=str(e))
            raise DataProviderException(f"轉換池子數據失敗: {e}")
    
    async def _calculate_pool_apr(self, pool_data: Dict[str, Any]) -> float:
        """計算池子 APR"""
        try:
            tvl = float(pool_data.get('liquidity', 0))
            volume_24h = float(pool_data.get('volume_24h', 0))
            fee_rate = float(pool_data.get('bin_step', 25)) / 10000  # 默認 0.25%
            
            if tvl <= 0:
                return 0.0
            
            # 計算日手續費收入
            daily_fees = volume_24h * fee_rate
            
            # 年化收益率
            apr = (daily_fees / tvl) * 365
            
            # 限制在合理範圍內
            return min(max(apr, 0), 5.0)  # 0-500%
            
        except Exception as e:
            logger.warning("meteora_apr_calculation_failed", error=str(e))
            return 0.0
