"""
BSC 交易構建器 - DyFlow v3.4
專門處理 BSC (PancakeSwap V3) 的交易構建和執行
從 pancake_subgraph_tool.py 分離出來的 BSC 專用功能
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

# Web3 相關導入
try:
    from web3 import Web3
    from eth_account import Account
    WEB3_AVAILABLE = True
except ImportError:
    WEB3_AVAILABLE = False
    logger.warning("Web3 libraries not available, using simulation mode")

logger = structlog.get_logger(__name__)

class BSCTransactionBuilder:
    """
    BSC 交易構建器
    
    功能：
    1. PancakeSwap V3 LP 建倉交易構建
    2. 四種策略的 tick 範圍計算
    3. Gas 費估算和優化
    4. 交易簽名和廣播
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # BSC 配置
        self.rpc_url = self.config.get('bsc_rpc_url', 'https://bsc-dataseed.binance.org/')
        self.chain_id = 56  # BSC Mainnet
        
        # PancakeSwap V3 合約地址
        self.contracts = {
            'router_v3': '******************************************',
            'position_manager': '******************************************',
            'factory': '******************************************'
        }
        
        # Web3 實例
        self.w3 = None
        if WEB3_AVAILABLE:
            try:
                self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
                logger.info("bsc_web3_connected", connected=self.w3.is_connected())
            except Exception as e:
                logger.error("bsc_web3_connection_failed", error=str(e))
        
        logger.info("bsc_transaction_builder_initialized",
                   web3_available=WEB3_AVAILABLE,
                   rpc_url=self.rpc_url)
    
    async def build_lp_transaction(self, pool_id: str, ranges: List[Dict[str, Any]], 
                                 strategy_type: str) -> Dict[str, Any]:
        """
        構建 LP 建倉交易
        
        Args:
            pool_id: 池子地址
            ranges: 流動性範圍列表
            strategy_type: 策略類型
            
        Returns:
            Dict: 交易數據
        """
        try:
            # 1. 獲取池子信息
            pool_info = await self._get_pool_info(pool_id)
            if not pool_info:
                raise Exception(f"Pool not found: {pool_id}")
            
            # 2. 構建交易參數
            tx_params = await self._build_mint_params(pool_info, ranges)
            
            # 3. 估算 Gas 費
            gas_estimate = await self._estimate_gas(tx_params)
            
            # 4. 構建完整交易
            transaction = {
                'to': self.contracts['position_manager'],
                'data': tx_params['encoded_data'],
                'value': tx_params.get('value', '0'),
                'gas': gas_estimate['gas_limit'],
                'gasPrice': gas_estimate['gas_price'],
                'nonce': None,  # 將在簽名時設定
                'chainId': self.chain_id,
                'pool_id': pool_id,
                'strategy_type': strategy_type,
                'ranges': ranges,
                'created_at': datetime.now().isoformat()
            }
            
            logger.info("bsc_lp_transaction_built",
                       pool_id=pool_id,
                       strategy=strategy_type,
                       ranges_count=len(ranges),
                       gas_limit=gas_estimate['gas_limit'])
            
            return transaction
            
        except Exception as e:
            logger.error("bsc_lp_transaction_build_failed",
                        pool_id=pool_id, error=str(e))
            raise
    
    async def _get_pool_info(self, pool_id: str) -> Optional[Dict[str, Any]]:
        """獲取池子詳細信息"""
        try:
            if not self.w3:
                # 模擬池子信息
                return {
                    'address': pool_id,
                    'token0': '******************************************',  # WBNB
                    'token1': '******************************************',  # USDT
                    'fee': 3000,
                    'tick': 276324,
                    'sqrt_price': '1234567890123456789012345678',
                    'liquidity': '123456789012345678901234567890'
                }
            
            # 實際實現應該調用合約獲取池子信息
            # pool_contract = self.w3.eth.contract(address=pool_id, abi=POOL_ABI)
            # ...
            
            return None
            
        except Exception as e:
            logger.error("bsc_pool_info_fetch_failed", pool_id=pool_id, error=str(e))
            return None
    
    async def _build_mint_params(self, pool_info: Dict[str, Any], 
                               ranges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """構建 mint 參數"""
        try:
            # 構建 PancakeSwap V3 mint 參數
            mint_params = []
            
            for range_data in ranges:
                tick_lower = range_data['tick_lower']
                tick_upper = range_data['tick_upper']
                amount_usd = range_data['amount_usd']
                
                # 計算代幣數量
                token_amounts = await self._calculate_token_amounts(
                    pool_info, tick_lower, tick_upper, amount_usd
                )
                
                mint_param = {
                    'token0': pool_info['token0'],
                    'token1': pool_info['token1'],
                    'fee': pool_info['fee'],
                    'tickLower': tick_lower,
                    'tickUpper': tick_upper,
                    'amount0Desired': token_amounts['amount0'],
                    'amount1Desired': token_amounts['amount1'],
                    'amount0Min': int(token_amounts['amount0'] * 0.95),  # 5% 滑點
                    'amount1Min': int(token_amounts['amount1'] * 0.95),
                    'recipient': '0x0000000000000000000000000000000000000000',  # 將設為實際地址
                    'deadline': int(datetime.now().timestamp()) + 1800  # 30 分鐘
                }
                
                mint_params.append(mint_param)
            
            # 編碼交易數據
            encoded_data = await self._encode_mint_data(mint_params)
            
            return {
                'mint_params': mint_params,
                'encoded_data': encoded_data,
                'value': '0'  # 如果涉及 ETH，需要計算 value
            }
            
        except Exception as e:
            logger.error("bsc_mint_params_build_failed", error=str(e))
            raise
    
    async def _calculate_token_amounts(self, pool_info: Dict[str, Any], 
                                     tick_lower: int, tick_upper: int, 
                                     amount_usd: float) -> Dict[str, Any]:
        """計算代幣數量"""
        try:
            # 簡化計算，實際應該根據當前價格和範圍計算精確數量
            # 這裡假設 50/50 分配
            
            # 假設 token0 是 WBNB ($300), token1 是 USDT ($1)
            token0_price = 300.0
            token1_price = 1.0
            
            amount0_usd = amount_usd * 0.5
            amount1_usd = amount_usd * 0.5
            
            # 轉換為 wei (18 decimals for WBNB, 18 for USDT)
            amount0 = int(amount0_usd / token0_price * 10**18)
            amount1 = int(amount1_usd / token1_price * 10**18)
            
            return {
                'amount0': amount0,
                'amount1': amount1,
                'amount0_usd': amount0_usd,
                'amount1_usd': amount1_usd
            }
            
        except Exception as e:
            logger.error("token_amount_calculation_failed", error=str(e))
            raise
    
    async def _encode_mint_data(self, mint_params: List[Dict[str, Any]]) -> str:
        """編碼 mint 交易數據"""
        try:
            if not self.w3:
                # 模擬編碼數據
                return "0x" + "a" * 200  # 模擬編碼後的數據
            
            # 實際實現應該使用 ABI 編碼
            # contract = self.w3.eth.contract(address=self.contracts['position_manager'], abi=POSITION_MANAGER_ABI)
            # encoded = contract.encodeABI(fn_name='mint', args=[mint_params[0]])
            # return encoded
            
            return "0x" + "a" * 200
            
        except Exception as e:
            logger.error("mint_data_encoding_failed", error=str(e))
            raise
    
    async def _estimate_gas(self, tx_params: Dict[str, Any]) -> Dict[str, Any]:
        """估算 Gas 費"""
        try:
            if not self.w3:
                # 模擬 Gas 估算
                return {
                    'gas_limit': 300000,
                    'gas_price': 5000000000,  # 5 Gwei
                    'gas_cost_usd': 15.0
                }
            
            # 實際 Gas 估算
            # gas_limit = self.w3.eth.estimate_gas(transaction)
            # gas_price = self.w3.eth.gas_price
            
            return {
                'gas_limit': 300000,
                'gas_price': 5000000000,
                'gas_cost_usd': 15.0
            }
            
        except Exception as e:
            logger.error("gas_estimation_failed", error=str(e))
            # 返回保守估算
            return {
                'gas_limit': 500000,
                'gas_price': 10000000000,  # 10 Gwei
                'gas_cost_usd': 25.0
            }
    
    async def sign_transaction(self, transaction: Dict[str, Any], 
                             private_key: str) -> Dict[str, Any]:
        """
        簽名 BSC 交易
        
        Args:
            transaction: 交易數據
            private_key: 私鑰
            
        Returns:
            Dict: 簽名結果
        """
        try:
            if not WEB3_AVAILABLE:
                # 模擬簽名
                return {
                    'success': True,
                    'signed_tx': transaction,
                    'tx_hash': f"0x{'a' * 64}",
                    'signature': f"0x{'b' * 130}",
                    'chain': 'bsc'
                }
            
            # 獲取 nonce
            account = Account.from_key(private_key)
            nonce = self.w3.eth.get_transaction_count(account.address)
            
            # 更新交易 nonce
            transaction['nonce'] = nonce
            
            # 簽名交易
            signed_txn = self.w3.eth.account.sign_transaction(transaction, private_key)
            
            return {
                'success': True,
                'signed_tx': signed_txn,
                'tx_hash': signed_txn.hash.hex(),
                'signature': signed_txn.signature.hex(),
                'chain': 'bsc'
            }
            
        except Exception as e:
            logger.error("bsc_transaction_signing_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'chain': 'bsc'
            }
    
    async def broadcast_transaction(self, signed_tx: Dict[str, Any]) -> Dict[str, Any]:
        """
        廣播 BSC 交易
        
        Args:
            signed_tx: 已簽名交易
            
        Returns:
            Dict: 廣播結果
        """
        try:
            if not self.w3:
                # 模擬廣播
                return {
                    'success': True,
                    'tx_hash': f"0x{'c' * 64}",
                    'chain': 'bsc',
                    'timestamp': datetime.now().isoformat()
                }
            
            # 實際廣播
            tx_hash = self.w3.eth.send_raw_transaction(signed_tx['rawTransaction'])
            
            return {
                'success': True,
                'tx_hash': tx_hash.hex(),
                'chain': 'bsc',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("bsc_transaction_broadcast_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'chain': 'bsc'
            }
    
    async def wait_for_confirmation(self, tx_hash: str, 
                                  confirmations: int = 3) -> Dict[str, Any]:
        """
        等待 BSC 交易確認
        
        Args:
            tx_hash: 交易哈希
            confirmations: 需要的確認數
            
        Returns:
            Dict: 確認結果
        """
        try:
            if not self.w3:
                # 模擬確認
                await asyncio.sleep(3)
                return {
                    'confirmed': True,
                    'tx_hash': tx_hash,
                    'block_number': 12345678,
                    'confirmations': confirmations,
                    'status': 'success'
                }
            
            # 實際等待確認
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
            
            return {
                'confirmed': True,
                'tx_hash': tx_hash,
                'block_number': receipt['blockNumber'],
                'confirmations': confirmations,
                'status': 'success' if receipt['status'] == 1 else 'failed',
                'gas_used': receipt['gasUsed'],
                'receipt': receipt
            }
            
        except Exception as e:
            logger.error("bsc_confirmation_wait_failed", tx_hash=tx_hash, error=str(e))
            return {
                'confirmed': False,
                'tx_hash': tx_hash,
                'error': str(e)
            }
    
    def get_supported_strategies(self) -> List[str]:
        """獲取支援的策略類型"""
        return [
            'SPOT_BALANCED',
            'CURVE_BALANCED', 
            'BID_ASK_BALANCED',
            'SPOT_IMBALANCED_DAMM'
        ]
    
    def get_contract_addresses(self) -> Dict[str, str]:
        """獲取合約地址"""
        return self.contracts.copy()
    
    def get_chain_info(self) -> Dict[str, Any]:
        """獲取鏈信息"""
        return {
            'chain': 'bsc',
            'chain_id': self.chain_id,
            'rpc_url': self.rpc_url,
            'web3_connected': self.w3.is_connected() if self.w3 else False,
            'contracts': self.contracts
        }
