"""
LP Strategy Analysis Tool for Agno Framework
用於 LP 策略分析和優化的量化工具
"""

import math
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import structlog

try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        def __init__(self):
            pass

logger = structlog.get_logger(__name__)

class StrategyAnalysisTool(Tool if AGNO_AVAILABLE else object):
    """LP 策略分析工具 - 量化分析和優化"""
    
    def __init__(self):
        if AGNO_AVAILABLE:
            super().__init__()
        
        # 策略類型配置
        self.strategy_configs = {
            "SPOT_BALANCED": {
                "allocation_weight": 0.40,
                "risk_level": "medium",
                "range_multiplier": 1.0,
                "rebalance_threshold": 0.05
            },
            "CURVE_BALANCED": {
                "allocation_weight": 0.30,
                "risk_level": "low",
                "range_multiplier": 0.8,
                "rebalance_threshold": 0.03
            },
            "BID_ASK_BALANCED": {
                "allocation_weight": 0.20,
                "risk_level": "medium",
                "range_multiplier": 1.2,
                "rebalance_threshold": 0.02
            },
            "SPOT_IMBALANCED_DAMM": {
                "allocation_weight": 0.10,
                "risk_level": "high",
                "range_multiplier": 1.5,
                "rebalance_threshold": 0.08
            }
        }
    
    def calculate_il_risk(self, price_change_percent: float) -> float:
        """
        計算無常損失風險
        
        Args:
            price_change_percent: 價格變化百分比
            
        Returns:
            無常損失百分比
        """
        try:
            # IL 公式: IL = 2 * sqrt(price_ratio) / (1 + price_ratio) - 1
            price_ratio = 1 + (price_change_percent / 100)
            if price_ratio <= 0:
                return -100.0  # 極端情況
            
            il = 2 * math.sqrt(price_ratio) / (1 + price_ratio) - 1
            return il * 100  # 轉換為百分比
            
        except Exception as e:
            logger.error("calculate_il_risk_failed", error=str(e))
            return 0.0
    
    def calculate_var_95(self, returns: List[float]) -> float:
        """
        計算 95% VaR (Value at Risk)
        
        Args:
            returns: 歷史收益率列表
            
        Returns:
            95% VaR 值
        """
        try:
            if not returns or len(returns) < 10:
                return 0.0
            
            # 使用歷史模擬法計算 VaR
            sorted_returns = sorted(returns)
            var_index = int(len(sorted_returns) * 0.05)  # 5% 分位數
            var_95 = abs(sorted_returns[var_index])
            
            return var_95
            
        except Exception as e:
            logger.error("calculate_var_95_failed", error=str(e))
            return 0.0
    
    def optimize_portfolio_allocation(self, pools: List[Dict[str, Any]], total_capital: float) -> Dict[str, Any]:
        """
        優化投資組合分配
        
        Args:
            pools: 池子列表
            total_capital: 總資本
            
        Returns:
            優化後的分配方案
        """
        try:
            if not pools:
                return {"strategies": [], "total_allocation": 0}
            
            strategies = []
            total_allocation = 0
            
            # 按 APR 和風險評分排序池子
            scored_pools = []
            for pool in pools:
                score = self._calculate_pool_score(pool)
                scored_pools.append((pool, score))
            
            scored_pools.sort(key=lambda x: x[1], reverse=True)
            
            # 為每種策略類型分配最佳池子
            for strategy_type, config in self.strategy_configs.items():
                if not scored_pools:
                    break
                
                # 選擇最佳池子
                best_pool, _ = scored_pools.pop(0)
                
                allocation_amount = total_capital * config["allocation_weight"]
                
                strategy = {
                    "strategy_type": strategy_type,
                    "pool_id": best_pool.get("pool_id", ""),
                    "token_pair": f"{best_pool.get('token0', {}).get('symbol', '')}/{best_pool.get('token1', {}).get('symbol', '')}",
                    "allocation_amount": allocation_amount,
                    "expected_apr": best_pool.get("apr", 0),
                    "risk_level": config["risk_level"],
                    "tvl_usd": best_pool.get("tvl_usd", 0),
                    "chain": best_pool.get("chain", ""),
                    "dex": best_pool.get("dex", ""),
                    "reasoning": self._generate_strategy_reasoning(strategy_type, best_pool),
                    "risk_metrics": {
                        "il_risk": self.calculate_il_risk(10),  # 假設 10% 價格變化
                        "var_95": 0.04,  # 4% VaR 閾值
                        "liquidity_risk": "low" if best_pool.get("tvl_usd", 0) > 50000000 else "medium"
                    }
                }
                
                strategies.append(strategy)
                total_allocation += allocation_amount
            
            return {
                "strategies": strategies,
                "total_allocation": total_allocation,
                "allocation_efficiency": total_allocation / total_capital,
                "diversification_score": len(strategies) / len(self.strategy_configs),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("optimize_portfolio_allocation_failed", error=str(e))
            return {"strategies": [], "total_allocation": 0}
    
    def _calculate_pool_score(self, pool: Dict[str, Any]) -> float:
        """計算池子評分"""
        try:
            apr = pool.get("apr", 0)
            tvl = pool.get("tvl_usd", 0)
            volume_24h = pool.get("volume_24h_usd", 0)
            
            # 評分公式：APR 權重 50%，TVL 權重 30%，交易量權重 20%
            apr_score = min(apr / 200, 1.0)  # 標準化到 200% APR
            tvl_score = min(tvl / 100000000, 1.0)  # 標準化到 $100M TVL
            volume_score = min(volume_24h / 10000000, 1.0)  # 標準化到 $10M 日交易量
            
            total_score = (apr_score * 0.5) + (tvl_score * 0.3) + (volume_score * 0.2)
            
            return total_score
            
        except Exception as e:
            logger.error("calculate_pool_score_failed", pool_id=pool.get("pool_id"), error=str(e))
            return 0.0
    
    def _generate_strategy_reasoning(self, strategy_type: str, pool: Dict[str, Any]) -> str:
        """生成策略推理說明"""
        token_pair = f"{pool.get('token0', {}).get('symbol', '')}/{pool.get('token1', {}).get('symbol', '')}"
        apr = pool.get("apr", 0)
        tvl = pool.get("tvl_usd", 0)
        chain = pool.get("chain", "")
        
        reasoning_templates = {
            "SPOT_BALANCED": f"平衡策略適合 {token_pair} 對，APR {apr:.1f}%，TVL ${tvl:,.0f}，在 {chain} 上提供穩定收益",
            "CURVE_BALANCED": f"曲線策略針對 {token_pair} 優化，集中流動性提高資本效率，預期 APR {apr:.1f}%",
            "BID_ASK_BALANCED": f"做市策略在 {token_pair} 上捕捉買賣價差，高頻交易環境下 APR {apr:.1f}%",
            "SPOT_IMBALANCED_DAMM": f"動態不平衡策略適合 {token_pair} 波動性，DAMM 機制下預期 APR {apr:.1f}%"
        }
        
        return reasoning_templates.get(strategy_type, f"策略適合 {token_pair}，預期 APR {apr:.1f}%")
    
    # Agno Tool 方法
    async def analyze_pools(self, pools_data: str) -> str:
        """Agno 工具方法：分析池子數據"""
        try:
            # 這裡應該解析 pools_data，但為了簡化，我們使用模擬數據
            mock_pools = [
                {
                    "pool_id": "SOL/USDC",
                    "token0": {"symbol": "SOL"},
                    "token1": {"symbol": "USDC"},
                    "apr": 180.0,
                    "tvl_usd": 25000000,
                    "volume_24h_usd": 5000000,
                    "chain": "Solana",
                    "dex": "Meteora DLMM v2"
                },
                {
                    "pool_id": "BNB/USDT",
                    "token0": {"symbol": "BNB"},
                    "token1": {"symbol": "USDT"},
                    "apr": 145.0,
                    "tvl_usd": 45000000,
                    "volume_24h_usd": 8000000,
                    "chain": "BSC",
                    "dex": "PancakeSwap V3"
                }
            ]
            
            result = self.optimize_portfolio_allocation(mock_pools, 100000)
            
            output = f"Portfolio Optimization Results:\n\n"
            output += f"Total Allocation: ${result['total_allocation']:,.2f}\n"
            output += f"Allocation Efficiency: {result['allocation_efficiency']:.1%}\n"
            output += f"Diversification Score: {result['diversification_score']:.1%}\n\n"
            
            for strategy in result['strategies']:
                output += f"• {strategy['strategy_type']}\n"
                output += f"  Pool: {strategy['token_pair']}\n"
                output += f"  Allocation: ${strategy['allocation_amount']:,.2f}\n"
                output += f"  Expected APR: {strategy['expected_apr']:.1f}%\n"
                output += f"  Risk Level: {strategy['risk_level']}\n"
                output += f"  Reasoning: {strategy['reasoning']}\n\n"
            
            return output
            
        except Exception as e:
            logger.error("analyze_pools_failed", error=str(e))
            return f"Analysis failed: {str(e)}"
    
    async def calculate_risk_metrics(self, price_changes: str) -> str:
        """Agno 工具方法：計算風險指標"""
        try:
            # 解析價格變化數據
            changes = [float(x.strip()) for x in price_changes.split(",")]
            
            il_risks = [self.calculate_il_risk(change) for change in changes]
            var_95 = self.calculate_var_95(changes)
            
            output = f"Risk Metrics Analysis:\n\n"
            output += f"Price Changes: {changes}\n"
            output += f"IL Risks: {[f'{il:.2f}%' for il in il_risks]}\n"
            output += f"95% VaR: {var_95:.2f}%\n"
            output += f"Risk Status: {'⚠️ HIGH' if var_95 > 4.0 else '✅ NORMAL'}\n"
            
            return output
            
        except Exception as e:
            logger.error("calculate_risk_metrics_failed", error=str(e))
            return f"Risk calculation failed: {str(e)}"
