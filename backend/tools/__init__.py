"""
DyFlow v3.4 統一工具包
清理後的核心工具集，移除重複實現
"""

# 導入核心工具
from .pool_scanner_tool import PoolScannerTool, PoolScannerToolSync
from .supabase_db_tool import SupabaseDbTool, SupabaseDbToolSync

# 嘗試導入其他工具（可選）
try:
    from .meteora_dlmm_tool import MeteoraDLMMTool
except ImportError:
    MeteoraDLMMTool = None

try:
    from .pancake_subgraph_tool import PancakeSubgraphTool
except ImportError:
    PancakeSubgraphTool = None

try:
    from .jupiter_swap_tool import JupiterSwapTool
except ImportError:
    JupiterSwapTool = None

# 检查Agno Framework可用性
try:
    from agno.agent import Agent
    from agno.tools import Toolkit
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

__all__ = [
    # 核心工具
    'PoolScannerTool',
    'SupabaseDbTool',
    'PoolScannerToolSync',
    'SupabaseDbToolSync',

    # 可選工具
    'MeteoraDLMMTool',
    'PancakeSubgraphTool',
    'JupiterSwapTool',

    # 工具状态
    'AGNO_AVAILABLE'
]

__version__ = "3.4.0"

# 工具集描述
TOOLS_INFO = {
    'PoolScannerTool': {
        'description': 'DeFi池子扫描工具，支持BSC PancakeSwap和Solana Meteora',
        'category': 'data_collection',
        'chains': ['BSC', 'Solana'],
        'agno_native': True
    },
    'SupabaseDbTool': {
        'description': 'Supabase数据库操作工具，提供统一数据访问接口',
        'category': 'database',
        'chains': ['N/A'],
        'agno_native': True
    }
}

def get_available_tools():
    """获取可用工具列表及其状态"""
    tools_status = {}

    for tool_name in ['PoolScannerTool', 'SupabaseDbTool']:
        if tool_name in TOOLS_INFO:
            tools_status[tool_name] = {
                'available': True,
                'agno_framework': AGNO_AVAILABLE,
                'sync_version': f"{tool_name}Sync",
                **TOOLS_INFO[tool_name]
            }

    return {
        'agno_framework_available': AGNO_AVAILABLE,
        'total_tools': len(tools_status),
        'tools': tools_status
    }

def create_tool_suite(supabase_url=None, supabase_key=None):
    """
    创建核心工具套件实例

    Args:
        supabase_url: Supabase数据库URL
        supabase_key: Supabase API密钥

    Returns:
        dict: 包含所有工具实例的字典
    """
    tools = {}

    # 创建核心工具实例
    tools['scanner'] = PoolScannerTool()

    if supabase_url and supabase_key:
        tools['database'] = SupabaseDbTool(
            supabase_url=supabase_url,
            supabase_key=supabase_key
        )

    # 如果Agno不可用，创建同步版本
    if not AGNO_AVAILABLE:
        tools['scanner_sync'] = PoolScannerToolSync()

        if supabase_url and supabase_key:
            tools['database_sync'] = SupabaseDbToolSync(
                supabase_url=supabase_url,
                supabase_key=supabase_key
            )

    return tools