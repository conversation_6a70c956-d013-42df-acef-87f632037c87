"""
CoinGecko API Tool for Agno Framework
用於獲取實時價格數據的 Agno 工具
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        def __init__(self):
            pass

logger = structlog.get_logger(__name__)

class CoinGeckoTool(Tool if AGNO_AVAILABLE else object):
    """CoinGecko API 工具 - 獲取實時價格數據"""
    
    def __init__(self):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.base_url = "https://api.coingecko.com/api/v3"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 代幣映射
        self.token_mapping = {
            # BSC tokens
            "BNB": "binancecoin",
            "USDT": "tether", 
            "USDC": "usd-coin",
            "USD1": "usd1",
            "BUSD": "binance-usd",
            "ETH": "ethereum",
            "BTCB": "bitcoin",
            
            # Solana tokens
            "SOL": "solana",
            "WSOL": "wrapped-solana",
            "WBTC": "wrapped-bitcoin",
            "USDC.SOL": "usd-coin",
            "USDT.SOL": "tether",
            
            # Common tokens
            "WETH": "ethereum",
            "WBTC": "wrapped-bitcoin"
        }
    
    async def initialize(self):
        """初始化 HTTP 會話"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'DyFlow-CoinGecko-Tool/1.0'
            }
        )
        logger.info("coingecko_tool_initialized")
    
    async def cleanup(self):
        """清理資源"""
        if self.session:
            await self.session.close()
        logger.info("coingecko_tool_cleanup_completed")
    
    async def get_token_price(self, token_symbol: str, vs_currency: str = "usd") -> Optional[float]:
        """
        獲取單個代幣價格
        
        Args:
            token_symbol: 代幣符號 (如 BNB, SOL, USDC)
            vs_currency: 計價貨幣 (默認 USD)
            
        Returns:
            代幣價格，失敗時返回 None
        """
        try:
            # 映射代幣符號到 CoinGecko ID
            token_id = self.token_mapping.get(token_symbol.upper())
            if not token_id:
                logger.warning("token_not_mapped", token=token_symbol)
                return None
            
            url = f"{self.base_url}/simple/price"
            params = {
                "ids": token_id,
                "vs_currencies": vs_currency,
                "include_24hr_change": "true",
                "include_last_updated_at": "true"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error("coingecko_api_error", status=response.status, token=token_symbol)
                    return None
                
                data = await response.json()
                price = data.get(token_id, {}).get(vs_currency)
                
                if price:
                    logger.debug("token_price_retrieved", token=token_symbol, price=price)
                    return float(price)
                else:
                    logger.warning("price_not_found", token=token_symbol, data=data)
                    return None
                    
        except Exception as e:
            logger.error("get_token_price_failed", token=token_symbol, error=str(e))
            return None
    
    async def get_multiple_prices(self, token_symbols: List[str], vs_currency: str = "usd") -> Dict[str, float]:
        """
        批量獲取多個代幣價格
        
        Args:
            token_symbols: 代幣符號列表
            vs_currency: 計價貨幣
            
        Returns:
            代幣價格字典 {symbol: price}
        """
        try:
            # 映射所有代幣符號
            token_ids = []
            symbol_to_id = {}
            
            for symbol in token_symbols:
                token_id = self.token_mapping.get(symbol.upper())
                if token_id:
                    token_ids.append(token_id)
                    symbol_to_id[token_id] = symbol.upper()
            
            if not token_ids:
                logger.warning("no_valid_tokens_to_fetch", tokens=token_symbols)
                return {}
            
            url = f"{self.base_url}/simple/price"
            params = {
                "ids": ",".join(token_ids),
                "vs_currencies": vs_currency,
                "include_24hr_change": "true"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error("coingecko_batch_api_error", status=response.status)
                    return {}
                
                data = await response.json()
                prices = {}
                
                for token_id, price_data in data.items():
                    symbol = symbol_to_id.get(token_id)
                    price = price_data.get(vs_currency)
                    
                    if symbol and price:
                        prices[symbol] = float(price)
                
                logger.info("multiple_prices_retrieved", count=len(prices), tokens=list(prices.keys()))
                return prices
                
        except Exception as e:
            logger.error("get_multiple_prices_failed", tokens=token_symbols, error=str(e))
            return {}
    
    async def get_market_data(self, token_symbol: str) -> Optional[Dict[str, Any]]:
        """
        獲取代幣詳細市場數據
        
        Args:
            token_symbol: 代幣符號
            
        Returns:
            市場數據字典，包含價格、市值、交易量等
        """
        try:
            token_id = self.token_mapping.get(token_symbol.upper())
            if not token_id:
                logger.warning("token_not_mapped_for_market_data", token=token_symbol)
                return None
            
            url = f"{self.base_url}/coins/{token_id}"
            params = {
                "localization": "false",
                "tickers": "false",
                "market_data": "true",
                "community_data": "false",
                "developer_data": "false",
                "sparkline": "false"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error("coingecko_market_data_error", status=response.status, token=token_symbol)
                    return None
                
                data = await response.json()
                market_data = data.get("market_data", {})
                
                result = {
                    "symbol": token_symbol.upper(),
                    "name": data.get("name"),
                    "current_price": market_data.get("current_price", {}).get("usd"),
                    "market_cap": market_data.get("market_cap", {}).get("usd"),
                    "total_volume": market_data.get("total_volume", {}).get("usd"),
                    "price_change_24h": market_data.get("price_change_24h"),
                    "price_change_percentage_24h": market_data.get("price_change_percentage_24h"),
                    "market_cap_rank": market_data.get("market_cap_rank"),
                    "circulating_supply": market_data.get("circulating_supply"),
                    "total_supply": market_data.get("total_supply"),
                    "last_updated": market_data.get("last_updated")
                }
                
                logger.info("market_data_retrieved", token=token_symbol, price=result["current_price"])
                return result
                
        except Exception as e:
            logger.error("get_market_data_failed", token=token_symbol, error=str(e))
            return None

    # Agno Tool 方法
    async def get_price(self, token: str) -> str:
        """Agno 工具方法：獲取代幣價格"""
        price = await self.get_token_price(token)
        if price:
            return f"${price:.6f}"
        return "Price not available"
    
    async def get_prices(self, tokens: str) -> str:
        """Agno 工具方法：批量獲取價格"""
        token_list = [t.strip() for t in tokens.split(",")]
        prices = await self.get_multiple_prices(token_list)
        
        if prices:
            result = []
            for token, price in prices.items():
                result.append(f"{token}: ${price:.6f}")
            return "\n".join(result)
        return "No prices available"
    
    async def get_market_info(self, token: str) -> str:
        """Agno 工具方法：獲取市場信息"""
        data = await self.get_market_data(token)
        if data:
            return f"""
{data['name']} ({data['symbol']})
Price: ${data['current_price']:.6f}
Market Cap: ${data['market_cap']:,.0f}
24h Volume: ${data['total_volume']:,.0f}
24h Change: {data['price_change_percentage_24h']:.2f}%
Rank: #{data['market_cap_rank']}
"""
        return "Market data not available"
