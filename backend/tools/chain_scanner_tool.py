"""
ChainScannerTool - BSC 和 Solana 鏈掃描工具
實現 PancakeSwap v3 和 Solana 鏈上數據掃描
"""

import asyncio
import aiohttp
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = structlog.get_logger(__name__)

class ChainScannerTool:
    """
    區塊鏈掃描工具
    支持 BSC PancakeSwap v3 和 Solana 鏈掃描
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.bsc_rpc_url = config.get('bsc_rpc_url', 'https://bsc-dataseed.binance.org/')
        self.solana_rpc_url = config.get('solana_rpc_url', 'https://api.mainnet-beta.solana.com')
        self.pancake_subgraph_url = config.get('pancake_subgraph_url', 
            'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ')
        self.timeout = config.get('timeout', 30)
        self.api_key = config.get('api_key', '9731921233db132a98c2325878e6c153')
    
    async def scan_pancake_pools(self) -> List[Dict[str, Any]]:
        """
        掃描 PancakeSwap v3 池子
        使用 The Graph 子圖 API
        """
        logger.info("scanning_pancake_v3_pools")
        
        try:
            # GraphQL 查詢
            query = """
            {
              pools(
                first: 100,
                orderBy: totalValueLockedUSD,
                orderDirection: desc,
                where: {
                  totalValueLockedUSD_gt: "10000"
                }
              ) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                totalValueLockedUSD
                volumeUSD
                feesUSD
                createdAtTimestamp
                tick
                sqrtPrice
                liquidity
              }
            }
            """
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}' if self.api_key else None
            }
            
            # 移除 None 值的 header
            headers = {k: v for k, v in headers.items() if v is not None}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    self.pancake_subgraph_url,
                    json={'query': query},
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'data' in data and 'pools' in data['data']:
                            pools = data['data']['pools']
                            processed_pools = []
                            
                            for pool in pools:
                                processed_pool = await self._process_pancake_pool(pool)
                                processed_pools.append(processed_pool)
                            
                            logger.info("pancake_pools_scanned", count=len(processed_pools))
                            return processed_pools
                        else:
                            logger.error("invalid_subgraph_response", data=data)
                            return []
                    else:
                        logger.error("subgraph_request_failed", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("pancake_pool_scan_failed", error=str(e))
            return []
    
    async def _process_pancake_pool(self, pool: Dict[str, Any]) -> Dict[str, Any]:
        """處理 PancakeSwap 池子數據"""
        try:
            # 提取基本信息
            pool_id = pool.get('id', '')
            token0 = pool.get('token0', {})
            token1 = pool.get('token1', {})
            
            # 計算數值
            tvl_usd = float(pool.get('totalValueLockedUSD', 0))
            volume_usd = float(pool.get('volumeUSD', 0))
            fees_usd = float(pool.get('feesUSD', 0))
            fee_tier = int(pool.get('feeTier', 3000))
            
            # 計算 APR
            apr = self._calculate_pancake_apr(tvl_usd, fees_usd)
            
            # 計算創建時間
            created_timestamp = pool.get('createdAtTimestamp')
            created_at = None
            if created_timestamp:
                created_at = datetime.fromtimestamp(int(created_timestamp))
            
            return {
                'id': pool_id,
                'chain': 'bsc',
                'protocol': 'pancakeswap_v3',
                'token0': {
                    'address': token0.get('id', ''),
                    'symbol': token0.get('symbol', ''),
                    'name': token0.get('name', ''),
                    'decimals': int(token0.get('decimals', 18))
                },
                'token1': {
                    'address': token1.get('id', ''),
                    'symbol': token1.get('symbol', ''),
                    'name': token1.get('name', ''),
                    'decimals': int(token1.get('decimals', 18))
                },
                'tvl_usd': tvl_usd,
                'volume_24h_usd': volume_usd,
                'fees_24h_usd': fees_usd,
                'fee_tier': fee_tier,
                'apr': apr,
                'created_at': created_at.isoformat() if created_at else None,
                'tick': pool.get('tick'),
                'sqrt_price': pool.get('sqrtPrice'),
                'liquidity': pool.get('liquidity'),
                'raw_data': pool
            }
            
        except Exception as e:
            logger.error("pancake_pool_processing_failed", pool_id=pool.get('id'), error=str(e))
            return {
                'id': pool.get('id', ''),
                'chain': 'bsc',
                'protocol': 'pancakeswap_v3',
                'error': str(e),
                'raw_data': pool
            }
    
    def _calculate_pancake_apr(self, tvl_usd: float, fees_24h_usd: float) -> float:
        """計算 PancakeSwap 池子 APR"""
        try:
            if tvl_usd > 0 and fees_24h_usd > 0:
                # 年化收益率 = (24h費用 / TVL) * 365 * 100
                apr = (fees_24h_usd / tvl_usd) * 365 * 100
                return min(apr, 10000)  # 限制最大 APR
            return 0.0
        except Exception:
            return 0.0
    
    async def scan_solana_tokens(self) -> List[Dict[str, Any]]:
        """
        掃描 Solana 代幣信息
        使用 Solana RPC API
        """
        logger.info("scanning_solana_tokens")
        
        try:
            # 獲取熱門代幣列表
            popular_tokens = [
                'So11111111111111111111111111111111111111112',  # SOL
                'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',  # USDT
            ]
            
            token_info = []
            for token_address in popular_tokens:
                info = await self._get_solana_token_info(token_address)
                if info:
                    token_info.append(info)
            
            logger.info("solana_tokens_scanned", count=len(token_info))
            return token_info
            
        except Exception as e:
            logger.error("solana_token_scan_failed", error=str(e))
            return []
    
    async def _get_solana_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """獲取 Solana 代幣信息"""
        try:
            # RPC 請求獲取代幣信息
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": [
                    token_address,
                    {"encoding": "jsonParsed"}
                ]
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(self.solana_rpc_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'result' in data and data['result']:
                            account_info = data['result']
                            return {
                                'address': token_address,
                                'chain': 'solana',
                                'account_info': account_info
                            }
            
            return None
            
        except Exception as e:
            logger.error("solana_token_info_failed", token=token_address, error=str(e))
            return None
    
    async def get_bsc_block_info(self) -> Optional[Dict[str, Any]]:
        """獲取 BSC 區塊信息"""
        try:
            payload = {
                "jsonrpc": "2.0",
                "method": "eth_blockNumber",
                "params": [],
                "id": 1
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(self.bsc_rpc_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'result' in data:
                            block_number = int(data['result'], 16)
                            return {
                                'chain': 'bsc',
                                'block_number': block_number,
                                'block_hex': data['result']
                            }
            
            return None
            
        except Exception as e:
            logger.error("bsc_block_info_failed", error=str(e))
            return None
    
    async def get_solana_slot_info(self) -> Optional[Dict[str, Any]]:
        """獲取 Solana slot 信息"""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSlot"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(self.solana_rpc_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'result' in data:
                            return {
                                'chain': 'solana',
                                'slot': data['result']
                            }
            
            return None
            
        except Exception as e:
            logger.error("solana_slot_info_failed", error=str(e))
            return None
    
    async def check_chain_health(self) -> Dict[str, Any]:
        """檢查鏈健康狀況"""
        try:
            # 並發檢查 BSC 和 Solana
            bsc_task = asyncio.create_task(self.get_bsc_block_info())
            solana_task = asyncio.create_task(self.get_solana_slot_info())
            
            bsc_info, solana_info = await asyncio.gather(bsc_task, solana_task, return_exceptions=True)
            
            health_status = {
                'bsc': {
                    'healthy': not isinstance(bsc_info, Exception) and bsc_info is not None,
                    'info': bsc_info if not isinstance(bsc_info, Exception) else {'error': str(bsc_info)}
                },
                'solana': {
                    'healthy': not isinstance(solana_info, Exception) and solana_info is not None,
                    'info': solana_info if not isinstance(solana_info, Exception) else {'error': str(solana_info)}
                }
            }
            
            overall_health = health_status['bsc']['healthy'] and health_status['solana']['healthy']
            
            return {
                'overall_healthy': overall_health,
                'chains': health_status,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("chain_health_check_failed", error=str(e))
            return {
                'overall_healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
