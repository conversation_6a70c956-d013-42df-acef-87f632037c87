"""
Real Data Provider Tool - 实时数据提供工具
整合 dyflow_real_data_backend.py 的逻辑到 Agno Framework
提供 BSC PancakeSwap V3 和 Solana Meteora DAMM v2 的实时数据
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
import structlog
from datetime import datetime
import json

# Agno Framework imports
try:
    from agno.tools import Tool
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        pass
    class BaseModel:
        pass

from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

class RealDataProviderTool(Tool):
    """实时数据提供工具 - 基于 Agno Framework"""
    
    name = "real_data_provider"
    description = "获取BSC PancakeSwap V3和Solana Meteora DAMM v2的实时池子数据"
    
    def __init__(self):
        super().__init__()
        
        # API配置
        self.pancakeswap_config = {
            'subgraph_url': 'https://gateway.thegraph.graph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
            'api_key': '9731921233db132a98c2325878e6c153'
        }
        
        self.meteora_config = {
            'base_url': 'https://dammv2-api.meteora.ag',
            'endpoints': [
                '/pools?order_by=tvl&order=desc&limit=50&offset=0',
                '/pools?order_by=apr&order=desc&limit=50&offset=0', 
                '/pools?order_by=volume24h&order=desc&limit=50&offset=0'
            ]
        }
        
        self.coingecko_url = 'https://api.coingecko.com/api/v3/simple/price'
        
        # 会话管理
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def run(self, chains: List[str] = None, max_pools_per_chain: int = None) -> Dict[str, Any]:
        """
        获取实时池子数据
        
        Args:
            chains: 要获取的链列表，默认 ['bsc', 'solana']
            max_pools_per_chain: 每条链最大池子数量，None表示返回所有符合条件的
            
        Returns:
            包含BSC和Solana池子数据的字典
        """
        if chains is None:
            chains = ['bsc', 'solana']
            
        try:
            # 初始化会话
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            logger.info("real_data_provider_started", chains=chains)
            
            result = {
                'bsc_pools': [],
                'solana_pools': [],
                'metadata': {
                    'timestamp': get_utc_timestamp().isoformat(),
                    'chains_requested': chains,
                    'max_pools_per_chain': max_pools_per_chain
                }
            }
            
            # 获取价格数据
            prices = await self._get_coingecko_prices()
            
            # 并发获取各链数据
            tasks = []
            if 'bsc' in chains:
                tasks.append(self._get_bsc_pools())
            if 'solana' in chains:
                tasks.append(self._get_solana_pools())
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            task_index = 0
            if 'bsc' in chains:
                bsc_result = results[task_index]
                if isinstance(bsc_result, Exception):
                    logger.error("bsc_data_fetch_failed", error=str(bsc_result))
                    result['metadata']['bsc_error'] = str(bsc_result)
                else:
                    result['bsc_pools'] = bsc_result[:max_pools_per_chain] if max_pools_per_chain else bsc_result
                task_index += 1
            
            if 'solana' in chains:
                solana_result = results[task_index]
                if isinstance(solana_result, Exception):
                    logger.error("solana_data_fetch_failed", error=str(solana_result))
                    result['metadata']['solana_error'] = str(solana_result)
                else:
                    result['solana_pools'] = solana_result[:max_pools_per_chain] if max_pools_per_chain else solana_result
            
            # 更新元数据
            result['metadata'].update({
                'bsc_pools_count': len(result['bsc_pools']),
                'solana_pools_count': len(result['solana_pools']),
                'total_pools': len(result['bsc_pools']) + len(result['solana_pools'])
            })
            
            logger.info("real_data_provider_completed",
                       bsc_pools=len(result['bsc_pools']),
                       solana_pools=len(result['solana_pools']))
            
            return result
            
        except Exception as e:
            logger.error("real_data_provider_failed", error=str(e))
            return {
                'bsc_pools': [],
                'solana_pools': [],
                'metadata': {
                    'timestamp': get_utc_timestamp().isoformat(),
                    'error': str(e)
                }
            }
    
    async def _get_coingecko_prices(self) -> Dict[str, float]:
        """获取CoinGecko价格数据"""
        try:
            params = {
                'ids': 'binancecoin,ethereum,bitcoin,solana',
                'vs_currencies': 'usd'
            }
            
            async with self.session.get(self.coingecko_url, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        'bnb': data.get('binancecoin', {}).get('usd', 0),
                        'eth': data.get('ethereum', {}).get('usd', 0),
                        'btc': data.get('bitcoin', {}).get('usd', 0),
                        'sol': data.get('solana', {}).get('usd', 0)
                    }
                else:
                    logger.warning("coingecko_api_error", status=response.status)
                    return {}
                    
        except Exception as e:
            logger.warning("coingecko_fetch_failed", error=str(e))
            return {}
    
    async def _get_bsc_pools(self) -> List[Dict[str, Any]]:
        """获取BSC PancakeSwap V3池子数据"""
        try:
            # GraphQL查询
            query = """
            {
                pools(first: 25, orderBy: totalValueLockedUSD, orderDirection: desc) {
                    id
                    feeTier
                    token0 {
                        symbol
                        name
                    }
                    token1 {
                        symbol
                        name
                    }
                    token0Price
                    token1Price
                    totalValueLockedUSD
                    volumeUSD
                }
            }
            """
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.pancakeswap_config["api_key"]}'
            }
            
            async with self.session.post(
                self.pancakeswap_config['subgraph_url'],
                json={'query': query},
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status != 200:
                    raise Exception(f"PancakeSwap API错误: {response.status}")
                
                data = await response.json()
                pools_data = data.get('data', {}).get('pools', [])
                
                return self._parse_bsc_pools(pools_data)
                
        except Exception as e:
            logger.error("bsc_pools_fetch_failed", error=str(e))
            raise
    
    async def _get_solana_pools(self) -> List[Dict[str, Any]]:
        """获取Solana Meteora DAMM v2池子数据"""
        try:
            all_pools = []
            unique_pools = {}
            
            # 从多个端点获取数据
            for endpoint in self.meteora_config['endpoints']:
                url = self.meteora_config['base_url'] + endpoint
                
                async with self.session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if isinstance(data, dict) and 'data' in data:
                            pools_data = data['data']
                        else:
                            pools_data = data if isinstance(data, list) else []
                        
                        parsed_pools = self._parse_meteora_pools(pools_data)
                        
                        # 去重
                        for pool in parsed_pools:
                            address = pool.get('address')
                            if address and address not in unique_pools:
                                unique_pools[address] = pool
            
            # 转换为列表并排序
            final_pools = list(unique_pools.values())
            final_pools.sort(key=lambda x: x.get('apr', 0), reverse=True)
            
            return final_pools
            
        except Exception as e:
            logger.error("solana_pools_fetch_failed", error=str(e))
            raise
    
    def _parse_bsc_pools(self, pools_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析BSC池子数据"""
        pools = []
        
        for pool_data in pools_data:
            try:
                # 基础数据提取
                token0_symbol = pool_data.get('token0', {}).get('symbol', '')
                token1_symbol = pool_data.get('token1', {}).get('symbol', '')
                
                if not token0_symbol or not token1_symbol:
                    continue
                
                # TVL和交易量处理
                tvl_usd_raw = float(pool_data.get('totalValueLockedUSD', 0))
                volume_usd_raw = float(pool_data.get('volumeUSD', 0))
                
                # 修复异常大数值 - 除以1e9
                if tvl_usd_raw > 1e15:
                    tvl_usd = tvl_usd_raw / 1e9
                else:
                    tvl_usd = tvl_usd_raw
                
                if volume_usd_raw > 1e15:
                    volume_24h = volume_usd_raw / 1e9
                else:
                    volume_24h = volume_usd_raw
                
                # 过滤低TVL池子
                if tvl_usd < 10000:  # 至少$10K TVL
                    continue
                
                # 计算费用和APR
                fee_tier = float(pool_data.get('feeTier', 2500))
                fee_rate = fee_tier / 1000000  # 转换为小数
                
                daily_fees = volume_24h * fee_rate
                apr = (daily_fees / tvl_usd * 365 * 100) if tvl_usd > 0 else 0
                
                pool = {
                    'pair': f"{token0_symbol}/{token1_symbol}",
                    'protocol': 'PancakeSwap V3',
                    'address': pool_data.get('id', ''),
                    'chain': 'bsc',
                    'tvl_usd': tvl_usd,
                    'volume_24h': volume_24h,
                    'fees_24h': daily_fees,
                    'apr': apr,
                    'risk_level': self._calculate_risk_level(apr, tvl_usd),
                    'fee_tier': f"{fee_tier/10000:.2f}%",
                    'recommendation': self._get_recommendation(apr, tvl_usd)
                }
                
                pools.append(pool)
                
            except Exception as e:
                logger.warning("bsc_pool_parse_failed", error=str(e))
                continue
        
        return pools
    
    def _parse_meteora_pools(self, pools_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析Meteora DAMM v2池子数据"""
        pools = []
        
        for pool_data in pools_data:
            try:
                # 代币信息
                token_a_symbol = pool_data.get('token_a_symbol', '')
                token_b_symbol = pool_data.get('token_b_symbol', '')
                
                if not token_a_symbol or not token_b_symbol:
                    continue
                
                # 数据提取
                tvl_usd = float(pool_data.get('tvl', 0))
                volume_24h = float(pool_data.get('volume24h', 0))
                fees_24h = float(pool_data.get('fee24h', 0))
                apr = float(pool_data.get('apr', 0))
                
                # 过滤条件
                if tvl_usd < 1000:  # 至少$1K TVL
                    continue
                
                if fees_24h <= 1:  # 24h费用 > $1
                    continue
                
                fee_tvl_ratio = (fees_24h / tvl_usd) * 100 if tvl_usd > 0 else 0
                if fee_tvl_ratio < 1.0:  # Fee/TVL >= 1%
                    continue
                
                pool = {
                    'pair': f"{token_a_symbol}-{token_b_symbol}",
                    'protocol': 'Meteora DAMM v2',
                    'address': pool_data.get('address', ''),
                    'chain': 'solana',
                    'tvl_usd': tvl_usd,
                    'volume_24h': volume_24h,
                    'fees_24h': fees_24h,
                    'apr': apr,
                    'risk_level': self._calculate_risk_level(apr, tvl_usd),
                    'fee_tier': f"{fee_tvl_ratio:.2f}%",
                    'recommendation': self._get_recommendation(apr, tvl_usd)
                }
                
                pools.append(pool)
                
            except Exception as e:
                logger.warning("meteora_pool_parse_failed", error=str(e))
                continue
        
        return pools
    
    def _calculate_risk_level(self, apr: float, tvl_usd: float) -> str:
        """计算风险等级"""
        if apr > 1000 or tvl_usd < 10000:
            return "高風險"
        elif apr > 100 or tvl_usd < 100000:
            return "中風險"
        else:
            return "低風險"
    
    def _get_recommendation(self, apr: float, tvl_usd: float) -> str:
        """获取投资建议"""
        if apr > 500 and tvl_usd > 50000:
            return "高收益機會"
        elif apr > 100 and tvl_usd > 100000:
            return "平衡選擇"
        elif tvl_usd > 1000000:
            return "穩定選擇"
        else:
            return "謹慎考慮"
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None

# 导出
__all__ = ['RealDataProviderTool', 'AGNO_AVAILABLE']
