"""
Meteora DLMM v2 Tool for Agno Framework
用於掃描 Solana Meteora DLMM v2 池子的 Agno 工具
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog

try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        def __init__(self):
            pass

logger = structlog.get_logger(__name__)

class MeteoraTool(Tool if AGNO_AVAILABLE else object):
    """Meteora DLMM v2 工具 - 掃描 Solana 池子"""
    
    def __init__(self):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.api_base = "https://dlmm-api.meteora.ag"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 目標代幣 mint 地址 (Solana)
        self.target_tokens = {
            "SOL": "So11111111111111111111111111111111111111112",  # Wrapped SOL
            "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "USDT": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", 
            "ETH": "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs",  # Ethereum (Wormhole)
            "WBTC": "********************************************", # Wrapped Bitcoin (Wormhole)
            "mSOL": "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So",
            "stSOL": "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj"
        }
    
    async def initialize(self):
        """初始化 HTTP 會話"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'DyFlow-Meteora-Tool/1.0'
            }
        )
        logger.info("meteora_tool_initialized")
    
    async def cleanup(self):
        """清理資源"""
        if self.session:
            await self.session.close()
        logger.info("meteora_tool_cleanup_completed")
    
    async def scan_pools(self, min_tvl: float = 10000000, max_age_hours: int = 48, min_fee_rate: float = 0.05) -> List[Dict[str, Any]]:
        """
        掃描符合條件的 Meteora DLMM v2 池子
        
        Args:
            min_tvl: 最小 TVL (默認 $10M)
            max_age_hours: 最大創建時間 (默認 48 小時)
            min_fee_rate: 最小費用率 (默認 5%)
            
        Returns:
            符合條件的池子列表
        """
        try:
            # 獲取所有池子
            url = f"{self.api_base}/pair/all"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error("meteora_api_error", status=response.status)
                    return []
                
                data = await response.json()
                
                # 處理不同的 API 響應格式
                if isinstance(data, list):
                    pools = data
                elif isinstance(data, dict):
                    pools = data.get("data", data.get("pairs", []))
                else:
                    logger.warning("unexpected_api_response_format", type=type(data))
                    return []
            
            # 過濾池子
            filtered_pools = []
            current_time = datetime.now()
            
            for pool in pools:
                if self._meets_criteria(pool, min_tvl, max_age_hours, min_fee_rate, current_time):
                    pool_info = self._format_pool_info(pool)
                    if pool_info:
                        filtered_pools.append(pool_info)
            
            logger.info("meteora_pools_scanned", 
                       total_found=len(pools), 
                       filtered_count=len(filtered_pools),
                       min_tvl=min_tvl,
                       max_age_hours=max_age_hours,
                       min_fee_rate=min_fee_rate)
            
            return filtered_pools
            
        except Exception as e:
            logger.error("scan_meteora_pools_failed", error=str(e))
            return []
    
    async def get_top_pools(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        獲取頂級池子（按 TVL 排序）
        
        Args:
            limit: 返回池子數量限制
            
        Returns:
            頂級池子列表
        """
        try:
            url = f"{self.api_base}/pair/all"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error("meteora_top_pools_api_error", status=response.status)
                    return []
                
                data = await response.json()
                
                # 處理響應格式
                if isinstance(data, list):
                    pools = data
                elif isinstance(data, dict):
                    pools = data.get("data", data.get("pairs", []))
                else:
                    return []
            
            # 格式化並排序池子
            formatted_pools = []
            for pool in pools:
                pool_info = self._format_pool_info(pool)
                if pool_info and pool_info.get('tvl_usd', 0) > 1000000:  # 最小 $1M TVL
                    formatted_pools.append(pool_info)
            
            # 按 TVL 排序
            formatted_pools.sort(key=lambda x: x.get('tvl_usd', 0), reverse=True)
            
            logger.info("meteora_top_pools_retrieved", count=len(formatted_pools[:limit]))
            return formatted_pools[:limit]
            
        except Exception as e:
            logger.error("get_meteora_top_pools_failed", error=str(e))
            return []
    
    def _meets_criteria(self, pool: Dict[str, Any], min_tvl: float, max_age_hours: int, 
                       min_fee_rate: float, current_time: datetime) -> bool:
        """檢查池子是否符合過濾條件"""
        try:
            # 檢查 TVL
            tvl = self._safe_float(pool.get('liquidity', 0))
            if tvl < min_tvl:
                return False
            
            # 檢查是否包含目標代幣
            mint_x = pool.get('mint_x', '')
            mint_y = pool.get('mint_y', '')
            target_mints = list(self.target_tokens.values())
            
            has_target_token = (mint_x in target_mints or mint_y in target_mints)
            if not has_target_token:
                return False
            
            # 檢查費用率
            fees_24h = self._safe_float(pool.get('fees_24h', 0))
            if tvl > 0:
                fee_rate = (fees_24h / tvl)
                if fee_rate < min_fee_rate:
                    return False
            
            # 注意：Meteora API 可能不提供創建時間，所以暫時跳過時間檢查
            # 如果需要，可以通過其他方式獲取池子創建時間
            
            return True
            
        except Exception as e:
            logger.error("meteora_criteria_check_failed", pool_address=pool.get('address'), error=str(e))
            return False
    
    def _format_pool_info(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """格式化池子信息"""
        try:
            tvl = self._safe_float(pool.get('liquidity', 0))
            volume_24h = self._safe_float(pool.get('trade_volume_24h', 0))
            fees_24h = self._safe_float(pool.get('fees_24h', 0))
            
            # 計算 APR
            apr = (fees_24h / tvl * 365 * 100) if tvl > 0 else 0.0
            
            # 獲取代幣符號
            mint_x = pool.get('mint_x', '')
            mint_y = pool.get('mint_y', '')
            
            token0_symbol = self._get_token_symbol(mint_x)
            token1_symbol = self._get_token_symbol(mint_y)
            
            return {
                "pool_id": pool.get('address', ''),
                "token0": {
                    "address": mint_x,
                    "symbol": token0_symbol,
                    "name": token0_symbol
                },
                "token1": {
                    "address": mint_y,
                    "symbol": token1_symbol,
                    "name": token1_symbol
                },
                "bin_step": self._safe_int(pool.get('bin_step', 0)),
                "tvl_usd": tvl,
                "volume_24h_usd": volume_24h,
                "fees_24h_usd": fees_24h,
                "apr": apr,
                "apy": self._safe_float(pool.get('apy', 0)),
                "current_price": self._safe_float(pool.get('current_price', 0)),
                "reserve_x": self._safe_float(pool.get('reserve_x', 0)),
                "reserve_y": self._safe_float(pool.get('reserve_y', 0)),
                "chain": "Solana",
                "dex": "Meteora DLMM v2"
            }
            
        except Exception as e:
            logger.error("format_meteora_pool_info_failed", pool_address=pool.get('address'), error=str(e))
            return None
    
    def _get_token_symbol(self, mint_address: str) -> str:
        """根據 mint 地址獲取代幣符號"""
        for symbol, mint in self.target_tokens.items():
            if mint == mint_address:
                return symbol
        
        # 如果不在目標代幣列表中，返回地址的前8位
        return mint_address[:8] if mint_address else "UNKNOWN"
    
    def _safe_float(self, value, default: float = 0.0) -> float:
        """安全的浮點數轉換"""
        if value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def _safe_int(self, value, default: int = 0) -> int:
        """安全的整數轉換"""
        if value is None:
            return default
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return default
    
    # Agno Tool 方法
    async def scan_solana_pools(self, min_tvl: str = "10000000") -> str:
        """Agno 工具方法：掃描 Solana 池子"""
        pools = await self.scan_pools(min_tvl=float(min_tvl))
        
        if not pools:
            return "No Solana pools found matching criteria"
        
        result = f"Found {len(pools)} Solana Meteora DLMM v2 pools:\n\n"
        for pool in pools[:10]:  # 限制顯示前10個
            result += f"• {pool['token0']['symbol']}/{pool['token1']['symbol']}\n"
            result += f"  TVL: ${pool['tvl_usd']:,.0f}\n"
            result += f"  APR: {pool['apr']:.2f}%\n"
            result += f"  Bin Step: {pool['bin_step']}\n\n"
        
        return result
    
    async def get_pool_details(self, pool_address: str) -> str:
        """Agno 工具方法：獲取特定池子詳情"""
        try:
            url = f"{self.api_base}/pair/{pool_address}"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    return f"Pool {pool_address} not found"
                
                data = await response.json()
                pool_info = self._format_pool_info(data)
                
                if pool_info:
                    return f"""
Pool: {pool_info['token0']['symbol']}/{pool_info['token1']['symbol']}
Address: {pool_info['pool_id']}
TVL: ${pool_info['tvl_usd']:,.0f}
24h Volume: ${pool_info['volume_24h_usd']:,.0f}
APR: {pool_info['apr']:.2f}%
Current Price: {pool_info['current_price']:.6f}
"""
                else:
                    return f"Failed to parse pool data for {pool_address}"
                    
        except Exception as e:
            logger.error("get_meteora_pool_details_failed", pool=pool_address, error=str(e))
            return f"Error getting pool details: {str(e)}"
