"""
DexRouterTool - DEX 路由工具
支持 Jupiter (Solana) 和 PancakeSwap (BSC) 交易路由
"""

import asyncio
import aiohttp
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

logger = structlog.get_logger(__name__)

class DexRouterTool:
    """
    DEX 路由工具
    支持跨鏈 DEX 交易和流動性操作
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.jupiter_api_url = config.get('jupiter_api_url', 'https://quote-api.jup.ag/v6')
        self.pancake_router_address = config.get('pancake_router_address', '0x13f4EA83D0bd40E75C8222255bc855a974568Dd4')
        self.slippage_tolerance = config.get('slippage_tolerance', 0.005)  # 0.5%
        self.max_hops = config.get('max_hops', 3)
        self.timeout = config.get('timeout', 30)
        
        # 常用代幣地址
        self.tokens = {
            'solana': {
                'SOL': 'So11111111111111111111111111111111111111112',
                'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
            },
            'bsc': {
                'BNB': '0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c',
                'USDT': '0x55d398326f99059fF775485246999027B3197955',
                'USDC': '0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d',
                'USD1': '0x55d398326f99059fF775485246999027B3197955'  # 示例地址
            }
        }
    
    async def get_quote(self, from_token: str, to_token: str, amount: float, chain: str) -> Dict[str, Any]:
        """
        獲取交易報價
        支持 Jupiter (Solana) 和 PancakeSwap (BSC)
        """
        logger.info("getting_dex_quote", 
                   from_token=from_token, 
                   to_token=to_token, 
                   amount=amount, 
                   chain=chain)
        
        try:
            if chain == 'solana':
                return await self._get_jupiter_quote(from_token, to_token, amount)
            elif chain == 'bsc':
                return await self._get_pancake_quote(from_token, to_token, amount)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported chain: {chain}'
                }
                
        except Exception as e:
            logger.error("quote_request_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_jupiter_quote(self, from_token: str, to_token: str, amount: float) -> Dict[str, Any]:
        """獲取 Jupiter 報價"""
        try:
            # 轉換代幣符號為地址
            from_mint = self._resolve_token_address(from_token, 'solana')
            to_mint = self._resolve_token_address(to_token, 'solana')
            
            if not from_mint or not to_mint:
                return {
                    'success': False,
                    'error': 'Invalid token symbols'
                }
            
            # 構建 Jupiter API 請求
            params = {
                'inputMint': from_mint,
                'outputMint': to_mint,
                'amount': int(amount * 1e9),  # 轉換為 lamports
                'slippageBps': int(self.slippage_tolerance * 10000),
                'maxAccounts': 20
            }
            
            url = f"{self.jupiter_api_url}/quote"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'success': True,
                            'chain': 'solana',
                            'dex': 'jupiter',
                            'from_token': from_token,
                            'to_token': to_token,
                            'input_amount': amount,
                            'output_amount': float(data.get('outAmount', 0)) / 1e9,
                            'price_impact': float(data.get('priceImpactPct', 0)),
                            'route_plan': data.get('routePlan', []),
                            'raw_quote': data
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'Jupiter API error: {response.status} - {error_text}'
                        }
                        
        except Exception as e:
            logger.error("jupiter_quote_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_pancake_quote(self, from_token: str, to_token: str, amount: float) -> Dict[str, Any]:
        """獲取 PancakeSwap 報價 (模擬)"""
        try:
            # 轉換代幣符號為地址
            from_address = self._resolve_token_address(from_token, 'bsc')
            to_address = self._resolve_token_address(to_token, 'bsc')
            
            if not from_address or not to_address:
                return {
                    'success': False,
                    'error': 'Invalid token symbols'
                }
            
            # 模擬 PancakeSwap 報價 (實際應該調用 PancakeSwap SDK 或 API)
            # 這裡使用簡單的匯率計算
            simulated_rate = 1.0
            if from_token == 'BNB' and to_token == 'USDT':
                simulated_rate = 300.0  # 1 BNB = 300 USDT
            elif from_token == 'USDT' and to_token == 'BNB':
                simulated_rate = 1/300.0  # 1 USDT = 1/300 BNB
            elif from_token == 'USDT' and to_token == 'USDC':
                simulated_rate = 1.0  # 1:1
            
            output_amount = amount * simulated_rate
            price_impact = 0.001  # 0.1% 模擬價格影響
            
            return {
                'success': True,
                'chain': 'bsc',
                'dex': 'pancakeswap',
                'from_token': from_token,
                'to_token': to_token,
                'input_amount': amount,
                'output_amount': output_amount,
                'price_impact': price_impact,
                'estimated_gas': 150000,
                'route': [from_address, to_address]
            }
            
        except Exception as e:
            logger.error("pancake_quote_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def execute_swap(self, quote: Dict[str, Any], wallet_address: str) -> Dict[str, Any]:
        """
        執行交換交易
        """
        logger.info("executing_swap", 
                   chain=quote.get('chain'),
                   dex=quote.get('dex'))
        
        try:
            chain = quote.get('chain')
            
            if chain == 'solana':
                return await self._execute_jupiter_swap(quote, wallet_address)
            elif chain == 'bsc':
                return await self._execute_pancake_swap(quote, wallet_address)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported chain: {chain}'
                }
                
        except Exception as e:
            logger.error("swap_execution_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_jupiter_swap(self, quote: Dict[str, Any], wallet_address: str) -> Dict[str, Any]:
        """執行 Jupiter 交換"""
        try:
            # 構建交換請求
            swap_request = {
                'quoteResponse': quote.get('raw_quote'),
                'userPublicKey': wallet_address,
                'wrapAndUnwrapSol': True,
                'dynamicComputeUnitLimit': True,
                'prioritizationFeeLamports': 'auto'
            }
            
            url = f"{self.jupiter_api_url}/swap"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(url, json=swap_request) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'success': True,
                            'chain': 'solana',
                            'transaction': data.get('swapTransaction'),
                            'setup_transaction': data.get('setupTransaction'),
                            'cleanup_transaction': data.get('cleanupTransaction'),
                            'message': 'Jupiter swap transaction prepared'
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'Jupiter swap error: {response.status} - {error_text}'
                        }
                        
        except Exception as e:
            logger.error("jupiter_swap_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_pancake_swap(self, quote: Dict[str, Any], wallet_address: str) -> Dict[str, Any]:
        """執行 PancakeSwap 交換 (模擬)"""
        try:
            # 模擬 PancakeSwap 交換執行
            # 實際應該構建 BSC 交易並發送到網絡
            
            tx_hash = f"0x{''.join(['a' if i % 2 == 0 else 'b' for i in range(64)])}"  # 模擬交易哈希
            
            return {
                'success': True,
                'chain': 'bsc',
                'tx_hash': tx_hash,
                'from_token': quote.get('from_token'),
                'to_token': quote.get('to_token'),
                'input_amount': quote.get('input_amount'),
                'output_amount': quote.get('output_amount'),
                'gas_used': quote.get('estimated_gas', 150000),
                'message': 'PancakeSwap transaction simulated'
            }
            
        except Exception as e:
            logger.error("pancake_swap_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def send_transaction(self, signed_tx: Dict[str, Any]) -> Dict[str, Any]:
        """
        發送已簽名的交易到區塊鏈網絡
        """
        logger.info("sending_transaction", chain=signed_tx.get('chain'))
        
        try:
            chain = signed_tx.get('chain')
            
            if chain == 'solana':
                return await self._send_solana_transaction(signed_tx)
            elif chain == 'bsc':
                return await self._send_bsc_transaction(signed_tx)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported chain: {chain}'
                }
                
        except Exception as e:
            logger.error("transaction_send_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _send_solana_transaction(self, signed_tx: Dict[str, Any]) -> Dict[str, Any]:
        """發送 Solana 交易"""
        try:
            # 模擬發送 Solana 交易
            tx_hash = f"solana_tx_{''.join(['1' if i % 2 == 0 else '2' for i in range(64)])}"
            
            return {
                'success': True,
                'chain': 'solana',
                'tx_hash': tx_hash,
                'message': 'Solana transaction sent successfully'
            }
            
        except Exception as e:
            logger.error("solana_tx_send_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _send_bsc_transaction(self, signed_tx: Dict[str, Any]) -> Dict[str, Any]:
        """發送 BSC 交易"""
        try:
            # 模擬發送 BSC 交易
            tx_hash = f"0x{''.join(['c' if i % 2 == 0 else 'd' for i in range(64)])}"
            
            return {
                'success': True,
                'chain': 'bsc',
                'tx_hash': tx_hash,
                'message': 'BSC transaction sent successfully'
            }
            
        except Exception as e:
            logger.error("bsc_tx_send_failed", error=str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    def _resolve_token_address(self, token_symbol: str, chain: str) -> Optional[str]:
        """解析代幣符號為地址"""
        try:
            if chain in self.tokens and token_symbol in self.tokens[chain]:
                return self.tokens[chain][token_symbol]
            
            # 如果已經是地址格式，直接返回
            if ((chain == 'solana' and len(token_symbol) == 44) or 
                (chain == 'bsc' and token_symbol.startswith('0x') and len(token_symbol) == 42)):
                return token_symbol
            
            return None
            
        except Exception as e:
            logger.error("token_address_resolution_failed", token=token_symbol, chain=chain, error=str(e))
            return None
    
    def get_supported_tokens(self, chain: str) -> Dict[str, str]:
        """獲取支持的代幣列表"""
        return self.tokens.get(chain, {})
    
    def get_router_info(self) -> Dict[str, Any]:
        """獲取路由器信息"""
        return {
            'jupiter_api_url': self.jupiter_api_url,
            'pancake_router_address': self.pancake_router_address,
            'slippage_tolerance': self.slippage_tolerance,
            'max_hops': self.max_hops,
            'supported_chains': list(self.tokens.keys())
        }
