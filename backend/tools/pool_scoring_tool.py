"""
PoolScoringTool - 基于6因子算法的DeFi池子动态评分工具
基于 src/agents/scorer_v2.py 的462行6因子动态评分算法实现
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import structlog
import math

try:
    from agno.sdk import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 如果Agno不可用，创建一个基础的Tool类作为fallback
    class Tool:
        name = ""
        description = ""
        
        async def run(self, *args, **kwargs):
            raise NotImplementedError("Agno Framework not available")

logger = structlog.get_logger(__name__)


class PoolScoringTool(Tool):
    """基于6因子算法的DeFi池子动态评分工具"""
    
    name = "pool_scoring"
    description = "基于6因子算法的DeFi池子动态评分工具，支持批量池子评分和排名"
    
    def __init__(self):
        # 基础权重配置（根据市场情况动态调整）
        self.base_weights = {
            'fee_tvl': 0.30,
            'volume_score': 0.25,
            'tvl_score': 0.15,
            'token_quality': 0.15,
            'liquidity_depth': 0.10,
            'volatility_score': 0.05
        }
        
        # 评分阈值配置
        self.thresholds = {
            'min_fee_tvl': 3.0,  # 最小年化费率 3%
            'min_volume_24h': 10000,  # 最小24h交易量 $10K
            'min_tvl': 50000,  # 最小TVL $50K
            'min_liquidity_depth': 50000,  # 最小流动性深度 $50K
            'max_volatility': 0.05,  # 最大日波动率 5%
            'stable_tokens': ['USDT', 'USDC', 'BUSD', 'DAI', 'FRAX'],
            'blue_chip_tokens': ['WBNB', 'WETH', 'BTCB', 'SOL', 'WSOL', 'BTC', 'ETH', 'BNB'],
            'high_risk_tokens': []  # 高风险代币黑名单
        }
        
        # 市场条件调整开关
        self.market_condition_adjustment = True
    
    async def run(self, pools: List[Dict], config: Dict = None) -> Dict[str, Any]:
        """
        对池子列表进行评分排名
        
        Args:
            pools: 池子数据列表，每个池子包含以下字段：
                - id: 池子地址
                - chain: 链名称 ('BSC', 'SOL')
                - tvl_usd: TVL (USD)
                - volume_24h: 24h交易量 (USD)
                - fee24h: 24h手续费收入 (USD)
                - fee_tvl: 年化费率 (%)
                - token0/token1: 代币符号
                - fee_rate: 手续费率
            config: 评分配置参数，可选参数：
                - weights: 自定义权重
                - thresholds: 自定义阈值
                - enable_market_adjustment: 是否启用市场调整
                
        Returns:
            Dict包含：
                - scored_pools: 评分后的池子列表
                - scoring_summary: 评分统计摘要
        """
        try:
            if not AGNO_AVAILABLE:
                return {
                    "error": "Agno Framework not available",
                    "scored_pools": [],
                    "scoring_summary": {}
                }
            
            if not pools:
                return {
                    "error": "没有提供池子数据",
                    "scored_pools": [],
                    "scoring_summary": {}
                }
            
            # 应用配置
            effective_config = self._apply_config(config)
            
            logger.info("pool_scoring_started", 
                       pools_count=len(pools), 
                       config=effective_config)
            
            # 分析市场条件
            market_condition = await self._analyze_market_condition()
            
            # 计算动态权重
            dynamic_weights = self._calculate_dynamic_weights(market_condition)
            
            # 批量评分
            scored_pools = []
            scoring_stats = {
                'total_pools': len(pools),
                'scored_pools': 0,
                'filtered_out': 0,
                'score_distribution': {},
                'top_factors': {}
            }
            
            for pool_data in pools:
                try:
                    # 标准化池子数据
                    pool_raw = self._standardize_pool_data(pool_data)
                    if not pool_raw:
                        scoring_stats['filtered_out'] += 1
                        continue
                    
                    # 获取详细信息
                    pool_detail = await self._get_pool_detailed_info(pool_raw)
                    if not pool_detail:
                        scoring_stats['filtered_out'] += 1
                        continue
                    
                    # 评分
                    pool_score, score_details = self._score_pool_enhanced(
                        pool_raw, pool_detail, dynamic_weights
                    )
                    
                    if pool_score:
                        scored_pool = {
                            **pool_data,
                            'score': pool_score['score'],
                            'hedgeable': pool_score['hedgeable'],
                            'score_details': score_details,
                            'weights_used': dynamic_weights._asdict() if hasattr(dynamic_weights, '_asdict') else vars(dynamic_weights)
                        }
                        scored_pools.append(scored_pool)
                        scoring_stats['scored_pools'] += 1
                    else:
                        scoring_stats['filtered_out'] += 1
                        
                except Exception as e:
                    logger.warning("pool_scoring_failed",
                                 pool_id=pool_data.get('id'),
                                 error=str(e))
                    scoring_stats['filtered_out'] += 1
                    continue
            
            # 按评分排序
            scored_pools.sort(key=lambda p: p['score'], reverse=True)
            
            # 生成统计摘要
            scoring_summary = self._generate_scoring_summary(
                scored_pools, scoring_stats, market_condition, dynamic_weights
            )
            
            result = {
                "scored_pools": scored_pools,
                "scoring_summary": scoring_summary
            }
            
            logger.info("pool_scoring_completed",
                       total_pools=len(pools),
                       scored_pools=len(scored_pools),
                       filtered_out=scoring_stats['filtered_out'])
            
            return result
            
        except Exception as e:
            logger.error("pool_scoring_tool_failed", error=str(e))
            return {
                "error": str(e),
                "scored_pools": [],
                "scoring_summary": {}
            }
    
    def _apply_config(self, config: Optional[Dict]) -> Dict:
        """应用配置参数"""
        effective_config = {
            'weights': self.base_weights.copy(),
            'thresholds': self.thresholds.copy(),
            'enable_market_adjustment': self.market_condition_adjustment
        }
        
        if config:
            if 'weights' in config:
                effective_config['weights'].update(config['weights'])
            if 'thresholds' in config:
                effective_config['thresholds'].update(config['thresholds'])
            if 'enable_market_adjustment' in config:
                effective_config['enable_market_adjustment'] = config['enable_market_adjustment']
        
        return effective_config
    
    async def _analyze_market_condition(self) -> Dict[str, Any]:
        """分析当前市场情况"""
        # 简化的市场分析，实际应该连接外部数据源
        return {
            "volatility_level": "medium",  # low, medium, high
            "volume_trend": "stable",      # increasing, stable, decreasing
            "market_sentiment": "neutral", # bullish, neutral, bearish
            "risk_appetite": "moderate"    # conservative, moderate, aggressive
        }
    
    def _calculate_dynamic_weights(self, market_condition: Dict[str, Any]) -> object:
        """根据市场情况计算动态权重"""
        class DynamicWeights:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)
            
            def normalize(self):
                total = sum(getattr(self, attr) for attr in ['fee_tvl', 'volume_score', 'tvl_score', 
                                                           'token_quality', 'liquidity_depth', 'volatility_score'])
                if total == 0:
                    return self
                factor = 1.0 / total
                for attr in ['fee_tvl', 'volume_score', 'tvl_score', 'token_quality', 'liquidity_depth', 'volatility_score']:
                    setattr(self, attr, getattr(self, attr) * factor)
                return self
        
        weights = DynamicWeights(**self.base_weights)
        
        if not self.market_condition_adjustment:
            return weights.normalize()
        
        # 根据市场波动率调整权重
        volatility_level = market_condition.get("volatility_level", "medium")
        if volatility_level == "high":
            weights.tvl_score *= 1.2
            weights.liquidity_depth *= 1.3
            weights.fee_tvl *= 0.8
            weights.volatility_score *= 1.5
        elif volatility_level == "low":
            weights.fee_tvl *= 1.2
            weights.volume_score *= 1.1
            weights.volatility_score *= 0.5
        
        # 根据市场情绪调整
        sentiment = market_condition.get("market_sentiment", "neutral")
        if sentiment == "bearish":
            weights.token_quality *= 1.3
            weights.tvl_score *= 1.2
            weights.fee_tvl *= 0.9
        elif sentiment == "bullish":
            weights.fee_tvl *= 1.1
            weights.volume_score *= 1.2
            weights.token_quality *= 0.9
        
        return weights.normalize()
    
    def _standardize_pool_data(self, pool_data: Dict) -> Optional[Dict]:
        """标准化池子数据格式"""
        try:
            # 基本字段验证
            required_fields = ['id', 'tvl_usd', 'volume_24h', 'fee24h']
            for field in required_fields:
                if field not in pool_data or pool_data[field] is None:
                    return None
            
            standardized = {
                'id': pool_data['id'],
                'chain': pool_data.get('chain', 'UNKNOWN'),
                'tvl_usd': float(pool_data['tvl_usd']),
                'volume_24h': float(pool_data['volume_24h']),
                'fee24h': float(pool_data['fee24h']),
                'fee_tvl': float(pool_data.get('fee_tvl', 0)),
                'token0': pool_data.get('token0', ''),
                'token1': pool_data.get('token1', ''),
                'fee_rate': float(pool_data.get('fee_rate', 0))
            }
            
            return standardized
            
        except (ValueError, TypeError, KeyError):
            return None
    
    async def _get_pool_detailed_info(self, pool_raw: Dict) -> Optional[Dict]:
        """获取池子的详细信息"""
        # 简化版本，实际应该从区块链或API获取详细信息
        try:
            return {
                'token0_symbol': pool_raw.get('token0', 'TOKEN0'),
                'token1_symbol': pool_raw.get('token1', 'TOKEN1'),
                'token0_address': f"0x{'0'*40}",  # 占位符
                'token1_address': f"0x{'1'*40}",  # 占位符
                'fee_rate': pool_raw.get('fee_rate', 0.0025),
                'liquidity_depth': pool_raw['tvl_usd'] * 0.1,  # 估算可用流动性为TVL的10%
                'price_volatility': 0.02,  # 2% 日波动率，实际应该计算
                'volume_24h': pool_raw['volume_24h'],
                'historical_apy': pool_raw.get('fee_tvl', 0) / 100  # 转换为小数
            }
        except Exception:
            return None
    
    def _score_pool_enhanced(self, pool_raw: Dict, pool_detail: Dict, weights: object) -> Tuple[Optional[Dict], Dict[str, Any]]:
        """增强版池子评分算法"""
        try:
            # 基本过滤
            if not self._meets_enhanced_criteria(pool_raw, pool_detail):
                return None, {"reason": "failed_basic_criteria"}
            
            # 计算各项评分
            factor_scores = {
                'fee_score': self._calculate_fee_score_v2(pool_raw, pool_detail),
                'volume_score': self._calculate_volume_score_v2(pool_raw, pool_detail),
                'tvl_score': self._calculate_tvl_score_v2(pool_raw, pool_detail),
                'token_quality_score': self._calculate_token_quality_score_v2(pool_detail),
                'liquidity_depth_score': self._calculate_liquidity_depth_score(pool_detail),
                'volatility_score': self._calculate_volatility_score(pool_detail)
            }
            
            # 计算加权综合评分
            final_score = (
                factor_scores['fee_score'] * weights.fee_tvl +
                factor_scores['volume_score'] * weights.volume_score +
                factor_scores['tvl_score'] * weights.tvl_score +
                factor_scores['token_quality_score'] * weights.token_quality +
                factor_scores['liquidity_depth_score'] * weights.liquidity_depth +
                factor_scores['volatility_score'] * weights.volatility_score
            )
            
            # 判断是否可对冲
            hedgeable = self._is_pool_hedgeable(pool_detail)
            
            pool_score = {
                'score': round(final_score * 100, 2),  # 转换为百分制
                'hedgeable': hedgeable
            }
            
            score_details = {
                'factor_scores': factor_scores,
                'weights_applied': vars(weights),
                'hedgeable_analysis': {
                    'hedgeable': hedgeable,
                    'reasons': self._get_hedgeable_reasons(pool_detail)
                }
            }
            
            return pool_score, score_details
            
        except Exception as e:
            logger.warning("pool_scoring_calculation_failed", error=str(e))
            return None, {"reason": f"calculation_error: {str(e)}"}
    
    def _meets_enhanced_criteria(self, pool_raw: Dict, pool_detail: Dict) -> bool:
        """检查池子是否满足基本标准"""
        try:
            # TVL检查
            if pool_raw['tvl_usd'] < self.thresholds['min_tvl']:
                return False
            
            # 交易量检查
            if pool_raw['volume_24h'] < self.thresholds['min_volume_24h']:
                return False
            
            # 费率合理性检查
            if pool_raw['fee_tvl'] < self.thresholds['min_fee_tvl']:
                return False
            
            # 流动性深度检查
            if pool_detail['liquidity_depth'] < self.thresholds['min_liquidity_depth']:
                return False
            
            # 波动率检查
            if pool_detail['price_volatility'] > self.thresholds['max_volatility']:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _calculate_fee_score_v2(self, pool_raw: Dict, pool_detail: Dict) -> float:
        """计算费率评分 (0-1)"""
        fee_tvl = pool_raw.get('fee_tvl', 0)
        if fee_tvl <= 0:
            return 0.0
        
        # 使用对数函数平滑评分
        normalized_score = math.log(1 + fee_tvl / 10) / math.log(1 + 50)  # 50%为满分参考
        return min(normalized_score, 1.0)
    
    def _calculate_volume_score_v2(self, pool_raw: Dict, pool_detail: Dict) -> float:
        """计算交易量评分 (0-1)"""
        volume = pool_raw.get('volume_24h', 0)
        if volume <= 0:
            return 0.0
        
        # 使用对数函数评分
        normalized_score = math.log(1 + volume / 100000) / math.log(1 + 10)  # 1M为较高分参考
        return min(normalized_score, 1.0)
    
    def _calculate_tvl_score_v2(self, pool_raw: Dict, pool_detail: Dict) -> float:
        """计算TVL评分 (0-1)"""
        tvl = pool_raw.get('tvl_usd', 0)
        if tvl <= 0:
            return 0.0
        
        # 使用对数函数评分
        normalized_score = math.log(1 + tvl / 1000000) / math.log(1 + 10)  # 10M为较高分参考
        return min(normalized_score, 1.0)
    
    def _calculate_token_quality_score_v2(self, pool_detail: Dict) -> float:
        """计算代币质量评分 (0-1)"""
        token0 = pool_detail.get('token0_symbol', '').upper()
        token1 = pool_detail.get('token1_symbol', '').upper()
        
        score = 0.5  # 基础分
        
        # 蓝筹代币加分
        for token in [token0, token1]:
            if token in self.thresholds['blue_chip_tokens']:
                score += 0.2
        
        # 稳定币加分
        for token in [token0, token1]:
            if token in self.thresholds['stable_tokens']:
                score += 0.15
        
        # 高风险代币扣分
        for token in [token0, token1]:
            if token in self.thresholds['high_risk_tokens']:
                score -= 0.3
        
        return max(0.0, min(score, 1.0))
    
    def _calculate_liquidity_depth_score(self, pool_detail: Dict) -> float:
        """计算流动性深度评分 (0-1)"""
        depth = pool_detail.get('liquidity_depth', 0)
        if depth <= 0:
            return 0.0
        
        # 使用对数函数评分
        normalized_score = math.log(1 + depth / 100000) / math.log(1 + 10)  # 1M为较高分参考
        return min(normalized_score, 1.0)
    
    def _calculate_volatility_score(self, pool_detail: Dict) -> float:
        """计算波动率评分 (0-1，波动率越低分数越高)"""
        volatility = pool_detail.get('price_volatility', 0)
        if volatility < 0:
            return 0.0
        
        # 反向评分：波动率越低分数越高
        max_acceptable_volatility = 0.1  # 10%
        if volatility >= max_acceptable_volatility:
            return 0.0
        
        return 1.0 - (volatility / max_acceptable_volatility)
    
    def _is_pool_hedgeable(self, pool_detail: Dict) -> bool:
        """判断池子是否可对冲"""
        token0 = pool_detail.get('token0_symbol', '').upper()
        token1 = pool_detail.get('token1_symbol', '').upper()
        
        # 包含蓝筹代币的池子通常可对冲
        hedgeable_tokens = self.thresholds['blue_chip_tokens'] + ['USDT', 'USDC']
        
        return (token0 in hedgeable_tokens or token1 in hedgeable_tokens)
    
    def _get_hedgeable_reasons(self, pool_detail: Dict) -> List[str]:
        """获取可对冲性分析原因"""
        reasons = []
        token0 = pool_detail.get('token0_symbol', '').upper()
        token1 = pool_detail.get('token1_symbol', '').upper()
        
        if token0 in self.thresholds['blue_chip_tokens']:
            reasons.append(f"{token0} is a blue chip token")
        if token1 in self.thresholds['blue_chip_tokens']:
            reasons.append(f"{token1} is a blue chip token")
        if token0 in self.thresholds['stable_tokens']:
            reasons.append(f"{token0} is a stable token")
        if token1 in self.thresholds['stable_tokens']:
            reasons.append(f"{token1} is a stable token")
        
        if not reasons:
            reasons.append("No hedgeable tokens identified")
        
        return reasons
    
    def _generate_scoring_summary(self, scored_pools: List[Dict], stats: Dict, 
                                market_condition: Dict, weights: object) -> Dict[str, Any]:
        """生成评分统计摘要"""
        if not scored_pools:
            return {
                "total_pools_analyzed": stats['total_pools'],
                "pools_scored": 0,
                "pools_filtered_out": stats['filtered_out'],
                "average_score": 0,
                "score_distribution": {},
                "market_condition": market_condition,
                "weights_used": vars(weights),
                "timestamp": datetime.utcnow().isoformat()
            }
        
        scores = [p['score'] for p in scored_pools]
        
        # 分数分布
        score_ranges = {
            '90-100': len([s for s in scores if s >= 90]),
            '80-89': len([s for s in scores if 80 <= s < 90]),
            '70-79': len([s for s in scores if 70 <= s < 80]),
            '60-69': len([s for s in scores if 60 <= s < 70]),
            'below_60': len([s for s in scores if s < 60])
        }
        
        return {
            "total_pools_analyzed": stats['total_pools'],
            "pools_scored": len(scored_pools),
            "pools_filtered_out": stats['filtered_out'],
            "average_score": round(sum(scores) / len(scores), 2),
            "highest_score": max(scores),
            "lowest_score": min(scores),
            "hedgeable_pools": len([p for p in scored_pools if p.get('hedgeable', False)]),
            "score_distribution": score_ranges,
            "market_condition": market_condition,
            "weights_used": vars(weights),
            "top_3_pools": scored_pools[:3],
            "timestamp": datetime.utcnow().isoformat()
        }


# 为了向后兼容，如果Agno不可用，提供同步版本
class PoolScoringToolSync:
    """PoolScoringTool的同步版本，用于非Agno环境"""
    
    def __init__(self):
        self.tool = PoolScoringTool()
    
    def score_pools(self, pools: List[Dict], config: Dict = None) -> Dict[str, Any]:
        """同步版本的池子评分"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.tool.run(pools, config))
            finally:
                loop.close()
        except Exception as e:
            logger.error("sync_pool_scoring_failed", error=str(e))
            return {
                "error": str(e),
                "scored_pools": [],
                "scoring_summary": {}
            }


# 导出标准接口
__all__ = ['PoolScoringTool', 'PoolScoringToolSync', 'AGNO_AVAILABLE']