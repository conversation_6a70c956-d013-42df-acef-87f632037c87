"""
PancakeSwap V3 Subgraph Tool for Agno Framework
用於掃描 BSC PancakeSwap V3 池子的 Agno 工具
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog

try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        def __init__(self):
            pass

logger = structlog.get_logger(__name__)

class PancakeSwapTool(Tool if AGNO_AVAILABLE else object):
    """PancakeSwap V3 Subgraph 工具 - 掃描 BSC 池子"""
    
    def __init__(self):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.subgraph_url = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 目標代幣地址 (BSC)
        self.target_tokens = {
            "BNB": "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c",  # WBNB
            "USDT": "0x55d398326f99059ff775485246999027b3197955",
            "USDC": "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d", 
            "USD1": "0x55d398326f99059ff775485246999027b3197955",  # 使用 USDT 地址作為示例
            "BUSD": "0xe9e7cea3dedca5984780bafc599bd69add087d56"
        }
    
    async def initialize(self):
        """初始化 HTTP 會話"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'DyFlow-PancakeSwap-Tool/1.0'
            }
        )
        logger.info("pancakeswap_tool_initialized")
    
    async def cleanup(self):
        """清理資源"""
        if self.session:
            await self.session.close()
        logger.info("pancakeswap_tool_cleanup_completed")
    
    async def scan_pools(self, min_tvl: float = 10000000, max_age_hours: int = 48, min_fee_rate: float = 0.05) -> List[Dict[str, Any]]:
        """
        掃描符合條件的 PancakeSwap V3 池子
        
        Args:
            min_tvl: 最小 TVL (默認 $10M)
            max_age_hours: 最大創建時間 (默認 48 小時)
            min_fee_rate: 最小費用率 (默認 5%)
            
        Returns:
            符合條件的池子列表
        """
        try:
            # 計算時間戳閾值
            current_time = datetime.now()
            min_timestamp = int((current_time - timedelta(hours=max_age_hours)).timestamp())
            
            query = """
            query scanPools($minTvl: String!, $minTimestamp: String!) {
              pools(
                first: 100
                orderBy: totalValueLockedUSD
                orderDirection: desc
                where: { 
                  totalValueLockedUSD_gt: $minTvl
                  createdAtTimestamp_gt: $minTimestamp
                }
              ) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                sqrtPrice
                tick
                liquidity
                volumeUSD
                totalValueLockedUSD
                token0Price
                token1Price
                feeGrowthGlobal0X128
                feeGrowthGlobal1X128
                createdAtTimestamp
                poolDayData(first: 1, orderBy: date, orderDirection: desc) {
                  date
                  volumeUSD
                  feesUSD
                  tvlUSD
                }
              }
            }
            """
            
            variables = {
                "minTvl": str(int(min_tvl)),
                "minTimestamp": str(min_timestamp)
            }
            
            result = await self._execute_query(query, variables)
            pools = result.get('data', {}).get('pools', [])
            
            # 過濾池子
            filtered_pools = []
            for pool in pools:
                if self._meets_criteria(pool, min_fee_rate):
                    pool_info = self._format_pool_info(pool)
                    filtered_pools.append(pool_info)
            
            logger.info("pools_scanned", 
                       total_found=len(pools), 
                       filtered_count=len(filtered_pools),
                       min_tvl=min_tvl,
                       max_age_hours=max_age_hours,
                       min_fee_rate=min_fee_rate)
            
            return filtered_pools
            
        except Exception as e:
            logger.error("scan_pools_failed", error=str(e))
            return []
    
    async def get_top_pools(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        獲取頂級池子（按 TVL 排序）
        
        Args:
            limit: 返回池子數量限制
            
        Returns:
            頂級池子列表
        """
        try:
            query = """
            query getTopPools($limit: Int!) {
              pools(
                first: $limit
                orderBy: totalValueLockedUSD
                orderDirection: desc
                where: { totalValueLockedUSD_gt: "1000000" }
              ) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                totalValueLockedUSD
                volumeUSD
                token0Price
                token1Price
                createdAtTimestamp
                poolDayData(first: 1, orderBy: date, orderDirection: desc) {
                  feesUSD
                  volumeUSD
                }
              }
            }
            """
            
            variables = {"limit": limit}
            
            result = await self._execute_query(query, variables)
            pools = result.get('data', {}).get('pools', [])
            
            formatted_pools = []
            for pool in pools:
                pool_info = self._format_pool_info(pool)
                formatted_pools.append(pool_info)
            
            logger.info("top_pools_retrieved", count=len(formatted_pools))
            return formatted_pools
            
        except Exception as e:
            logger.error("get_top_pools_failed", error=str(e))
            return []
    
    def _meets_criteria(self, pool: Dict[str, Any], min_fee_rate: float) -> bool:
        """檢查池子是否符合過濾條件"""
        try:
            # 檢查是否包含目標代幣
            token0_addr = pool['token0']['id'].lower()
            token1_addr = pool['token1']['id'].lower()
            target_addrs = [addr.lower() for addr in self.target_tokens.values()]
            
            has_target_token = (token0_addr in target_addrs or token1_addr in target_addrs)
            
            # 計算費用率
            tvl = float(pool.get('totalValueLockedUSD', 0))
            pool_day_data = pool.get('poolDayData', [])
            
            if tvl > 0 and pool_day_data:
                daily_fees = float(pool_day_data[0].get('feesUSD', 0))
                fee_rate = (daily_fees / tvl) if tvl > 0 else 0
                meets_fee_criteria = fee_rate >= min_fee_rate
            else:
                meets_fee_criteria = False
            
            return has_target_token and meets_fee_criteria
            
        except Exception as e:
            logger.error("criteria_check_failed", pool_id=pool.get('id'), error=str(e))
            return False
    
    def _format_pool_info(self, pool: Dict[str, Any]) -> Dict[str, Any]:
        """格式化池子信息"""
        try:
            tvl = float(pool.get('totalValueLockedUSD', 0))
            volume_24h = float(pool.get('volumeUSD', 0))
            fee_tier = int(pool.get('feeTier', 0))
            
            # 計算 APR
            pool_day_data = pool.get('poolDayData', [])
            if pool_day_data and tvl > 0:
                daily_fees = float(pool_day_data[0].get('feesUSD', 0))
                apr = (daily_fees / tvl) * 365 * 100
            else:
                apr = 0.0
            
            # 計算創建時間
            created_timestamp = int(pool.get('createdAtTimestamp', 0))
            created_date = datetime.fromtimestamp(created_timestamp) if created_timestamp > 0 else None
            
            return {
                "pool_id": pool['id'],
                "token0": {
                    "address": pool['token0']['id'],
                    "symbol": pool['token0']['symbol'],
                    "name": pool['token0']['name'],
                    "decimals": int(pool['token0']['decimals'])
                },
                "token1": {
                    "address": pool['token1']['id'], 
                    "symbol": pool['token1']['symbol'],
                    "name": pool['token1']['name'],
                    "decimals": int(pool['token1']['decimals'])
                },
                "fee_tier": fee_tier,
                "tvl_usd": tvl,
                "volume_24h_usd": volume_24h,
                "apr": apr,
                "token0_price": float(pool.get('token0Price', 0)),
                "token1_price": float(pool.get('token1Price', 0)),
                "created_at": created_date.isoformat() if created_date else None,
                "created_timestamp": created_timestamp,
                "chain": "BSC",
                "dex": "PancakeSwap V3"
            }
            
        except Exception as e:
            logger.error("format_pool_info_failed", pool_id=pool.get('id'), error=str(e))
            return {}
    
    async def _execute_query(self, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """執行 GraphQL 查詢"""
        try:
            payload = {
                "query": query,
                "variables": variables
            }
            
            async with self.session.post(self.subgraph_url, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"GraphQL request failed: {response.status} - {error_text}")
                
                result = await response.json()
                
                if 'errors' in result:
                    errors = result['errors']
                    raise Exception(f"GraphQL query errors: {errors}")
                
                return result
                
        except Exception as e:
            logger.error("execute_query_failed", error=str(e))
            raise
    
    # Agno Tool 方法
    async def scan_bsc_pools(self, min_tvl: str = "10000000") -> str:
        """Agno 工具方法：掃描 BSC 池子"""
        pools = await self.scan_pools(min_tvl=float(min_tvl))
        
        if not pools:
            return "No pools found matching criteria"
        
        result = f"Found {len(pools)} BSC PancakeSwap V3 pools:\n\n"
        for pool in pools[:10]:  # 限制顯示前10個
            result += f"• {pool['token0']['symbol']}/{pool['token1']['symbol']}\n"
            result += f"  TVL: ${pool['tvl_usd']:,.0f}\n"
            result += f"  APR: {pool['apr']:.2f}%\n"
            result += f"  Fee Tier: {pool['fee_tier']/10000:.2f}%\n\n"
        
        return result
    
    async def get_pool_info(self, pool_address: str) -> str:
        """Agno 工具方法：獲取特定池子信息"""
        # 這裡可以實現獲取特定池子的詳細信息
        return f"Pool info for {pool_address} - Implementation needed"
