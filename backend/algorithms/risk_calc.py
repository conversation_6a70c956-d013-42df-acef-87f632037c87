"""
风险计算模块
实现无常损失估算和各种风险指标计算
IL_est = volatility² × 0.25  # 无常损失预估公式
"""

import math
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum
import structlog

logger = structlog.get_logger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """风险指标数据类"""
    impermanent_loss_risk: float      # 无常损失风险 (%)
    volatility_risk: float            # 波动率风险评分 (0-1)
    liquidity_risk: float             # 流动性风险评分 (0-1)
    concentration_risk: float         # 集中度风险评分 (0-1)
    smart_contract_risk: float        # 智能合约风险评分 (0-1)
    overall_risk_score: float         # 综合风险评分 (0-1)
    risk_level: RiskLevel             # 风险等级
    
    # 详细分析
    risk_factors: List[str]           # 风险因素列表
    mitigation_suggestions: List[str] # 风险缓解建议
    confidence: float                 # 风险评估置信度


class RiskCalculator:
    """风险计算器"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        """
        初始化风险计算器
        
        Args:
            params: 风险计算参数配置
        """
        # 默认参数
        default_params = {
            'il_coefficient': 0.25,           # 无常损失系数
            'volatility_threshold_low': 10.0, # 低波动阈值
            'volatility_threshold_high': 40.0,# 高波动阈值
            'min_tvl_safe': 1000000.0,       # 安全TVL阈值
            'min_volume_ratio': 0.01,         # 最小交易量比例
            'max_concentration': 0.3,         # 最大集中度
        }
        
        self.params = default_params.copy()
        if params:
            self.params.update(params)
        
        # 代币风险映射
        self.token_risk_mapping = self._init_token_risk_mapping()
        
        # 协议风险映射
        self.protocol_risk_mapping = self._init_protocol_risk_mapping()
        
        logger.debug("risk_calculator_initialized", params=self.params)
    
    def estimate_impermanent_loss(self, 
                                 token0: str, 
                                 token1: str, 
                                 volatility: float,
                                 time_horizon_days: int = 30) -> float:
        """
        估算无常损失风险
        公式: IL_est = volatility² × 0.25
        
        Args:
            token0: 代币0符号
            token1: 代币1符号
            volatility: 价格波动率 (%)
            time_horizon_days: 时间范围(天)
            
        Returns:
            无常损失风险估算 (%)
        """
        try:
            # 基础无常损失计算
            # 将百分比转换为小数
            vol_decimal = volatility / 100.0
            
            # 基础公式: IL_est = volatility² × coefficient
            base_il = (vol_decimal ** 2) * self.params['il_coefficient']
            
            # 时间调整因子(假设风险随时间平方根增长)
            time_factor = math.sqrt(time_horizon_days / 30.0)
            
            # 代币相关性调整
            correlation_factor = self._get_token_correlation_factor(token0, token1)
            
            # 最终无常损失估算
            il_estimate = base_il * time_factor * correlation_factor
            
            # 转换回百分比并限制在合理范围内
            il_percentage = min(il_estimate * 100, 50.0)  # 最大50%
            
            logger.debug("impermanent_loss_estimated",
                        token0=token0,
                        token1=token1,
                        volatility=volatility,
                        il_estimate=il_percentage,
                        correlation_factor=correlation_factor)
            
            return il_percentage
            
        except Exception as e:
            logger.error("impermanent_loss_calculation_failed",
                        token0=token0,
                        token1=token1,
                        volatility=volatility,
                        error=str(e))
            # 返回保守估算
            return min(volatility * 0.5, 30.0)
    
    def calculate_comprehensive_risk(self, 
                                   pool_data: Dict[str, Any]) -> RiskMetrics:
        """
        计算综合风险指标
        
        Args:
            pool_data: 池子数据
            
        Returns:
            风险指标对象
        """
        try:
            # 提取基础数据
            token0 = pool_data.get('token0', '').upper()
            token1 = pool_data.get('token1', '').upper()
            volatility = float(pool_data.get('volatility', 20.0))
            tvl = float(pool_data.get('tvl_usd', pool_data.get('tvl', 0)))
            volume_24h = float(pool_data.get('volume_24h', 0))
            chain = pool_data.get('chain', '')
            protocol = pool_data.get('protocol', '')
            
            # 1. 无常损失风险
            il_risk = self.estimate_impermanent_loss(token0, token1, volatility)
            
            # 2. 波动率风险
            volatility_risk = self._calculate_volatility_risk(volatility)
            
            # 3. 流动性风险
            liquidity_risk = self._calculate_liquidity_risk(tvl, volume_24h)
            
            # 4. 集中度风险
            concentration_risk = self._calculate_concentration_risk(token0, token1)
            
            # 5. 智能合约风险
            sc_risk = self._calculate_smart_contract_risk(chain, protocol)
            
            # 计算综合风险评分
            overall_risk = self._calculate_overall_risk(
                il_risk, volatility_risk, liquidity_risk, 
                concentration_risk, sc_risk
            )
            
            # 确定风险等级
            risk_level = self._determine_risk_level(overall_risk)
            
            # 识别风险因素
            risk_factors = self._identify_risk_factors(
                pool_data, il_risk, volatility_risk, 
                liquidity_risk, concentration_risk, sc_risk
            )
            
            # 生成缓解建议
            mitigation_suggestions = self._generate_mitigation_suggestions(
                risk_factors, pool_data
            )
            
            # 计算置信度
            confidence = self._calculate_risk_confidence(pool_data)
            
            return RiskMetrics(
                impermanent_loss_risk=il_risk,
                volatility_risk=volatility_risk,
                liquidity_risk=liquidity_risk,
                concentration_risk=concentration_risk,
                smart_contract_risk=sc_risk,
                overall_risk_score=overall_risk,
                risk_level=risk_level,
                risk_factors=risk_factors,
                mitigation_suggestions=mitigation_suggestions,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error("comprehensive_risk_calculation_failed",
                        pool_id=pool_data.get('pool_id'),
                        error=str(e))
            
            # 返回保守的高风险评估
            return self._get_default_high_risk_metrics()
    
    def _get_token_correlation_factor(self, token0: str, token1: str) -> float:
        """获取代币相关性调整因子"""
        # 稳定币列表
        stablecoins = {'USDT', 'USDC', 'BUSD', 'DAI', 'FRAX', 'TUSD', 'FDUSD'}
        
        # 相关代币组
        correlated_groups = [
            {'BTC', 'BTCB', 'WBTC'},
            {'ETH', 'WETH'},
            {'BNB', 'WBNB'},
            stablecoins
        ]
        
        token0 = token0.upper()
        token1 = token1.upper()
        
        # 稳定币对：相关性很高，无常损失风险很低
        if token0 in stablecoins and token1 in stablecoins:
            return 0.1  # 大幅降低风险
        
        # 检查是否在相关组中
        for group in correlated_groups:
            if token0 in group and token1 in group:
                return 0.6  # 降低风险
        
        # 一个稳定币 + 一个主流币
        major_coins = {'BTC', 'ETH', 'BNB', 'SOL', 'BTCB', 'WBNB', 'WETH', 'WBTC'}
        if ((token0 in stablecoins and token1 in major_coins) or
            (token1 in stablecoins and token0 in major_coins)):
            return 0.8  # 轻微降低风险
        
        # 默认情况：无相关性
        return 1.0
    
    def _calculate_volatility_risk(self, volatility: float) -> float:
        """计算波动率风险评分"""
        if volatility <= self.params['volatility_threshold_low']:
            return 0.2  # 低风险
        elif volatility <= self.params['volatility_threshold_high']:
            # 线性插值
            ratio = (volatility - self.params['volatility_threshold_low']) / (
                self.params['volatility_threshold_high'] - self.params['volatility_threshold_low']
            )
            return 0.2 + ratio * 0.6  # 中等风险
        else:
            return min(1.0, 0.8 + (volatility - self.params['volatility_threshold_high']) / 100)
    
    def _calculate_liquidity_risk(self, tvl: float, volume_24h: float) -> float:
        """计算流动性风险评分"""
        # TVL风险
        if tvl < 10000:
            tvl_risk = 1.0  # 极高风险
        elif tvl < 100000:
            tvl_risk = 0.8  # 高风险
        elif tvl < self.params['min_tvl_safe']:
            tvl_risk = 0.5  # 中等风险
        else:
            tvl_risk = 0.2  # 低风险
        
        # 交易活跃度风险
        if tvl > 0:
            volume_ratio = volume_24h / tvl
            if volume_ratio < 0.001:
                activity_risk = 1.0  # 极低活跃度
            elif volume_ratio < self.params['min_volume_ratio']:
                activity_risk = 0.7  # 低活跃度
            elif volume_ratio > 5.0:
                activity_risk = 0.8  # 过度活跃(可能不稳定)
            else:
                activity_risk = 0.2  # 正常活跃度
        else:
            activity_risk = 1.0
        
        # 综合流动性风险
        return max(tvl_risk, activity_risk)
    
    def _calculate_concentration_risk(self, token0: str, token1: str) -> float:
        """计算集中度风险评分"""
        # 基于代币类型的风险评估
        token0_risk = self.token_risk_mapping.get(token0.upper(), 0.5)
        token1_risk = self.token_risk_mapping.get(token1.upper(), 0.5)
        
        # 取平均值作为集中度风险
        return (token0_risk + token1_risk) / 2
    
    def _calculate_smart_contract_risk(self, chain: str, protocol: str) -> float:
        """计算智能合约风险评分"""
        # 链风险
        chain_risk = {
            'ethereum': 0.1,
            'bsc': 0.2,
            'polygon': 0.3,
            'solana': 0.2,
            'avalanche': 0.25,
            'arbitrum': 0.15,
            'optimism': 0.15
        }.get(chain.lower(), 0.5)
        
        # 协议风险
        protocol_risk = self.protocol_risk_mapping.get(protocol.lower(), 0.5)
        
        # 综合智能合约风险
        return max(chain_risk, protocol_risk)
    
    def _calculate_overall_risk(self, 
                              il_risk: float,
                              volatility_risk: float,
                              liquidity_risk: float,
                              concentration_risk: float,
                              sc_risk: float) -> float:
        """计算综合风险评分"""
        # 风险权重
        weights = {
            'il': 0.3,          # 无常损失权重
            'volatility': 0.25, # 波动率权重
            'liquidity': 0.25,  # 流动性权重
            'concentration': 0.1, # 集中度权重
            'smart_contract': 0.1 # 智能合约权重
        }
        
        # 无常损失风险需要归一化(从百分比转换)
        il_risk_normalized = min(il_risk / 50.0, 1.0)
        
        overall_risk = (
            weights['il'] * il_risk_normalized +
            weights['volatility'] * volatility_risk +
            weights['liquidity'] * liquidity_risk +
            weights['concentration'] * concentration_risk +
            weights['smart_contract'] * sc_risk
        )
        
        return min(overall_risk, 1.0)
    
    def _determine_risk_level(self, overall_risk: float) -> RiskLevel:
        """确定风险等级"""
        if overall_risk < 0.25:
            return RiskLevel.LOW
        elif overall_risk < 0.5:
            return RiskLevel.MEDIUM
        elif overall_risk < 0.75:
            return RiskLevel.HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _identify_risk_factors(self, 
                             pool_data: Dict[str, Any],
                             il_risk: float,
                             volatility_risk: float,
                             liquidity_risk: float,
                             concentration_risk: float,
                             sc_risk: float) -> List[str]:
        """识别具体风险因素"""
        factors = []
        
        if il_risk > 10:
            factors.append(f"高无常损失风险({il_risk:.1f}%)")
        
        if volatility_risk > 0.6:
            factors.append("高价格波动风险")
        
        if liquidity_risk > 0.6:
            tvl = pool_data.get('tvl_usd', 0)
            if tvl < 100000:
                factors.append(f"TVL过低({tvl:,.0f})")
            
            volume_ratio = pool_data.get('volume_24h', 0) / max(tvl, 1)
            if volume_ratio < 0.001:
                factors.append("交易活跃度极低")
        
        if concentration_risk > 0.6:
            factors.append("代币集中度风险")
        
        if sc_risk > 0.6:
            factors.append("智能合约/协议风险")
        
        return factors
    
    def _generate_mitigation_suggestions(self, 
                                       risk_factors: List[str],
                                       pool_data: Dict[str, Any]) -> List[str]:
        """生成风险缓解建议"""
        suggestions = []
        
        if any("无常损失" in factor for factor in risk_factors):
            suggestions.append("考虑相关性较高的代币对以降低无常损失")
            suggestions.append("设置合理的止损点位")
        
        if any("波动" in factor for factor in risk_factors):
            suggestions.append("缩短投资周期，频繁监控")
            suggestions.append("考虑对冲策略")
        
        if any("TVL" in factor for factor in risk_factors):
            suggestions.append("等待池子规模增长后再投资")
            suggestions.append("降低投资金额")
        
        if any("活跃度" in factor for factor in risk_factors):
            suggestions.append("验证项目基本面是否健康")
            suggestions.append("监控交易量变化趋势")
        
        if any("集中度" in factor for factor in risk_factors):
            suggestions.append("进行充分的代币研究")
            suggestions.append("分散投资，避免过度集中")
        
        if any("合约" in factor for factor in risk_factors):
            suggestions.append("验证智能合约审计报告")
            suggestions.append("选择知名协议和主流链")
        
        return suggestions
    
    def _calculate_risk_confidence(self, pool_data: Dict[str, Any]) -> float:
        """计算风险评估置信度"""
        confidence = 1.0
        
        # 数据完整性影响置信度
        required_fields = ['tvl_usd', 'volume_24h', 'token0', 'token1']
        missing_fields = sum(1 for field in required_fields if not pool_data.get(field))
        confidence *= (1 - missing_fields * 0.15)
        
        # 数据质量影响置信度
        tvl = pool_data.get('tvl_usd', 0)
        if tvl < 1000:
            confidence *= 0.6  # 小池子数据可能不准确
        
        # 时间新鲜度影响置信度
        # 这里简化处理，实际应该检查数据时间戳
        
        return max(0.3, min(1.0, confidence))
    
    def _get_default_high_risk_metrics(self) -> RiskMetrics:
        """获取默认高风险指标(当计算失败时)"""
        return RiskMetrics(
            impermanent_loss_risk=30.0,
            volatility_risk=0.8,
            liquidity_risk=0.8,
            concentration_risk=0.7,
            smart_contract_risk=0.6,
            overall_risk_score=0.8,
            risk_level=RiskLevel.HIGH,
            risk_factors=["数据不足", "计算失败"],
            mitigation_suggestions=["谨慎投资", "充分研究"],
            confidence=0.3
        )
    
    def _init_token_risk_mapping(self) -> Dict[str, float]:
        """初始化代币风险映射"""
        return {
            # 稳定币 - 低风险
            'USDT': 0.1, 'USDC': 0.1, 'BUSD': 0.15, 'DAI': 0.2, 'FRAX': 0.25,
            
            # 主流币 - 中低风险
            'BTC': 0.2, 'ETH': 0.2, 'BNB': 0.25, 'SOL': 0.3,
            'BTCB': 0.2, 'WBNB': 0.25, 'WETH': 0.2, 'WBTC': 0.2,
            
            # 知名DeFi代币 - 中等风险
            'UNI': 0.4, 'SUSHI': 0.4, 'AAVE': 0.4, 'COMP': 0.4,
            'CRV': 0.4, 'BAL': 0.4, 'YFI': 0.5,
            
            # Layer2/侧链代币 - 中等风险
            'MATIC': 0.35, 'AVAX': 0.3, 'FTM': 0.4, 'ONE': 0.5,
            
            # 其他代币默认为中高风险
        }
    
    def _init_protocol_risk_mapping(self) -> Dict[str, float]:
        """初始化协议风险映射"""
        return {
            # 顶级协议 - 低风险
            'uniswap': 0.1, 'pancakeswap': 0.15, 'sushiswap': 0.2,
            'curve': 0.15, 'balancer': 0.2, 'compound': 0.2,
            
            # 知名协议 - 中低风险
            'aave': 0.25, '1inch': 0.3, 'yearn': 0.3,
            'convex': 0.35, 'frax': 0.3,
            
            # 新兴协议 - 中高风险
            'dodo': 0.5, 'alpaca': 0.6, 'venus': 0.4,
            
            # 未知协议默认高风险
        }
    
    def get_risk_thresholds(self) -> Dict[str, Dict[str, float]]:
        """获取风险阈值配置"""
        return {
            'impermanent_loss': {
                'low': 5.0,      # < 5%
                'medium': 15.0,  # 5-15%
                'high': 30.0,    # 15-30%
                'critical': 50.0 # > 30%
            },
            'volatility': {
                'low': self.params['volatility_threshold_low'],
                'high': self.params['volatility_threshold_high']
            },
            'tvl': {
                'min_safe': self.params['min_tvl_safe'],
                'min_acceptable': 100000,
                'min_viable': 10000
            },
            'volume_ratio': {
                'min_healthy': self.params['min_volume_ratio'],
                'min_acceptable': 0.001
            }
        }