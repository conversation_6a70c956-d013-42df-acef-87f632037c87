"""
DyFlow v3.4 DCA 退出策略
實現分批退出邏輯，包括退出時機判斷和滑點控制
基於 Agno Framework 的事件驅動架構
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

# Agno Framework imports
try:
    from agno import Agent
except ImportError:
    class Agent:
        pass

from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

class ExitTrigger(Enum):
    """退出觸發條件"""
    IL_BREACH = "il_breach"           # IL 熔斷
    VAR_BREACH = "var_breach"         # VaR 超限
    PROFIT_TAKING = "profit_taking"   # 獲利了結
    TIME_BASED = "time_based"         # 時間觸發
    MANUAL = "manual"                 # 手動觸發

@dataclass
class ExitBatch:
    """退出批次"""
    batch_id: str
    position_id: str
    percentage: float  # 退出百分比 (0-1)
    target_price: Optional[float]
    max_slippage: float  # 最大滑點
    execution_time: Optional[datetime]
    status: str  # "pending", "executing", "completed", "failed"

@dataclass
class DCAExitPlan:
    """DCA 退出計劃"""
    plan_id: str
    position_id: str
    trigger: ExitTrigger
    total_batches: int
    batch_interval: timedelta  # 批次間隔
    batches: List[ExitBatch]
    max_total_slippage: float
    created_at: datetime
    status: str

class DCAExitStrategy(Agent):
    """
    DCA 退出策略執行器 v3.4
    
    功能：
    1. 分批退出時機判斷
    2. 滑點控制和優化
    3. 動態調整退出計劃
    4. 風險管理整合
    """
    
    def __init__(self, config: Dict[str, Any]):
        # super().__init__()  # 暫時註釋避免初始化問題
        self.config = config
        self.name = "DCAExitStrategy"
        
        # 退出配置
        self.default_batches = config.get('default_batches', 5)
        self.default_interval = timedelta(minutes=config.get('default_interval_minutes', 30))
        self.max_slippage = config.get('max_slippage', 0.02)  # 2%
        
        # 活躍退出計劃
        self.active_plans: Dict[str, DCAExitPlan] = {}
        
        # Agno 上下文
        self.ctx = None
        
        logger.info("dca_exit_strategy_v34_initialized",
                   default_batches=self.default_batches,
                   max_slippage=self.max_slippage)
    
    def set_context(self, ctx):
        """設置 Agno 上下文"""
        self.ctx = ctx
    
    async def create_exit_plan(self, position_id: str, trigger: ExitTrigger,
                             custom_config: Optional[Dict[str, Any]] = None) -> DCAExitPlan:
        """
        創建 DCA 退出計劃
        
        Args:
            position_id: 倉位ID
            trigger: 退出觸發條件
            custom_config: 自定義配置
            
        Returns:
            DCAExitPlan: 退出計劃
        """
        try:
            plan_id = f"dca_exit_{position_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 合併配置
            config = {
                'batches': self.default_batches,
                'interval': self.default_interval,
                'max_slippage': self.max_slippage
            }
            if custom_config:
                config.update(custom_config)
            
            # 根據觸發條件調整策略
            config = self._adjust_config_by_trigger(trigger, config)
            
            # 創建批次
            batches = self._create_exit_batches(
                plan_id, position_id, config['batches'], config['max_slippage']
            )
            
            # 創建退出計劃
            plan = DCAExitPlan(
                plan_id=plan_id,
                position_id=position_id,
                trigger=trigger,
                total_batches=config['batches'],
                batch_interval=config['interval'],
                batches=batches,
                max_total_slippage=config['max_slippage'],
                created_at=datetime.now(),
                status="created"
            )
            
            self.active_plans[plan_id] = plan
            
            logger.info("dca_exit_plan_created",
                       plan_id=plan_id,
                       position_id=position_id,
                       trigger=trigger.value,
                       batches=len(batches))
            
            return plan
            
        except Exception as e:
            logger.error("dca_exit_plan_creation_failed", 
                        position_id=position_id, error=str(e))
            raise DyFlowException(f"Failed to create DCA exit plan: {e}")
    
    def _adjust_config_by_trigger(self, trigger: ExitTrigger, 
                                config: Dict[str, Any]) -> Dict[str, Any]:
        """根據觸發條件調整配置"""
        
        if trigger == ExitTrigger.IL_BREACH:
            # IL 熔斷：快速退出，減少批次
            config['batches'] = 3
            config['interval'] = timedelta(minutes=10)
            config['max_slippage'] = 0.03  # 允許更高滑點
            
        elif trigger == ExitTrigger.VAR_BREACH:
            # VaR 超限：中等速度退出
            config['batches'] = 4
            config['interval'] = timedelta(minutes=20)
            config['max_slippage'] = 0.025
            
        elif trigger == ExitTrigger.PROFIT_TAKING:
            # 獲利了結：慢速退出，最小化滑點
            config['batches'] = 8
            config['interval'] = timedelta(hours=1)
            config['max_slippage'] = 0.01
            
        elif trigger == ExitTrigger.TIME_BASED:
            # 時間觸發：標準退出
            config['batches'] = 5
            config['interval'] = timedelta(minutes=30)
            config['max_slippage'] = 0.02
            
        return config
    
    def _create_exit_batches(self, plan_id: str, position_id: str, 
                           num_batches: int, max_slippage: float) -> List[ExitBatch]:
        """創建退出批次"""
        batches = []
        
        # 計算每批次的退出百分比
        # 使用遞減策略：前面批次較大，後面批次較小
        percentages = self._calculate_batch_percentages(num_batches)
        
        for i, percentage in enumerate(percentages):
            batch_id = f"{plan_id}_batch_{i+1}"
            
            # 計算每批次的最大滑點
            batch_slippage = max_slippage / num_batches
            
            batch = ExitBatch(
                batch_id=batch_id,
                position_id=position_id,
                percentage=percentage,
                target_price=None,  # 執行時動態設定
                max_slippage=batch_slippage,
                execution_time=None,
                status="pending"
            )
            
            batches.append(batch)
        
        return batches
    
    def _calculate_batch_percentages(self, num_batches: int) -> List[float]:
        """計算批次百分比 - 遞減策略"""
        if num_batches == 1:
            return [1.0]
        
        # 使用幾何級數：第一批最大，逐漸遞減
        weights = []
        for i in range(num_batches):
            weight = 1.0 / (2 ** i)  # 1, 0.5, 0.25, 0.125, ...
            weights.append(weight)
        
        # 正規化到總和為 1
        total_weight = sum(weights)
        percentages = [w / total_weight for w in weights]
        
        return percentages
    
    async def execute_exit_plan(self, plan_id: str) -> bool:
        """
        執行 DCA 退出計劃
        
        Args:
            plan_id: 計劃ID
            
        Returns:
            bool: 執行是否成功
        """
        try:
            plan = self.active_plans.get(plan_id)
            if not plan:
                raise Exception(f"Exit plan not found: {plan_id}")
            
            plan.status = "executing"
            
            logger.info("dca_exit_plan_execution_started",
                       plan_id=plan_id,
                       position_id=plan.position_id,
                       total_batches=len(plan.batches))
            
            # 執行所有批次
            for i, batch in enumerate(plan.batches):
                try:
                    # 執行批次
                    success = await self._execute_batch(plan, batch)
                    
                    if not success:
                        logger.error("batch_execution_failed",
                                   plan_id=plan_id,
                                   batch_id=batch.batch_id)
                        plan.status = "failed"
                        return False
                    
                    # 等待下一批次（除了最後一批）
                    if i < len(plan.batches) - 1:
                        await asyncio.sleep(plan.batch_interval.total_seconds())
                        
                except Exception as e:
                    logger.error("batch_execution_error",
                               plan_id=plan_id,
                               batch_id=batch.batch_id,
                               error=str(e))
                    batch.status = "failed"
                    plan.status = "failed"
                    return False
            
            plan.status = "completed"
            
            logger.info("dca_exit_plan_completed",
                       plan_id=plan_id,
                       position_id=plan.position_id)
            
            # 發佈完成事件
            if self.ctx:
                await self.ctx.publish("dca.exit_completed", {
                    "plan_id": plan_id,
                    "position_id": plan.position_id,
                    "trigger": plan.trigger.value,
                    "batches_executed": len(plan.batches),
                    "timestamp": datetime.now().isoformat()
                })
            
            return True
            
        except Exception as e:
            logger.error("dca_exit_plan_execution_failed",
                        plan_id=plan_id, error=str(e))
            return False
    
    async def _execute_batch(self, plan: DCAExitPlan, batch: ExitBatch) -> bool:
        """執行單個退出批次"""
        try:
            batch.status = "executing"
            batch.execution_time = datetime.now()
            
            logger.info("executing_exit_batch",
                       plan_id=plan.plan_id,
                       batch_id=batch.batch_id,
                       percentage=batch.percentage)
            
            # 獲取當前市場價格
            current_price = await self._get_current_price(plan.position_id)
            
            # 計算目標價格（考慮滑點）
            batch.target_price = current_price * (1 - batch.max_slippage)
            
            # 執行退出交易
            success = await self._execute_exit_transaction(plan, batch)
            
            if success:
                batch.status = "completed"
                logger.info("exit_batch_completed",
                           batch_id=batch.batch_id,
                           percentage=batch.percentage)
            else:
                batch.status = "failed"
                logger.error("exit_batch_failed", batch_id=batch.batch_id)
            
            return success
            
        except Exception as e:
            logger.error("exit_batch_execution_error",
                        batch_id=batch.batch_id, error=str(e))
            batch.status = "failed"
            return False
    
    async def _get_current_price(self, position_id: str) -> float:
        """獲取當前市場價格"""
        # 這裡應該調用實際的價格獲取邏輯
        # 暫時返回模擬價格
        return 100.0
    
    async def _execute_exit_transaction(self, plan: DCAExitPlan, 
                                      batch: ExitBatch) -> bool:
        """執行退出交易"""
        try:
            # 這裡應該調用實際的交易執行邏輯
            # 根據鏈類型選擇不同的執行方法
            
            # 模擬交易執行
            await asyncio.sleep(2)  # 模擬交易時間
            
            # 發佈交易事件
            if self.ctx:
                await self.ctx.publish("dca.batch_executed", {
                    "plan_id": plan.plan_id,
                    "batch_id": batch.batch_id,
                    "position_id": batch.position_id,
                    "percentage": batch.percentage,
                    "target_price": batch.target_price,
                    "timestamp": datetime.now().isoformat()
                })
            
            return True
            
        except Exception as e:
            logger.error("exit_transaction_failed",
                        batch_id=batch.batch_id, error=str(e))
            return False
    
    def get_plan_status(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """獲取計劃狀態"""
        plan = self.active_plans.get(plan_id)
        if not plan:
            return None
        
        completed_batches = sum(1 for b in plan.batches if b.status == "completed")
        failed_batches = sum(1 for b in plan.batches if b.status == "failed")
        
        return {
            "plan_id": plan_id,
            "position_id": plan.position_id,
            "status": plan.status,
            "trigger": plan.trigger.value,
            "total_batches": plan.total_batches,
            "completed_batches": completed_batches,
            "failed_batches": failed_batches,
            "progress": completed_batches / plan.total_batches,
            "created_at": plan.created_at.isoformat()
        }
    
    def cancel_plan(self, plan_id: str) -> bool:
        """取消退出計劃"""
        plan = self.active_plans.get(plan_id)
        if not plan:
            return False
        
        if plan.status in ["executing", "completed"]:
            logger.warning("cannot_cancel_plan", 
                          plan_id=plan_id, status=plan.status)
            return False
        
        plan.status = "cancelled"
        logger.info("dca_exit_plan_cancelled", plan_id=plan_id)
        return True
