"""
Enhanced MarketIntelAgent - DyFlow v3.4 市場情報收集
兩層混合輪詢 + WS 架構：HTTP 快照 + Price WebSocket
實現差分流程 intel_agent.py 規格
"""

import asyncio
import structlog
import hashlib
import json
import aiohttp
import websockets
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = structlog.get_logger(__name__)

class RollingPriceTracker:
    """滾動價格追蹤器 - 計算實時波動率"""
    
    def __init__(self, window_size: int = 60):
        self.window_size = window_size
        self.prices = defaultdict(lambda: deque(maxlen=window_size))
        self.last_volatility = defaultdict(float)
    
    def update(self, pool_id: str, price: float):
        """更新價格數據"""
        self.prices[pool_id].append({
            'price': price,
            'timestamp': datetime.now().timestamp()
        })
        
        # 計算波動率
        if len(self.prices[pool_id]) >= 10:
            prices = [p['price'] for p in self.prices[pool_id]]
            returns = [(prices[i] / prices[i-1] - 1) for i in range(1, len(prices))]
            volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
            self.last_volatility[pool_id] = volatility
    
    def changed(self, pool_id: str) -> bool:
        """檢查波動率是否有顯著變化"""
        return len(self.prices[pool_id]) >= 10
    
    def snapshot(self, pool_id: str) -> Dict[str, Any]:
        """獲取池子快照"""
        if pool_id not in self.prices or not self.prices[pool_id]:
            return {}
        
        latest = self.prices[pool_id][-1]
        return {
            'pool_id': pool_id,
            'price': latest['price'],
            'volatility': self.last_volatility.get(pool_id, 0),
            'timestamp': latest['timestamp']
        }

class EnhancedMarketIntelAgent:
    """
    Enhanced MarketIntelAgent - 市場情報收集 Agent v3.4
    
    兩層混合輪詢 + WS 架構：
    1. HTTP 快照：池清單、TVL、fee24h (BSC 15s, SOL 12s)
    2. Price WebSocket：即時成交價 → σ₁ₘ/ATR (<1s)
    3. 差分流程：md5 pool_json 檢測變化
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = "EnhancedMarketIntelAgent"
        
        # 掃描間隔配置
        self.bsc_interval = config.get('bsc_scan_interval', 15)  # BSC 15秒
        self.sol_interval = config.get('sol_scan_interval', 12)  # SOL 12秒
        
        self.is_running = False
        
        # API 端點
        self.endpoints = {
            "pancake_subgraph": "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ",
            "meteora_dlmm_api": "https://dammv2-api.meteora.ag",
            "jupiter_ws": "wss://quote-api.jup.ag/v6/ws"
        }
        
        # API 密鑰
        self.api_keys = {
            "thegraph": "9731921233db132a98c2325878e6c153"
        }
        
        # 狀態追蹤
        self.pool_cache = {}  # 池子緩存
        self.pool_hashes = {}  # 池子 MD5 哈希
        self.last_cursors = {'bsc': None, 'sol': None}  # 掃描游標
        
        # 價格追蹤器
        self.price_tracker = RollingPriceTracker()
        
        # WebSocket 連接
        self.price_ws = None
        
        # 事件發佈上下文 (Agno)
        self.ctx = None
        
        logger.info("enhanced_market_intel_agent_v34_initialized", 
                   bsc_interval=self.bsc_interval,
                   sol_interval=self.sol_interval)
    
    def set_context(self, ctx):
        """設置 Agno 上下文"""
        self.ctx = ctx
    
    async def start_scanning(self):
        """開始掃描 - 啟動兩個並發任務"""
        self.is_running = True
        logger.info("enhanced_market_intel_scanning_started")
        
        # 並發啟動 HTTP 快照掃描和 Price WebSocket
        tasks = [
            self._http_snapshot_loop(),
            self._price_websocket_loop()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop_scanning(self):
        """停止掃描"""
        self.is_running = False
        if self.price_ws:
            await self.price_ws.close()
        logger.info("enhanced_market_intel_scanning_stopped")
    
    async def _http_snapshot_loop(self):
        """HTTP 快照輪詢主循環"""
        while self.is_running:
            try:
                # 並發獲取 BSC 和 SOL 數據
                tasks = [
                    self._fetch_bsc_snapshot(),
                    self._fetch_sol_snapshot()
                ]
                
                new_bsc, new_sol = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 處理異常
                if isinstance(new_bsc, Exception):
                    logger.error("bsc_snapshot_failed", error=str(new_bsc))
                    new_bsc = []
                
                if isinstance(new_sol, Exception):
                    logger.error("sol_snapshot_failed", error=str(new_sol))
                    new_sol = []
                
                # 差分檢測和事件發佈
                events = await self._diff_and_build(new_bsc + new_sol)
                
                # 發佈事件到 bus.pool
                for event in events:
                    if self.ctx:
                        await self.ctx.publish("bus.pool", event)
                
                logger.info("http_snapshot_completed",
                           bsc_pools=len(new_bsc) if isinstance(new_bsc, list) else 0,
                           sol_pools=len(new_sol) if isinstance(new_sol, list) else 0,
                           events_published=len(events))
                
                # 等待下次掃描
                await asyncio.sleep(min(self.bsc_interval, self.sol_interval))
                
            except Exception as e:
                logger.error("http_snapshot_loop_error", error=str(e))
                await asyncio.sleep(5)
    
    async def _fetch_bsc_snapshot(self) -> List[Dict[str, Any]]:
        """獲取 BSC 池子快照 - GraphQL HTTP POST"""
        try:
            # GraphQL 查詢 - 獲取池清單、TVL、fee24h、tick index
            query = """
            query GetPools($first: Int!, $cursor: String) {
              pools(
                first: $first
                where: {
                  totalValueLockedUSD_gte: "10000000"
                  createdAtTimestamp_gte: "%s"
                  id_gt: $cursor
                }
                orderBy: id
              ) {
                id
                token0 { id symbol name }
                token1 { id symbol name }
                feeTier
                totalValueLockedUSD
                tick
                poolDayData(first: 1, orderBy: date, orderDirection: desc) {
                  volumeUSD
                  feesUSD
                  tvlUSD
                }
              }
            }
            """ % int((datetime.now() - timedelta(days=2)).timestamp())
            
            variables = {
                "first": 100,
                "cursor": self.last_cursors.get('bsc', "")
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoints["pancake_subgraph"],
                    json={"query": query, "variables": variables},
                    headers={"Authorization": f"Bearer {self.api_keys['thegraph']}"},
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data.get("data", {}).get("pools", [])
                        
                        # 更新游標
                        if pools:
                            self.last_cursors['bsc'] = pools[-1]['id']
                        
                        # 處理池子數據
                        processed_pools = []
                        for pool in pools:
                            processed_pool = self._process_bsc_pool(pool)
                            if processed_pool:
                                processed_pools.append(processed_pool)
                        
                        return processed_pools
                    else:
                        logger.error("bsc_subgraph_error", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("bsc_snapshot_fetch_failed", error=str(e))
            return []
    
    def _process_bsc_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 BSC 池子數據"""
        try:
            pool_day_data = pool.get("poolDayData", [])
            day_data = pool_day_data[0] if pool_day_data else {}
            
            tvl = float(pool.get("totalValueLockedUSD", 0))
            fees_24h = float(day_data.get("feesUSD", 0))
            
            # 計算 fee_tvl_pct
            fee_tvl_pct = (fees_24h / tvl * 100) if tvl > 0 else 0
            
            return {
                "pool_id": pool["id"],
                "chain": "bsc",
                "tvl": tvl,
                "fee_tvl_pct": fee_tvl_pct,
                "tick": int(pool.get("tick", 0)),
                "ts": datetime.now().timestamp(),
                "token0": pool.get("token0", {}),
                "token1": pool.get("token1", {}),
                "fee_tier": int(pool.get("feeTier", 3000)),
                "raw_data": pool
            }
            
        except Exception as e:
            logger.error("bsc_pool_processing_failed", pool_id=pool.get("id"), error=str(e))
            return None

    async def _fetch_sol_snapshot(self) -> List[Dict[str, Any]]:
        """獲取 Solana 池子快照 - REST GET /api/v1/pools"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.endpoints['meteora_dlmm_api']}/pair/all",
                    timeout=30
                ) as response:
                    if response.status == 200:
                        pools = await response.json()

                        # 處理池子數據
                        processed_pools = []
                        for pool in pools:
                            processed_pool = self._process_sol_pool(pool)
                            if processed_pool:
                                processed_pools.append(processed_pool)

                        return processed_pools
                    else:
                        logger.error("sol_dlmm_api_error", status=response.status)
                        return []

        except Exception as e:
            logger.error("sol_snapshot_fetch_failed", error=str(e))
            return []

    def _process_sol_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 Solana 池子數據"""
        try:
            tvl = float(pool.get("liquidity", 0))
            fees_24h = float(pool.get("fees_24h", 0))

            # 計算 fee_tvl_pct
            fee_tvl_pct = (fees_24h / tvl * 100) if tvl > 0 else 0

            return {
                "pool_id": pool["address"],
                "chain": "solana",
                "tvl": tvl,
                "fee_tvl_pct": fee_tvl_pct,
                "bin_step": int(pool.get("bin_step", 0)),
                "ts": datetime.now().timestamp(),
                "token_x": pool.get("mint_x", {}),
                "token_y": pool.get("mint_y", {}),
                "raw_data": pool
            }

        except Exception as e:
            logger.error("sol_pool_processing_failed", pool_id=pool.get("address"), error=str(e))
            return None

    async def _diff_and_build(self, new_pools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """差分檢測和事件構建 - md5 pool_json"""
        events = []

        for pool in new_pools:
            try:
                pool_id = pool["pool_id"]

                # 計算池子數據的 MD5 哈希
                pool_json = json.dumps(pool, sort_keys=True)
                pool_hash = hashlib.md5(pool_json.encode()).hexdigest()

                # 檢查是否有變化
                if pool_id not in self.pool_hashes or self.pool_hashes[pool_id] != pool_hash:
                    # 池子有變化，創建事件
                    event = {
                        "type": "PoolEvent",
                        "pool_id": pool_id,
                        "chain": pool["chain"],
                        "tvl": pool["tvl"],
                        "fee_tvl_pct": pool["fee_tvl_pct"],
                        "sigma": 0.02,  # 默認波動率，將由 Price WS 更新
                        "spread": 0.0003,  # 默認價差
                        "timestamp": pool["ts"],
                        "data": pool
                    }
                    events.append(event)

                    # 更新哈希緩存
                    self.pool_hashes[pool_id] = pool_hash
                    self.pool_cache[pool_id] = pool

            except Exception as e:
                logger.error("pool_diff_failed", pool_id=pool.get("pool_id"), error=str(e))

        return events

    async def _price_websocket_loop(self):
        """Price WebSocket 循環 - 即時成交價 → σ₁ₘ／ATR"""
        while self.is_running:
            try:
                # 連接 Jupiter Quote WebSocket (模擬)
                # 實際應該連接到真實的價格 WebSocket
                await self._simulate_price_updates()

            except Exception as e:
                logger.error("price_websocket_error", error=str(e))
                await asyncio.sleep(5)

    async def _simulate_price_updates(self):
        """模擬價格更新 - 實際應該連接真實 WebSocket"""
        # 為緩存中的池子模擬價格更新
        for pool_id in list(self.pool_cache.keys()):
            try:
                # 模擬價格變化
                import random
                base_price = 100.0
                price_change = random.uniform(-0.05, 0.05)  # ±5% 變化
                new_price = base_price * (1 + price_change)

                # 更新價格追蹤器
                self.price_tracker.update(pool_id, new_price)

                # 如果波動率有顯著變化，發佈更新事件
                if self.price_tracker.changed(pool_id):
                    snapshot = self.price_tracker.snapshot(pool_id)
                    if snapshot and self.ctx:
                        await self.ctx.publish("bus.pool", snapshot)

            except Exception as e:
                logger.error("price_update_failed", pool_id=pool_id, error=str(e))

        # 等待 1 秒 (< 1s 差價更新)
        await asyncio.sleep(1)

    async def handle_price_tick(self, msg: Dict[str, Any]):
        """處理價格 tick 事件 - WebSocket 回調"""
        try:
            pool_id = msg.get("pool_id")
            price = msg.get("price")

            if pool_id and price:
                self.price_tracker.update(pool_id, price)

                if self.price_tracker.changed(pool_id):
                    snapshot = self.price_tracker.snapshot(pool_id)
                    if snapshot and self.ctx:
                        await self.ctx.publish("bus.pool", snapshot)

        except Exception as e:
            logger.error("price_tick_handling_failed", error=str(e))

    def get_metrics(self) -> Dict[str, Any]:
        """獲取指標用於監控"""
        return {
            'enhanced_market_intel_pools_cached': len(self.pool_cache),
            'enhanced_market_intel_price_tracked': len(self.price_tracker.prices),
            'enhanced_market_intel_bsc_cursor': self.last_cursors.get('bsc', ''),
            'enhanced_market_intel_sol_cursor': self.last_cursors.get('sol', ''),
            'enhanced_market_intel_running': self.is_running
        }
