"""
CoreAgent - DyFlow v3.4 核心代理
PRD Section 3 & 10 - 唯一對外 API/WS Gateway

CoreAgent is the only surface:
REST /v1/* + single WS /ws/{workflow}; everything else stays internal.
"""

import asyncio
import structlog
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import FastAPI, WebSocket, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Agno Framework imports
try:
    from agno import Agent, Context, Workflow
except ImportError:
    # 如果 Agno 不可用，使用模擬類
    class Agent:
        pass
    class Context:
        pass
    class Workflow:
        pass

logger = structlog.get_logger(__name__)

class CoreAgent(Agent):
    """
    CoreAgent - 唯一對外接口
    
    職責：
    1. HTTP API Gateway (/v1/*)
    2. WebSocket Gateway (/ws/{workflow})
    3. 內部事件總線協調
    4. 用戶交互和狀態管理
    """
    
    def __init__(self, config: Dict[str, Any]):
        # super().__init__()  # 暫時註釋，避免初始化問題
        self.config = config
        self.name = "CoreAgent"
        
        # FastAPI 應用
        self.app = FastAPI(
            title="DyFlow v3.4 Core API",
            description="24/7 自動化 LP 策略系統",
            version="3.4.0"
        )
        
        # CORS 設置
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 系統狀態
        self.current_phase = 0
        self.trading_mode = "paused"  # active/paused/exit_only
        self.active_agents = {}
        self.system_health = {}
        self.nav_data = {}
        
        # WebSocket 連接管理
        self.websocket_connections = set()
        
        # 內部事件總線
        self.event_bus = None
        
        # 設置路由
        self._setup_routes()
        
        logger.info("core_agent_initialized", 
                   trading_mode=self.trading_mode,
                   current_phase=self.current_phase)
    
    def _setup_routes(self):
        """設置 API 路由"""
        
        @self.app.get("/")
        async def root():
            return {
                "service": "DyFlow v3.4 Core API",
                "status": "running",
                "current_phase": self.current_phase,
                "trading_mode": self.trading_mode,
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/health")
        async def health_check():
            """健康檢查端點"""
            return {
                "status": "healthy",
                "phase": self.current_phase,
                "agents": len(self.active_agents),
                "health_score": self._calculate_health_score(),
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/core/launch/confirm")
        async def launch_confirm(request: Dict[str, Any]):
            """確認啟動提案 - PRD Section 10"""
            proposal_id = request.get("proposal_id")
            if not proposal_id:
                raise HTTPException(status_code=400, detail="Missing proposal_id")
            
            # 發佈 LaunchConfirmed 事件
            event = {
                "type": "LaunchConfirmed",
                "proposal_id": proposal_id,
                "user_id": request.get("user_id", "default"),
                "approved_plans": request.get("approved_plans", []),
                "ts": datetime.now().timestamp()
            }
            
            await self._publish_event("launch.confirmed", event)
            
            return {
                "status": "confirmed",
                "proposal_id": proposal_id,
                "next_phase": self.current_phase + 1
            }
        
        @self.app.get("/core/kpi")
        async def get_kpi():
            """獲取 KPI 數據 - PRD Section 10"""
            return {
                "nav": self.nav_data,
                "fee24h": self._get_fee_24h(),
                "risk": self._get_risk_metrics(),
                "positions": self._get_position_count(),
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/core/trading_mode")
        async def set_trading_mode(request: Dict[str, Any]):
            """設置交易模式 - PRD Section 10"""
            mode = request.get("mode")
            if mode not in ["active", "paused", "exit_only"]:
                raise HTTPException(status_code=400, detail="Invalid trading mode")
            
            old_mode = self.trading_mode
            self.trading_mode = mode
            
            # 發佈模式變更事件
            await self._publish_event("trading.mode_changed", {
                "old_mode": old_mode,
                "new_mode": mode,
                "ts": datetime.now().timestamp()
            })
            
            logger.info("trading_mode_changed", old_mode=old_mode, new_mode=mode)
            
            return {
                "status": "updated",
                "mode": mode,
                "previous_mode": old_mode
            }
        
        @self.app.websocket("/ws/{workflow}")
        async def websocket_endpoint(websocket: WebSocket, workflow: str):
            """WebSocket 端點 - PRD Section 10"""
            await self._handle_websocket(websocket, workflow)
    
    async def _handle_websocket(self, websocket: WebSocket, workflow: str):
        """處理 WebSocket 連接"""
        await websocket.accept()
        self.websocket_connections.add(websocket)
        
        logger.info("websocket_connected", workflow=workflow, 
                   total_connections=len(self.websocket_connections))
        
        try:
            # 發送初始狀態
            await websocket.send_json({
                "type": "connection_established",
                "workflow": workflow,
                "current_phase": self.current_phase,
                "trading_mode": self.trading_mode,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保持連接並處理消息
            while True:
                try:
                    data = await websocket.receive_json()
                    await self._handle_websocket_message(websocket, data)
                except Exception as e:
                    logger.error("websocket_message_error", error=str(e))
                    break
                    
        except Exception as e:
            logger.error("websocket_error", error=str(e))
        finally:
            self.websocket_connections.discard(websocket)
            logger.info("websocket_disconnected", workflow=workflow,
                       remaining_connections=len(self.websocket_connections))
    
    async def _handle_websocket_message(self, websocket: WebSocket, data: Dict[str, Any]):
        """處理 WebSocket 消息"""
        message_type = data.get("type")
        
        if message_type == "ping":
            await websocket.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})
        elif message_type == "get_status":
            await websocket.send_json({
                "type": "status_update",
                "phase": self.current_phase,
                "trading_mode": self.trading_mode,
                "agents": self.active_agents,
                "health": self.system_health
            })
        else:
            logger.warning("unknown_websocket_message", type=message_type)
    
    async def _publish_event(self, topic: str, event: Dict[str, Any]):
        """發佈事件到內部事件總線"""
        if self.event_bus:
            await self.event_bus.publish(topic, event)
        
        # 同時廣播到所有 WebSocket 連接
        await self._broadcast_to_websockets({
            "type": "event",
            "topic": topic,
            "data": event
        })
    
    async def _broadcast_to_websockets(self, message: Dict[str, Any]):
        """廣播消息到所有 WebSocket 連接"""
        if not self.websocket_connections:
            return
        
        disconnected = set()
        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(message)
            except Exception:
                disconnected.add(websocket)
        
        # 清理斷開的連接
        self.websocket_connections -= disconnected
    
    def _calculate_health_score(self) -> float:
        """計算系統健康分數"""
        if not self.system_health:
            return 0.0
        
        scores = [h.get("score", 0.0) for h in self.system_health.values()]
        return sum(scores) / len(scores) if scores else 0.0
    
    def _get_fee_24h(self) -> Dict[str, Any]:
        """獲取 24 小時費用數據"""
        return {
            "total_usd": 0.0,
            "by_chain": {"bsc": 0.0, "solana": 0.0},
            "apr": 0.0
        }
    
    def _get_risk_metrics(self) -> Dict[str, Any]:
        """獲取風險指標"""
        return {
            "il_net": 0.0,
            "var_95": 0.0,
            "positions_at_risk": 0,
            "risk_level": "low"
        }
    
    def _get_position_count(self) -> int:
        """獲取倉位數量"""
        return 0
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8001):
        """啟動 HTTP 服務器"""
        logger.info("starting_core_agent_server", host=host, port=port)
        
        config = uvicorn.Config(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    
    def set_event_bus(self, event_bus):
        """設置事件總線"""
        self.event_bus = event_bus
    
    def update_agent_status(self, agent_name: str, status: Dict[str, Any]):
        """更新 Agent 狀態"""
        self.active_agents[agent_name] = {
            **status,
            "last_update": datetime.now().isoformat()
        }
    
    def update_system_health(self, component: str, health_data: Dict[str, Any]):
        """更新系統健康狀態"""
        self.system_health[component] = {
            **health_data,
            "last_update": datetime.now().isoformat()
        }
    
    def advance_phase(self, new_phase: int):
        """推進到下一階段"""
        old_phase = self.current_phase
        self.current_phase = new_phase
        
        logger.info("phase_advanced", old_phase=old_phase, new_phase=new_phase)
        
        # 廣播階段變更
        asyncio.create_task(self._broadcast_to_websockets({
            "type": "phase_changed",
            "old_phase": old_phase,
            "new_phase": new_phase,
            "timestamp": datetime.now().isoformat()
        }))

# 全局 CoreAgent 實例
core_agent = None

def get_core_agent() -> CoreAgent:
    """獲取全局 CoreAgent 實例"""
    global core_agent
    if core_agent is None:
        core_agent = CoreAgent({})
    return core_agent
