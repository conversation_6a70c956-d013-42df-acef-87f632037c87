"""
Hedge Agent - DyFlow v3 + Agno Framework
基於 Agno Framework 的對沖和 DCA 出貨 Agent
負責執行 DCA 出貨策略和風險對沖操作
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.tools.reasoning import ReasoningTools
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass
    class BaseModel:
        pass

from .base_agent import BaseAgent
from ..utils.models_v3 import AgentResult, RiskLevel
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException
from ..tools.jupiter_swap_tool import JupiterSwapTool, DCAParams
from ..tools.oneinch_swap_tool import OneInchSwapTool
from ..tools.binance_hedge_tool import BinanceHedgeTool

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class HedgeAnalysis(BaseModel):
        """對沖分析 - Agno 結構化輸出"""
        position_id: str = Field(..., description="持倉ID")
        current_pnl: float = Field(..., description="當前盈虧")
        unrealized_gain_pct: float = Field(..., description="未實現收益百分比")
        
        # 對沖建議
        hedge_recommendation: str = Field(..., description="對沖建議", pattern="^(no_hedge|partial_hedge|full_hedge|dca_exit)$")
        hedge_ratio: float = Field(..., ge=0.0, le=1.0, description="建議對沖比例")
        hedge_instrument: str = Field(..., description="對沖工具", pattern="^(perpetual|spot_sell|options|none)$")
        
        # DCA 出貨參數
        dca_trigger_price: Optional[float] = Field(None, description="DCA 觸發價格")
        dca_exit_percentage: float = Field(..., ge=0.0, le=1.0, description="DCA 出貨比例")
        dca_orders_count: int = Field(..., ge=1, le=20, description="DCA 訂單數量")
        dca_interval_minutes: int = Field(..., ge=5, le=1440, description="DCA 間隔分鐘")
        
        # 風險評估
        market_risk_level: str = Field(..., description="市場風險級別", pattern="^(low|medium|high|extreme)$")
        execution_urgency: str = Field(..., description="執行緊急度", pattern="^(low|medium|high|immediate)$")
        
        reasoning: str = Field(..., description="分析推理過程")

    class DCAExitPlan(BaseModel):
        """DCA 出貨計劃 - Agno 結構化輸出"""
        position_id: str = Field(..., description="持倉ID")
        total_exit_amount: float = Field(..., gt=0, description="總出貨數量")
        
        # 執行策略
        exit_strategy: str = Field(..., description="出貨策略", pattern="^(gradual_dca|momentum_based|volatility_based|time_based)$")
        order_size_distribution: str = Field(..., description="訂單大小分佈", pattern="^(equal|decreasing|increasing|adaptive)$")
        
        # 時機控制
        start_immediately: bool = Field(..., description="是否立即開始")
        price_conditions: List[str] = Field(default_factory=list, description="價格條件")
        market_conditions: List[str] = Field(default_factory=list, description="市場條件")
        
        # 風險管理
        stop_loss_price: Optional[float] = Field(None, description="止損價格")
        take_profit_price: Optional[float] = Field(None, description="止盈價格")
        max_slippage_bps: int = Field(..., ge=10, le=500, description="最大滑點 (基點)")
        
        reasoning: str = Field(..., description="計劃推理")

else:
    # 降級數據類
    @dataclass
    class HedgeAnalysis:
        position_id: str
        hedge_recommendation: str
        reasoning: str
        hedge_required: bool = False
        current_exposure: float = 0.0
        target_hedge_ratio: float = 0.0

    @dataclass
    class DCAExitPlan:
        execute_dca_exit: bool
        urgency: str
        reasoning: str

# ========== Hedge Agent ==========

class HedgeAgent(BaseAgent):
    """對沖和 DCA 出貨 Agent"""
    
    def __init__(self, name: str = None, config = None):
        super().__init__(name, config)

        # 數據庫連接（可選）
        self.database = None  # 暫時不使用數據庫
        
        # Agno 相關配置
        self.agno_available = AGNO_AVAILABLE
        agent_specific_config = config or {}
        agno_config = agent_specific_config.get('agno', {})
        
        self.primary_model = agno_config.get('primary_model', 'gpt-4o')
        self.use_reasoning = agno_config.get('use_reasoning', True)
        self.debug_mode = agno_config.get('debug_mode', False)
        
        # Agno Agent 實例
        self.agno_hedge_analyzer: Optional[Agent] = None
        self.agno_dca_strategist: Optional[Agent] = None
        
        # 工具實例
        self.jupiter_tool: Optional[JupiterSwapTool] = None
        self.oneinch_tool: Optional[OneInchSwapTool] = None
        self.binance_tool: Optional[BinanceHedgeTool] = None
        
        # 對沖配置
        self.hedge_config = {
            'dca_trigger_gain_pct': 20.0,  # 20% 收益觸發 DCA
            'dca_step_size_pct': 10.0,     # 每次 DCA 10%
            'max_dca_orders': 10,          # 最多 10 次 DCA
            'min_dca_interval_minutes': 30, # 最小間隔 30 分鐘
            'hedge_threshold_pct': 50.0,   # 50% 收益考慮對沖
            'stop_loss_pct': -8.0,         # -8% 止損
            'max_slippage_bps': 100        # 最大滑點 1%
        }
        
        # DCA 狀態追蹤
        self.active_dca_plans: Dict[str, Dict[str, Any]] = {}
        self.dca_execution_history: Dict[str, List[Dict[str, Any]]] = {}
        
        logger.info("hedge_agent_initialized", 
                   agno_available=self.agno_available,
                   config=self.hedge_config)
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        
        if not self.agno_available:
            logger.info("hedge_agent_fallback_initialized")
            self.is_initialized = True
            return
        
        try:
            # 初始化工具
            tool_config = self.config.get('tools', {})
            
            jupiter_config = tool_config.get('jupiter_swap', {})
            self.jupiter_tool = JupiterSwapTool(jupiter_config)
            await self.jupiter_tool.initialize()
            
            oneinch_config = tool_config.get('oneinch_swap', {})
            self.oneinch_tool = OneInchSwapTool(oneinch_config)
            await self.oneinch_tool.initialize()
            
            binance_config = tool_config.get('binance_hedge', {})
            self.binance_tool = BinanceHedgeTool(binance_config)
            await self.binance_tool.initialize()
            
            # 初始化對沖分析 Agent
            self.agno_hedge_analyzer = Agent(
                name="HedgeAnalyzer",
                role="Analyze positions for hedging and DCA exit opportunities",
                agent_id="hedge-analyzer",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=self.use_reasoning,
                response_model=HedgeAnalysis,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are an expert hedge fund risk manager specializing in DeFi positions.",
                    "Analyze LP positions for optimal hedging and DCA exit strategies.",
                    "Consider market conditions, volatility, and profit-taking opportunities.",
                    "Optimize for risk-adjusted returns and capital preservation.",
                    "Provide clear hedging recommendations with execution parameters."
                ]
            )
            
            # 初始化 DCA 策略 Agent
            self.agno_dca_strategist = Agent(
                name="DCAStrategist",
                role="Design optimal DCA exit strategies for profitable positions",
                agent_id="dca-strategist",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=self.use_reasoning,
                response_model=DCAExitPlan,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a quantitative trading strategist specializing in DCA exit strategies.",
                    "Design optimal exit plans that maximize profits while minimizing market impact.",
                    "Consider market microstructure, volatility patterns, and liquidity conditions.",
                    "Balance between profit maximization and risk management.",
                    "Provide detailed execution plans with adaptive parameters."
                ]
            )
            
            logger.info("hedge_agent_agno_initialized")
            self.is_initialized = True
            
        except Exception as e:
            logger.error("hedge_agent_initialization_failed", error=str(e))
            self.agno_available = False
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行對沖和 DCA 分析"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("hedge_agent_execution_started")
            
            # 1. 獲取所有盈利持倉
            profitable_positions = await self._get_profitable_positions()
            
            if not profitable_positions:
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有盈利持倉需要對沖或 DCA"}
                )
            
            # 2. 分析每個持倉的對沖需求
            hedge_actions = []
            
            for position in profitable_positions:
                try:
                    # AI 對沖分析
                    hedge_analysis = await self._analyze_hedge_with_ai(position)
                    
                    if hedge_analysis:
                        # 根據分析結果執行相應操作
                        if hedge_analysis.hedge_recommendation == 'dca_exit':
                            # 制定 DCA 出貨計劃
                            dca_plan = await self._create_dca_plan_with_ai(position, hedge_analysis)
                            
                            if dca_plan:
                                # 執行 DCA 出貨
                                execution_result = await self._execute_dca_plan(position, dca_plan)
                                
                                hedge_actions.append({
                                    "position_id": position['id'],
                                    "action": "dca_exit",
                                    "analysis": hedge_analysis,
                                    "dca_plan": dca_plan,
                                    "execution_result": execution_result
                                })
                        
                        elif hedge_analysis.hedge_recommendation in ['partial_hedge', 'full_hedge']:
                            # 執行對沖操作
                            hedge_result = await self._execute_hedge(position, hedge_analysis)
                            
                            hedge_actions.append({
                                "position_id": position['id'],
                                "action": "hedge",
                                "analysis": hedge_analysis,
                                "hedge_result": hedge_result
                            })
                
                except Exception as e:
                    logger.error("position_hedge_analysis_failed", 
                               position_id=position.get('id'), 
                               error=str(e))
                    continue
            
            # 3. 檢查並執行活躍的 DCA 計劃
            dca_executions = await self._execute_active_dca_plans()
            
            logger.info("hedge_agent_execution_completed", 
                       positions_analyzed=len(profitable_positions),
                       hedge_actions=len(hedge_actions),
                       dca_executions=len(dca_executions))
            
            return AgentResult(
                agent_name=self.name,
                data=hedge_actions + dca_executions,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "positions_analyzed": len(profitable_positions),
                    "hedge_actions": len(hedge_actions),
                    "dca_executions": len(dca_executions),
                    "agno_enhanced": self.agno_available
                }
            )
            
        except Exception as e:
            logger.error("hedge_agent_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )

    async def _get_profitable_positions(self) -> List[Dict[str, Any]]:
        """獲取所有盈利持倉"""
        try:
            # 從數據庫獲取盈利持倉
            # 這裡需要實際的數據庫查詢邏輯
            # 簡化實現，返回示例數據
            
            sample_positions = [
                {
                    "id": "pos_001",
                    "pool_address": "0x1234567890abcdef",
                    "chain": "bsc",
                    "token0": "WBNB",
                    "token1": "USDT",
                    "entry_price": 300.0,
                    "current_price": 360.0,  # 20% 收益
                    "liquidity_usd": 10000.0,
                    "unrealized_pnl": 2000.0,
                    "entry_timestamp": datetime.now() - timedelta(days=7)
                },
                {
                    "id": "pos_002",
                    "pool_address": "sol_pool_456",
                    "chain": "solana",
                    "token0": "SOL",
                    "token1": "USDC",
                    "entry_price": 100.0,
                    "current_price": 150.0,  # 50% 收益
                    "liquidity_usd": 5000.0,
                    "unrealized_pnl": 2500.0,
                    "entry_timestamp": datetime.now() - timedelta(days=3)
                }
            ]
            
            # 過濾出收益超過觸發閾值的持倉
            profitable_positions = []
            for pos in sample_positions:
                gain_pct = (pos['unrealized_pnl'] / pos['liquidity_usd']) * 100
                if gain_pct >= self.hedge_config['dca_trigger_gain_pct']:
                    pos['gain_pct'] = gain_pct
                    profitable_positions.append(pos)
            
            return profitable_positions
            
        except Exception as e:
            logger.error("get_profitable_positions_failed", error=str(e))
            return []

    async def _analyze_hedge_with_ai(self, position: Dict[str, Any]) -> Optional[HedgeAnalysis]:
        """使用 AI 分析對沖需求"""
        if not self.agno_hedge_analyzer:
            return None
        
        try:
            analysis_prompt = f"""
            Analyze the position for optimal hedging and DCA exit strategy:
            
            Position Details:
            - ID: {position['id']}
            - Pool: {position['pool_address']}
            - Chain: {position['chain']}
            - Token Pair: {position['token0']}/{position['token1']}
            - Entry Price: ${position['entry_price']:.2f}
            - Current Price: ${position['current_price']:.2f}
            - Unrealized Gain: {position['gain_pct']:.1f}%
            - Position Size: ${position['liquidity_usd']:,.0f}
            - Unrealized PnL: ${position['unrealized_pnl']:,.0f}
            - Days Held: {(datetime.now() - position['entry_timestamp']).days}
            
            Please analyze:
            1. Current profit level and risk exposure
            2. Optimal hedging strategy (none, partial, full, or DCA exit)
            3. Market conditions and volatility assessment
            4. DCA exit parameters if recommended
            5. Risk management considerations
            
            Consider:
            - Profit protection vs continued upside potential
            - Market volatility and trend analysis
            - Optimal exit timing and sizing
            - Gas costs and execution efficiency
            """
            
            response = await self.agno_hedge_analyzer.arun(analysis_prompt)
            
            if response and hasattr(response, 'structured_content'):
                analysis = response.structured_content
                logger.info("hedge_analysis_completed", 
                           position_id=position['id'],
                           recommendation=analysis.hedge_recommendation,
                           hedge_ratio=analysis.hedge_ratio)
                return analysis
            
            return None
            
        except Exception as e:
            logger.error("hedge_analysis_with_ai_failed", 
                        position_id=position['id'], 
                        error=str(e))
            return None

    async def _create_dca_plan_with_ai(self, position: Dict[str, Any], 
                                     analysis: HedgeAnalysis) -> Optional[DCAExitPlan]:
        """使用 AI 制定 DCA 出貨計劃"""
        if not self.agno_dca_strategist:
            return None
        
        try:
            plan_prompt = f"""
            Create an optimal DCA exit plan based on the hedge analysis:
            
            Position: {position['id']}
            Current Gain: {position['gain_pct']:.1f}%
            Position Size: ${position['liquidity_usd']:,.0f}
            
            Hedge Analysis:
            - Recommendation: {analysis.hedge_recommendation}
            - DCA Exit Percentage: {analysis.dca_exit_percentage:.1%}
            - Suggested Orders: {analysis.dca_orders_count}
            - Interval: {analysis.dca_interval_minutes} minutes
            - Market Risk: {analysis.market_risk_level}
            - Urgency: {analysis.execution_urgency}
            
            Analysis Reasoning: {analysis.reasoning}
            
            Please design:
            1. Detailed DCA execution strategy
            2. Order size distribution and timing
            3. Market condition triggers
            4. Risk management parameters
            5. Adaptive execution rules
            
            Optimize for:
            - Profit maximization
            - Market impact minimization
            - Execution efficiency
            - Risk management
            """
            
            response = await self.agno_dca_strategist.arun(plan_prompt)
            
            if response and hasattr(response, 'structured_content'):
                plan = response.structured_content
                logger.info("dca_plan_created", 
                           position_id=position['id'],
                           strategy=plan.exit_strategy,
                           total_amount=plan.total_exit_amount)
                return plan
            
            return None
            
        except Exception as e:
            logger.error("dca_plan_creation_with_ai_failed", 
                        position_id=position['id'], 
                        error=str(e))
            return None

    async def _execute_dca_plan(self, position: Dict[str, Any], 
                              plan: DCAExitPlan) -> Dict[str, Any]:
        """執行 DCA 出貨計劃"""
        try:
            logger.info("dca_plan_execution_started", 
                       position_id=position['id'],
                       strategy=plan.exit_strategy)
            
            # 將計劃添加到活躍計劃中
            self.active_dca_plans[position['id']] = {
                "position": position,
                "plan": plan,
                "created_at": datetime.now(),
                "executed_orders": 0,
                "total_executed_amount": 0.0,
                "status": "active"
            }
            
            # 如果計劃要求立即開始，執行第一筆訂單
            if plan.start_immediately:
                first_order_result = await self._execute_single_dca_order(position['id'])
                
                return {
                    "success": True,
                    "plan_created": True,
                    "immediate_execution": first_order_result,
                    "timestamp": get_utc_timestamp().isoformat()
                }
            else:
                return {
                    "success": True,
                    "plan_created": True,
                    "immediate_execution": None,
                    "timestamp": get_utc_timestamp().isoformat()
                }
            
        except Exception as e:
            logger.error("dca_plan_execution_failed", 
                        position_id=position['id'], 
                        error=str(e))
            return {
                "success": False,
                "error": str(e),
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def _execute_hedge(self, position: Dict[str, Any], 
                           analysis: HedgeAnalysis) -> Dict[str, Any]:
        """執行對沖操作"""
        try:
            logger.info("hedge_execution_started", 
                       position_id=position['id'],
                       hedge_ratio=analysis.hedge_ratio)
            
            # 根據對沖工具執行對沖
            if analysis.hedge_instrument == 'perpetual' and self.binance_tool:
                # 使用永續合約對沖
                hedge_result = await self._execute_perpetual_hedge(position, analysis)
            elif analysis.hedge_instrument == 'spot_sell':
                # 使用現貨賣出對沖
                hedge_result = await self._execute_spot_hedge(position, analysis)
            else:
                hedge_result = {
                    "success": False,
                    "error": f"Unsupported hedge instrument: {analysis.hedge_instrument}"
                }
            
            return hedge_result
            
        except Exception as e:
            logger.error("hedge_execution_failed", 
                        position_id=position['id'], 
                        error=str(e))
            return {
                "success": False,
                "error": str(e),
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def _execute_perpetual_hedge(self, position: Dict[str, Any], 
                                     analysis: HedgeAnalysis) -> Dict[str, Any]:
        """執行永續合約對沖"""
        if not self.binance_tool:
            return {"success": False, "error": "Binance tool not available"}
        
        # 實現永續合約對沖邏輯
        return {
            "success": True,
            "hedge_type": "perpetual",
            "hedge_ratio": analysis.hedge_ratio,
            "timestamp": get_utc_timestamp().isoformat()
        }

    async def _execute_spot_hedge(self, position: Dict[str, Any], 
                                analysis: HedgeAnalysis) -> Dict[str, Any]:
        """執行現貨對沖"""
        # 根據鏈選擇交換工具
        if position['chain'] == 'solana' and self.jupiter_tool:
            # 使用 Jupiter 執行現貨賣出
            pass
        elif position['chain'] == 'bsc' and self.oneinch_tool:
            # 使用 1inch 執行現貨賣出
            pass
        
        return {
            "success": True,
            "hedge_type": "spot_sell",
            "hedge_ratio": analysis.hedge_ratio,
            "timestamp": get_utc_timestamp().isoformat()
        }

    async def _execute_active_dca_plans(self) -> List[Dict[str, Any]]:
        """執行活躍的 DCA 計劃"""
        executions = []
        
        for position_id, dca_state in self.active_dca_plans.items():
            try:
                if dca_state['status'] != 'active':
                    continue
                
                # 檢查是否到了執行時間
                if self._should_execute_dca_order(dca_state):
                    result = await self._execute_single_dca_order(position_id)
                    executions.append(result)
            
            except Exception as e:
                logger.error("active_dca_execution_failed", 
                           position_id=position_id, 
                           error=str(e))
                continue
        
        return executions

    def _should_execute_dca_order(self, dca_state: Dict[str, Any]) -> bool:
        """檢查是否應該執行 DCA 訂單"""
        plan = dca_state['plan']
        last_execution = dca_state.get('last_execution_time')
        
        if not last_execution:
            return True  # 第一次執行
        
        # 檢查時間間隔
        interval = timedelta(minutes=plan.dca_interval_minutes)
        return datetime.now() - last_execution >= interval

    async def _execute_single_dca_order(self, position_id: str) -> Dict[str, Any]:
        """執行單次 DCA 訂單"""
        try:
            dca_state = self.active_dca_plans.get(position_id)
            if not dca_state:
                return {"success": False, "error": "DCA plan not found"}
            
            position = dca_state['position']
            plan = dca_state['plan']
            
            # 計算訂單大小
            order_size = plan.total_exit_amount / plan.dca_orders_count
            
            # 根據鏈執行交換
            if position['chain'] == 'solana' and self.jupiter_tool:
                # 使用 Jupiter 執行
                result = {"success": True, "chain": "solana", "amount": order_size}
            elif position['chain'] == 'bsc' and self.oneinch_tool:
                # 使用 1inch 執行
                result = {"success": True, "chain": "bsc", "amount": order_size}
            else:
                result = {"success": False, "error": "No suitable swap tool available"}
            
            # 更新 DCA 狀態
            if result.get("success"):
                dca_state['executed_orders'] += 1
                dca_state['total_executed_amount'] += order_size
                dca_state['last_execution_time'] = datetime.now()
                
                # 檢查是否完成
                if dca_state['executed_orders'] >= plan.dca_orders_count:
                    dca_state['status'] = 'completed'
            
            return result
            
        except Exception as e:
            logger.error("single_dca_order_failed", 
                        position_id=position_id, 
                        error=str(e))
            return {"success": False, "error": str(e)}

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.jupiter_tool:
                await self.jupiter_tool.cleanup()
            if self.oneinch_tool:
                await self.oneinch_tool.cleanup()
            if self.binance_tool:
                await self.binance_tool.cleanup()
            
            logger.info("hedge_agent_cleanup_completed")
            
        except Exception as e:
            logger.error("hedge_agent_cleanup_failed", error=str(e))
