"""
DyFlow v3.3 StrategyAgent - 策略生成代理
負責 Phase 6 的策略生成和 LP 計劃創建
"""

import asyncio
import structlog
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .base_agent import BaseAgent
from .planner_agno import PlannerAgnoAgent
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

@dataclass
class LPPlan:
    """LP 策略計劃"""
    pool_id: str
    strategy_type: str
    allocation_amount: float
    expected_apy: float
    risk_level: str
    reasoning: str
    priority: int = 1

class StrategyAgent(BaseAgent):
    """
    策略生成代理 - Phase 6
    負責生成 LP 策略計劃和投資組合優化
    """
    
    def __init__(self, name: str = "StrategyAgent", config: Dict[str, Any] = None):
        if config is None:
            config = {}
        super().__init__(name, config)
        self.planner_agno = None
        self.strategy_types = config.get('strategy_types', [
            'SPOT_BALANCED',
            'CURVE_BALANCED', 
            'BID_ASK_BALANCED',
            'SPOT_IMBALANCED_DAMM'
        ])
        self.risk_assessment = config.get('risk_assessment', True)
        self.max_allocation_per_pool = config.get('max_allocation_per_pool', 0.15)
        self.total_capital = config.get('total_capital', 100000)
        
    async def initialize(self) -> bool:
        """初始化策略代理"""
        try:
            logger.info("strategy_agent_initializing")
            
            # 初始化 PlannerAgno 作為策略生成引擎
            planner_config = {
                'debug_mode': False,
                'use_reasoning': True,
                'primary_model': 'gpt-4o-mini',
                'fallback_model': 'gpt-3.5-turbo'
            }
            
            self.planner_agno = PlannerAgnoAgent(planner_config)
            await self.planner_agno.initialize()
            
            self.is_initialized = True
            logger.info("strategy_agent_initialized", 
                       strategy_types=self.strategy_types,
                       total_capital=self.total_capital)
            return True
            
        except Exception as e:
            logger.error("strategy_agent_initialization_failed", error=str(e))
            return False
    
    async def generate_lp_plans(self) -> Dict[str, Any]:
        """
        生成 LP 策略計劃
        這是 Phase 6 的核心方法
        """
        if not self.is_initialized:
            await self.initialize()
            
        try:
            logger.info("generating_lp_plans_started")
            
            # 使用 PlannerAgno 生成策略
            if self.planner_agno:
                result = await self.planner_agno.execute()
                
                if result.status == "success" and result.data:
                    # 轉換為 LPPlan 格式
                    lp_plans = []
                    for plan_data in result.data:
                        if hasattr(plan_data, 'pool_id'):
                            lp_plan = LPPlan(
                                pool_id=plan_data.pool_id,
                                strategy_type=plan_data.strategy_type,
                                allocation_amount=plan_data.allocation_amount,
                                expected_apy=plan_data.expected_apy,
                                risk_level=plan_data.risk_level,
                                reasoning=plan_data.reasoning,
                                priority=getattr(plan_data, 'priority', 1)
                            )
                            lp_plans.append(lp_plan)
                    
                    if lp_plans:
                        logger.info("lp_plans_generated_successfully", 
                                   plans_count=len(lp_plans),
                                   total_allocation=sum(p.allocation_amount for p in lp_plans))
                        
                        return {
                            'plans': lp_plans,
                            'total_plans': len(lp_plans),
                            'total_allocation': sum(p.allocation_amount for p in lp_plans),
                            'metadata': result.metadata,
                            'timestamp': get_utc_timestamp()
                        }
            
            # 如果 PlannerAgno 失敗，使用簡化策略生成
            logger.warning("planner_agno_failed_using_fallback_strategy")
            fallback_plans = await self._generate_fallback_plans()
            
            return {
                'plans': fallback_plans,
                'total_plans': len(fallback_plans),
                'total_allocation': sum(p.allocation_amount for p in fallback_plans),
                'metadata': {'fallback_mode': True},
                'timestamp': get_utc_timestamp()
            }
            
        except Exception as e:
            logger.error("lp_plans_generation_failed", error=str(e))
            return {'plans': [], 'error': str(e)}
    
    async def _generate_fallback_plans(self) -> List[LPPlan]:
        """生成後備策略計劃"""
        try:
            logger.info("generating_fallback_strategy_plans")
            
            # 模擬策略生成（基於 dyflow_workflow_executor.py 的邏輯）
            fallback_plans = [
                LPPlan(
                    pool_id="SOL/USDC",
                    strategy_type="SPOT_IMBALANCED_DAMM",
                    allocation_amount=self.total_capital * 0.4,
                    expected_apy=180.0,
                    risk_level="medium",
                    reasoning="高收益 Solana meme 幣策略，適合當前市場波動",
                    priority=1
                ),
                LPPlan(
                    pool_id="BNB/USDT",
                    strategy_type="CURVE_BALANCED",
                    allocation_amount=self.total_capital * 0.3,
                    expected_apy=145.0,
                    risk_level="low",
                    reasoning="穩定的 BSC 主流幣對，平衡風險收益",
                    priority=2
                ),
                LPPlan(
                    pool_id="ETH/USDC",
                    strategy_type="SPOT_BALANCED",
                    allocation_amount=self.total_capital * 0.3,
                    expected_apy=120.0,
                    risk_level="low",
                    reasoning="經典 ETH 對，提供組合穩定性",
                    priority=3
                )
            ]
            
            logger.info("fallback_plans_generated", plans_count=len(fallback_plans))
            return fallback_plans
            
        except Exception as e:
            logger.error("fallback_plans_generation_failed", error=str(e))
            return []
    
    async def validate_strategy_plan(self, plan: LPPlan) -> bool:
        """驗證策略計劃"""
        try:
            # 基本驗證
            if not plan.pool_id or not plan.strategy_type:
                return False
                
            if plan.allocation_amount <= 0:
                return False
                
            if plan.strategy_type not in self.strategy_types:
                logger.warning("invalid_strategy_type", 
                              strategy=plan.strategy_type,
                              allowed=self.strategy_types)
                return False
                
            # 分配限制檢查
            if plan.allocation_amount > self.total_capital * self.max_allocation_per_pool:
                logger.warning("allocation_exceeds_limit",
                              allocation=plan.allocation_amount,
                              limit=self.total_capital * self.max_allocation_per_pool)
                return False
                
            return True
            
        except Exception as e:
            logger.error("strategy_plan_validation_failed", error=str(e))
            return False
    
    async def execute(self) -> Dict[str, Any]:
        """執行策略生成（BaseAgent 接口）"""
        return await self.generate_lp_plans()
    
    def get_status(self) -> Dict[str, Any]:
        """獲取代理狀態"""
        return {
            'name': self.name,
            'initialized': self.is_initialized,
            'strategy_types': self.strategy_types,
            'total_capital': self.total_capital,
            'max_allocation_per_pool': self.max_allocation_per_pool,
            'planner_agno_available': self.planner_agno is not None,
            'timestamp': get_utc_timestamp()
        }
