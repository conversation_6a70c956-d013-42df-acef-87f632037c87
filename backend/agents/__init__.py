"""DyFlow v3.4 Agents - PRD 符合"""

# PRD 規範的核心 Agents
from .core_agent import CoreAgent
from .supervisor_agent import SupervisorAgent
from .health_guard_agent import HealthGuardAgent
from .onboarding_agent import OnboardingAgent
from .enhanced_market_intel_agent import EnhancedMarketIntelAgent
from .strategy_agent import StrategyAgent
from .execution_agent import ExecutionAgent
from .fee_collector_agent import FeeCollectorAgent

# 兼容的舊 Agents (可選)
try:
    from .risk_sentinel_agno import RiskSentinelAgent
except ImportError:
    RiskSentinelAgent = None

__all__ = [
    "CoreAgent",
    "SupervisorAgent",
    "HealthGuardAgent",
    "OnboardingAgent",
    "EnhancedMarketIntelAgent",
    "StrategyAgent",
    "ExecutionAgent",
    "FeeCollectorAgent",
    "RiskSentinelAgent"
]
