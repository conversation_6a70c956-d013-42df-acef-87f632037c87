"""
DyFlow v3.3 Risk Sentinel Agent
基於 Agno Framework 的動態風險監控系統
支援 per-position 風控和 ExitRequest 事件
"""

import asyncio
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import json
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.ollama import Ollama
    from agno.tools.reasoning import ReasoningTools
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True

    # 可選工具導入
    try:
        from agno.tools.duckduckgo import DuckDuckGoTools
        DUCKDUCKGO_AVAILABLE = True
    except ImportError:
        DUCKDUCKGO_AVAILABLE = False
        DuckDuckGoTools = None

    # 暫時設為 None，避免導入錯誤
    ReasoningTools = None
    SqliteStorage = None

except ImportError:
    AGNO_AVAILABLE = False
    DUCKDUCKGO_AVAILABLE = False
    # 定義替代類
    class BaseModel:
        pass
    class Agent:
        pass
    DuckDuckGoTools = None
    ReasoningTools = None
    SqliteStorage = None

# Dy-Flow v3 imports
from .base_agent import BaseAgent
# from .risk_sentinel import RiskSentinelAgent  # 暂时注释掉
# from ..utils.models_v3 import PoolRaw, RiskAlert, AgentResult, RiskLevel  # 暂时注释掉
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

# 临时定义缺失的类
@dataclass
class RiskAssessment:
    pool_id: str
    risk_level: str
    risk_score: float
    warnings: List[str]
    recommendations: List[str]

class RiskLevel:
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class RiskAlert:
    level: str
    message: str
    pool_id: str = ""
    timestamp: str = ""
    action_required: bool = False
    affected_pools: List[str] = None
    recommended_action: str = ""

    def __post_init__(self):
        if self.affected_pools is None:
            self.affected_pools = []

@dataclass
class AgentResult:
    agent_name: str
    data: Any
    timestamp: Any
    status: str
    metadata: Optional[Dict[str, Any]] = None

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class MarketRiskAssessment(BaseModel):
        """市場風險評估 - Agno 結構化輸出"""
        overall_risk_level: str = Field(..., description="整體風險級別", pattern="^(low|medium|high|critical)$")
        market_volatility: float = Field(..., ge=0.0, le=1.0, description="市場波動率 (0.0-1.0)")
        liquidity_risk: float = Field(..., ge=0.0, le=1.0, description="流動性風險")
        systemic_risk: float = Field(..., ge=0.0, le=1.0, description="系統性風險")
        
        confidence_score: float = Field(..., ge=0.0, le=1.0, description="評估信心度")
        risk_factors: List[str] = Field(default_factory=list, description="識別的風險因子")
        warning_signals: List[str] = Field(default_factory=list, description="預警信號")
        
        recommended_actions: List[str] = Field(default_factory=list, description="建議採取的行動")
        reasoning: str = Field(..., description="風險評估推理過程")

    class PoolRiskAnalysis(BaseModel):
        """池子風險分析 - Agno 結構化輸出"""
        pool_id: str = Field(..., description="池子ID")
        risk_level: str = Field(..., description="風險級別", pattern="^(low|medium|high|critical)$")
        risk_score: float = Field(..., ge=0.0, le=100.0, description="風險評分 (0-100)")
        
        # 具體風險因子
        impermanent_loss_risk: float = Field(..., ge=0.0, le=1.0, description="無常損失風險")
        liquidity_drain_risk: float = Field(..., ge=0.0, le=1.0, description="流動性流失風險")
        token_risk: float = Field(..., ge=0.0, le=1.0, description="代幣風險")
        smart_contract_risk: float = Field(..., ge=0.0, le=1.0, description="智能合約風險")
        
        # 市場風險
        price_volatility: float = Field(..., ge=0.0, le=1.0, description="價格波動風險")
        volume_risk: float = Field(..., ge=0.0, le=1.0, description="交易量風險")
        
        risk_events: List[str] = Field(default_factory=list, description="檢測到的風險事件")
        mitigation_strategies: List[str] = Field(default_factory=list, description="風險緩解策略")
        
        confidence_level: float = Field(..., ge=0.0, le=1.0, description="分析信心度")
        reasoning: str = Field(..., description="風險分析推理")
        
        # 告警建議
        alert_urgency: str = Field(..., description="告警緊急程度", pattern="^(none|low|medium|high|critical)$")
        requires_immediate_action: bool = Field(..., description="是否需要立即行動")

    class TrendAnalysis(BaseModel):
        """趨勢分析 - Agno 結構化輸出"""
        trend_direction: str = Field(..., description="趨勢方向", pattern="^(bullish|bearish|sideways|uncertain)$")
        trend_strength: float = Field(..., ge=0.0, le=1.0, description="趨勢強度")
        trend_sustainability: float = Field(..., ge=0.0, le=1.0, description="趨勢可持續性")
        
        key_indicators: List[str] = Field(default_factory=list, description="關鍵指標")
        support_levels: List[float] = Field(default_factory=list, description="支撐位")
        resistance_levels: List[float] = Field(default_factory=list, description="阻力位")
        
        confidence_score: float = Field(..., ge=0.0, le=1.0, description="趨勢分析信心度")
        time_horizon: str = Field(..., description="時間周期", pattern="^(short|medium|long)$")
        
        reasoning: str = Field(..., description="趨勢分析推理")

else:
    # 降級數據類
    @dataclass
    class MarketRiskAssessment:
        overall_risk_level: str
        market_volatility: float
        liquidity_risk: float
        systemic_risk: float
        confidence_score: float
        risk_factors: List[str]
        warning_signals: List[str]
        recommended_actions: List[str]
        reasoning: str

# ========== Agno Enhanced Risk Sentinel Agent ==========

class RiskSentinelAgnoAgent(BaseAgent):
    """
    DyFlow v3.4 動態風險監控 Agent
    符合 PRD 狀態機規範
    """

    # 狀態機定義 - 符合 v3.4 PRD 規範
    class State:
        WATCHING = "Watching"
        BREACH = "Breach"
        EMIT_EXIT = "EmitExit"

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # 檢查 Agno 可用性
        self.agno_available = AGNO_AVAILABLE

        # 添加agent_config属性以保持兼容性
        self.agent_config = self.config

        # DyFlow v3.4 動態風控配置
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        self.position_monitors: Dict[str, Dict[str, Any]] = {}
        self.exit_requests: List[Dict[str, Any]] = []

        # 狀態機初始化 - 符合 PRD 規範
        self.current_state = self.State.WATCHING
        self.state_history = []
        self.breach_details = None
        self.monitored_positions = {}
        
        if not self.agno_available:
            logger.warning("agno_framework_not_available", 
                         message="Agno framework not installed, falling back to traditional risk monitoring")
        
        # Agno 相關配置 - 使用默认值，因为agent_config是AgentConfig对象
        # LLM 模型選擇
        self.primary_model = getattr(self.config, 'model_name', 'qwen2.5:3b')
        self.secondary_model = 'qwen2.5:3b'  # 使用相同的本地模型
        self.use_reasoning = getattr(self.config, 'enable_reasoning', False)
        self.enable_memory = getattr(self.config, 'enable_memory', False)
        self.enable_market_data = True
        self.debug_mode = False
        
        # Agno Agent 實例
        self.agno_market_risk_analyzer: Optional[Agent] = None
        self.agno_pool_risk_analyzer: Optional[Agent] = None
        self.agno_trend_analyzer: Optional[Agent] = None
        
        # Memory 存儲
        self.memory_storage = None
        
        # 風險歷史緩存
        self.risk_history: Dict[str, List[Dict]] = {}
        self.last_market_analysis: Optional[datetime] = None

    def transition_state(self, new_state: str, trigger: str = None, position_id: str = None):
        """
        狀態轉換 - 符合 PRD 狀態機規範
        """
        old_state = self.current_state
        self.current_state = new_state

        # 記錄狀態歷史
        self.state_history.append({
            "from": old_state,
            "to": new_state,
            "trigger": trigger,
            "position_id": position_id,
            "timestamp": datetime.now().isoformat()
        })

        logger.info("risk_sentinel_state_transition",
                   from_state=old_state,
                   to_state=new_state,
                   trigger=trigger,
                   position_id=position_id)

    def can_transition(self, from_state: str, to_state: str, trigger: str = None) -> bool:
        """
        檢查狀態轉換是否有效 - 符合 PRD 狀態機規範
        """
        valid_transitions = {
            self.State.WATCHING: [self.State.BREACH],
            self.State.BREACH: [self.State.EMIT_EXIT],
            self.State.EMIT_EXIT: [self.State.WATCHING]
        }

        return to_state in valid_transitions.get(from_state, [])

    async def check_position_risks(self, position_id: str, position_data: Dict[str, Any]) -> bool:
        """
        檢查單個持倉風險 - 使用狀態機管理
        """
        # 確保在 WATCHING 狀態
        if self.current_state != self.State.WATCHING:
            logger.warning("risk_check_invalid_state",
                          current_state=self.current_state,
                          position_id=position_id)
            return False

        # 檢查風險指標
        risk_breaches = []

        # 1. IL_net 熔斷檢查 (使用 v3.4 公式)
        il_net = self._calculate_il_net(position_data)
        il_cut = position_data.get("risk_profile", {}).get("il_cut", -0.08)

        if il_net <= il_cut:
            risk_breaches.append({
                "type": "il_net_breach",
                "current_value": il_net,
                "threshold": il_cut,
                "severity": "high"
            })

        # 2. VaR 檢查
        var_95 = self._calculate_position_var(position_data)
        var_cut = position_data.get("risk_profile", {}).get("var_cut", 0.04)
        notional = position_data.get("notional_usd", 1)

        if var_95 > var_cut * notional:
            risk_breaches.append({
                "type": "var_breach",
                "current_value": var_95,
                "threshold": var_cut * notional,
                "severity": "high"
            })

        # 3. 波動率檢查
        sigma_1m = position_data.get("sigma_1m", 0)
        sigma_cut = position_data.get("risk_profile", {}).get("sigma_cut", 0.05)

        if sigma_1m > sigma_cut:
            risk_breaches.append({
                "type": "sigma_breach",
                "current_value": sigma_1m,
                "threshold": sigma_cut,
                "severity": "medium"
            })

        # 4. 持倉時間檢查
        holding_window = position_data.get("risk_profile", {}).get("holding_window", 3600)
        position_age = datetime.now().timestamp() - position_data.get("created_at", datetime.now().timestamp())

        if position_age > holding_window:
            risk_breaches.append({
                "type": "holding_window_expired",
                "current_value": position_age,
                "threshold": holding_window,
                "severity": "low"
            })

        # 如果有風險違規，轉換狀態
        if risk_breaches:
            # 狀態轉換: Watching → Breach
            trigger = "|".join([breach["type"] for breach in risk_breaches])
            self.transition_state(self.State.BREACH, trigger, position_id)

            # 記錄違規詳情
            self.breach_details = {
                "position_id": position_id,
                "breaches": risk_breaches,
                "timestamp": datetime.now().isoformat()
            }

            # 狀態轉換: Breach → EmitExit
            self.transition_state(self.State.EMIT_EXIT, "breach_confirmed", position_id)

            # 發射退出請求
            await self._emit_exit_request(position_id, position_data, risk_breaches)

            # 狀態轉換: EmitExit → Watching
            self.transition_state(self.State.WATCHING, "exit_emitted", position_id)

            return True

        return False

    async def _emit_exit_request(self, position_id: str, position_data: Dict[str, Any],
                                risk_breaches: List[Dict[str, Any]]):
        """發射退出請求事件 - 符合 PRD 規範"""
        try:
            # 確定退出資產
            exit_asset = position_data.get("risk_profile", {}).get("exit_asset", "USDC")

            # 確定主要違規原因
            primary_breach = max(risk_breaches, key=lambda x: {"high": 3, "medium": 2, "low": 1}.get(x["severity"], 0))
            reason_mapping = {
                "il_net_breach": "il",
                "var_breach": "var",
                "sigma_breach": "sigma",
                "holding_window_expired": "timeout"
            }
            reason = reason_mapping.get(primary_breach["type"], "unknown")

            # 創建退出請求事件
            exit_request = {
                "event_type": "ExitRequest",
                "position_id": position_id,
                "exit_asset": exit_asset,
                "reason": reason,
                "timestamp": datetime.now().isoformat(),
                "breach_details": risk_breaches
            }

            # 這裡應該通過 Agno 發佈事件
            logger.info("exit_request_emitted", **exit_request)

            # 記錄到退出請求列表
            self.exit_requests.append(exit_request)

        except Exception as e:
            logger.error("exit_request_emission_failed",
                        position_id=position_id, error=str(e))
        
    async def initialize(self) -> None:
        """初始化 Agno Agents 和基礎組件"""
        await super().initialize()
        await self.risk_sentinel.initialize()
        
        if not self.agno_available:
            logger.info("risk_sentinel_fallback_initialized", message="Using traditional risk monitoring only")
            self.is_initialized = True
            return
        
        try:
            # 初始化 Memory 存儲
            if self.enable_memory:
                self.memory_storage = SqliteStorage(
                    table_name="risk_sentinel_agno_memory",
                    db_file="data/agno_memory/risk_sentinel_sessions.db",
                    auto_upgrade_schema=True
                )
            
            # 初始化市場風險分析 Agent - 使用正確的 Agno Agent 構造函數
            tools_list = []
            if self.enable_market_data and DUCKDUCKGO_AVAILABLE and DuckDuckGoTools:
                tools_list.append(DuckDuckGoTools())

            self.agno_market_risk_analyzer = Agent(
                model=Ollama(id=self.primary_model, host=self.ollama_host),
                tools=tools_list,
                description="DeFi market risk analyst with expertise in systemic risk assessment",
                instructions=[
                    "You are a DeFi market risk analyst with expertise in systemic risk assessment.",
                    "Monitor market conditions for early warning signals of potential risks.",
                    "Analyze volatility patterns, liquidity flows, and systemic vulnerabilities.",
                    "Provide actionable risk assessments with specific recommended actions.",
                    "Focus on protecting LP positions from market crashes and protocol failures.",
                    "Use available market data tools to gather real-time information when needed.",
                    "Respond with clear, structured text analysis instead of JSON format."
                ]
            )
            
            # 初始化池子風險分析 Agent
            self.agno_pool_risk_analyzer = Agent(
                model=Ollama(id=self.primary_model, host=self.ollama_host),
                tools=[],  # 暫時不使用工具
                description="DeFi pool risk specialist focusing on individual pool vulnerabilities",
                instructions=[
                    "You are a DeFi pool risk specialist focusing on individual pool vulnerabilities.",
                    "Analyze pools for impermanent loss, liquidity drain, and token risks.",
                    "Evaluate smart contract risks and protocol-specific vulnerabilities.",
                    "Provide detailed risk scoring with specific mitigation strategies.",
                    "Consider both technical and market factors in your analysis.",
                    "Prioritize risks that could cause immediate financial loss.",
                    "Respond with clear, structured text analysis instead of JSON format."
                ]
            )
            
            # 初始化趨勢分析 Agent
            self.agno_trend_analyzer = Agent(
                name="MarketTrendAnalyzer",
                role="Analyze market trends and predict directional movements",
                agent_id="risk-sentinel-trend-analyzer",
                model=Ollama(id=self.primary_model, host=self.ollama_host),
                tools=[
                    ReasoningTools(add_instructions=True)
                ] + ([DuckDuckGoTools()] if self.enable_market_data and DUCKDUCKGO_AVAILABLE else []),
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=TrendAnalysis,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="trend_analysis_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a market trend analyst specializing in DeFi and crypto markets.",
                    "Analyze price movements, volume patterns, and market sentiment.",
                    "Identify key support and resistance levels for major tokens.",
                    "Assess trend strength and sustainability for strategic planning.",
                    "Provide insights that help optimize LP position timing and sizing.",
                    "Focus on trends that impact liquidity provision profitability."
                ]
            )
            
            logger.info("risk_sentinel_agno_initialized",
                       primary_model=self.primary_model,
                       secondary_model=self.secondary_model,
                       reasoning_enabled=self.use_reasoning,
                       memory_enabled=self.enable_memory,
                       market_data_enabled=self.enable_market_data,
                       debug_mode=self.debug_mode)
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error("risk_sentinel_agno_initialization_failed", error=str(e))
            # 降級到傳統模式
            self.agno_available = False
            logger.warning("falling_back_to_traditional_risk_monitoring", error=str(e))
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行 Agno 增強版風險監控"""
        if not self.is_initialized:
            await self.initialize()
        
        # 如果 Agno 不可用，使用傳統風險監控
        if not self.agno_available:
            traditional_result = await self.risk_sentinel.execute()
            traditional_result.metadata = traditional_result.metadata or {}
            traditional_result.metadata["agno_enhanced"] = False
            traditional_result.metadata["fallback_mode"] = True
            return traditional_result
        
        try:
            logger.info("risk_sentinel_agno_execution_started")

            # 1. 獲取當前位置和池子信息
            active_positions = self._get_active_positions()
            monitored_pools = self._get_monitored_pools()
            
            risk_alerts = []
            ai_analyses = []
            
            # 2. AI 市場風險分析
            market_risk = await self._analyze_market_risk_with_ai()
            if market_risk:
                ai_analyses.append(("market_risk", market_risk))
                
                # 基於市場風險生成告警
                if market_risk.overall_risk_level in ["high", "critical"]:
                    for action in market_risk.recommended_actions:
                        alert = RiskAlert(
                            level=market_risk.overall_risk_level,
                            message=f"Market Risk Alert: {action}",
                            pool_id="MARKET",
                            trigger_type="market_analysis",
                            timestamp=get_utc_timestamp(),
                            resolved=False
                        )
                        risk_alerts.append(alert)
            
            # 3. AI 趨勢分析
            trend_analysis = await self._analyze_market_trends_with_ai()
            if trend_analysis:
                ai_analyses.append(("trend_analysis", trend_analysis))
            
            # 4. 批量池子風險分析
            for pool in monitored_pools[:5]:  # 限制前5個池子進行深度 AI 分析
                try:
                    pool_risk = await self._analyze_pool_risk_with_ai(pool, market_risk, trend_analysis)
                    if pool_risk:
                        ai_analyses.append(("pool_risk", pool_risk))
                        
                        # 基於池子風險生成告警
                        if pool_risk.requires_immediate_action or pool_risk.risk_level in ["high", "critical"]:
                            for strategy in pool_risk.mitigation_strategies:
                                alert = RiskAlert(
                                    level=pool_risk.risk_level,
                                    message=f"Pool Risk Alert: {strategy}",
                                    pool_id=pool_risk.pool_id,
                                    trigger_type="pool_analysis",
                                    timestamp=get_utc_timestamp(),
                                    resolved=False
                                )
                                risk_alerts.append(alert)
                        
                except Exception as e:
                    pool_id = pool.get('pool_id', 'unknown') if isinstance(pool, dict) else getattr(pool, 'id', 'unknown')
                    logger.error("agno_pool_risk_analysis_failed",
                               pool_id=pool_id,
                               error=str(e))
            
            # 5. 傳統風險檢查（混合模式）
            traditional_result = await self.risk_sentinel.execute()
            if traditional_result.status == "success" and traditional_result.data:
                risk_alerts.extend(traditional_result.data)
            
            # 6. 風險去重和優先級排序
            unique_alerts = self._deduplicate_and_prioritize_alerts(risk_alerts)
            
            # 7. 更新風險歷史
            self._update_risk_history(ai_analyses)
            
            logger.info("risk_sentinel_agno_execution_completed", 
                       total_alerts=len(unique_alerts),
                       ai_analyses_count=len(ai_analyses),
                       market_risk_level=market_risk.overall_risk_level if market_risk else "unknown")
            
            return AgentResult(
                agent_name=self.name,
                data=unique_alerts,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "agno_enhanced": True,
                    "total_alerts": len(unique_alerts),
                    "ai_analyses_count": len(ai_analyses),
                    "high_priority_alerts": len([a for a in unique_alerts if a.level in ["high", "critical"]]),
                    "market_risk_assessment": market_risk.model_dump() if market_risk and hasattr(market_risk, 'model_dump') else None,
                    "trend_analysis": trend_analysis.model_dump() if trend_analysis and hasattr(trend_analysis, 'model_dump') else None,
                    "ai_insights": {
                        "market_volatility": market_risk.market_volatility if market_risk else 0.0,
                        "systemic_risk": market_risk.systemic_risk if market_risk else 0.0,
                        "trend_direction": trend_analysis.trend_direction if trend_analysis else "unknown",
                        "trend_strength": trend_analysis.trend_strength if trend_analysis else 0.0,
                        "total_risk_factors": len(market_risk.risk_factors) if market_risk else 0,
                        "total_warning_signals": len(market_risk.warning_signals) if market_risk else 0
                    }
                }
            )
            
        except Exception as e:
            logger.error("risk_sentinel_agno_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e), "agno_enhanced": True}
            )

    async def _analyze_market_risk_with_ai(self):
        """使用 AI 進行市場風險分析"""
        if not self.agno_market_risk_analyzer:
            return None
            
        # 檢查是否需要更新市場分析（30分鐘更新一次）
        if (self.last_market_analysis and 
            datetime.now() - self.last_market_analysis < timedelta(minutes=30)):
            return None
            
        try:
            market_prompt = f"""
            Analyze current DeFi market risk conditions:
            
            Current timestamp: {get_utc_timestamp().isoformat()}
            
            Please assess:
            1. Overall market risk level (low/medium/high/critical)
            2. Market volatility and liquidity conditions
            3. Systemic risks affecting DeFi protocols
            4. Early warning signals for potential market stress
            
            Consider recent events:
            - Major DeFi protocol updates or vulnerabilities
            - Regulatory developments affecting crypto markets
            - Macroeconomic factors impacting risk appetite
            - Large liquidations or TVL movements
            
            Focus on risks that could impact LP positions:
            - Impermanent loss risks from volatility
            - Liquidity drainage from protocols
            - Smart contract vulnerabilities
            - Token depegging events
            
            Provide specific actionable recommendations for risk mitigation.
            """
            
            response = await self.agno_market_risk_analyzer.arun(market_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                assessment = response.structured_content
                self.last_market_analysis = datetime.now()
                
                logger.info("market_risk_analysis_completed", 
                           risk_level=assessment.overall_risk_level,
                           volatility=assessment.market_volatility,
                           confidence=assessment.confidence_score)
                return assessment
            else:
                logger.warning("market_risk_analysis_no_structured_response")
                return None
                
        except Exception as e:
            logger.error("market_risk_analysis_with_ai_failed", error=str(e))
            return None

    async def _analyze_market_trends_with_ai(self):
        """使用 AI 進行市場趨勢分析"""
        if not self.agno_trend_analyzer:
            return None
            
        try:
            trend_prompt = f"""
            Analyze current market trends for DeFi and major cryptocurrencies:
            
            Current timestamp: {get_utc_timestamp().isoformat()}
            
            Please analyze:
            1. Overall trend direction (bullish/bearish/sideways/uncertain)
            2. Trend strength and sustainability
            3. Key support and resistance levels
            4. Volume and momentum indicators
            
            Focus on trends affecting LP strategies:
            - ETH trends (major LP asset)
            - BNB trends (BSC ecosystem)
            - SOL trends (Solana ecosystem)
            - Stablecoin trends and depegging risks
            
            Consider multiple timeframes:
            - Short-term (1-7 days): immediate tactical decisions
            - Medium-term (1-4 weeks): position sizing
            - Long-term (1-3 months): strategic allocation
            
            Provide insights for optimizing LP position timing and risk management.
            """
            
            response = await self.agno_trend_analyzer.arun(trend_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                analysis = response.structured_content
                logger.info("trend_analysis_completed", 
                           direction=analysis.trend_direction,
                           strength=analysis.trend_strength,
                           confidence=analysis.confidence_score)
                return analysis
            else:
                logger.warning("trend_analysis_no_structured_response")
                return None
                
        except Exception as e:
            logger.error("trend_analysis_with_ai_failed", error=str(e))
            return None

    # ========== VaR Calculation Enhancement ==========

    def calculate_portfolio_var(self, positions: List[Dict[str, Any]],
                              confidence_level: float = 0.95,
                              time_horizon_days: int = 1) -> Dict[str, float]:
        """計算投資組合 VaR (Value at Risk)"""
        try:
            if not positions:
                return {"var_amount": 0.0, "var_percentage": 0.0, "confidence_level": confidence_level}

            # 計算總投資組合價值
            total_portfolio_value = sum(pos.get('current_value', 0) for pos in positions)

            if total_portfolio_value <= 0:
                return {"var_amount": 0.0, "var_percentage": 0.0, "confidence_level": confidence_level}

            # 獲取歷史收益率數據
            portfolio_returns = self._get_portfolio_historical_returns(positions)

            if len(portfolio_returns) < 30:  # 至少需要30天數據
                # 使用參數化方法估算 VaR
                return self._calculate_parametric_var(
                    total_portfolio_value, positions, confidence_level, time_horizon_days
                )

            # 使用歷史模擬法計算 VaR
            return self._calculate_historical_var(
                total_portfolio_value, portfolio_returns, confidence_level, time_horizon_days
            )

        except Exception as e:
            logger.error("portfolio_var_calculation_failed", error=str(e))
            return {"var_amount": 0.0, "var_percentage": 0.0, "confidence_level": confidence_level, "error": str(e)}

    def _get_portfolio_historical_returns(self, positions: List[Dict[str, Any]],
                                        days: int = 90) -> List[float]:
        """獲取投資組合歷史收益率"""
        try:
            # 這裡需要從數據庫或外部API獲取歷史價格數據
            # 簡化實現，使用模擬數據

            import random
            import numpy as np

            # 模擬歷史收益率（實際應從數據庫獲取）
            returns = []
            for _ in range(days):
                # 模擬日收益率，考慮不同策略的波動性
                daily_return = 0.0
                total_weight = 0.0

                for pos in positions:
                    weight = pos.get('current_value', 0)
                    strategy = pos.get('strategy_type', 'passive_high_tvl')

                    # 不同策略的波動性
                    if strategy == 'delta_neutral':
                        volatility = 0.02  # 2% 日波動
                    elif strategy == 'ladder_ss':
                        volatility = 0.05  # 5% 日波動
                    else:  # passive_high_tvl
                        volatility = 0.015  # 1.5% 日波動

                    # 生成正態分佈收益率
                    token_return = np.random.normal(0, volatility)
                    daily_return += token_return * weight
                    total_weight += weight

                if total_weight > 0:
                    daily_return /= total_weight
                    returns.append(daily_return)

            return returns

        except Exception as e:
            logger.error("get_portfolio_historical_returns_failed", error=str(e))
            return []

    def _calculate_historical_var(self, portfolio_value: float, returns: List[float],
                                confidence_level: float, time_horizon_days: int) -> Dict[str, float]:
        """使用歷史模擬法計算 VaR"""
        try:
            import numpy as np

            # 調整時間範圍
            if time_horizon_days != 1:
                returns = [r * math.sqrt(time_horizon_days) for r in returns]

            # 排序收益率
            sorted_returns = sorted(returns)

            # 計算 VaR 分位數
            var_index = int((1 - confidence_level) * len(sorted_returns))
            var_return = sorted_returns[var_index] if var_index < len(sorted_returns) else sorted_returns[0]

            # 計算 VaR 金額
            var_amount = abs(var_return * portfolio_value)
            var_percentage = abs(var_return) * 100

            logger.info("historical_var_calculated",
                       var_amount=var_amount,
                       var_percentage=var_percentage,
                       confidence_level=confidence_level)

            return {
                "var_amount": var_amount,
                "var_percentage": var_percentage,
                "confidence_level": confidence_level,
                "method": "historical_simulation",
                "time_horizon_days": time_horizon_days,
                "sample_size": len(returns)
            }

        except Exception as e:
            logger.error("historical_var_calculation_failed", error=str(e))
            return {"var_amount": 0.0, "var_percentage": 0.0, "confidence_level": confidence_level}

    def _calculate_parametric_var(self, portfolio_value: float, positions: List[Dict[str, Any]],
                                confidence_level: float, time_horizon_days: int) -> Dict[str, float]:
        """使用參數化方法計算 VaR"""
        try:
            import numpy as np
            from scipy import stats

            # 估算投資組合波動率
            portfolio_volatility = self._estimate_portfolio_volatility(positions)

            # 調整時間範圍
            adjusted_volatility = portfolio_volatility * math.sqrt(time_horizon_days)

            # 計算 VaR（假設正態分佈）
            z_score = stats.norm.ppf(1 - confidence_level)
            var_return = abs(z_score * adjusted_volatility)

            var_amount = var_return * portfolio_value
            var_percentage = var_return * 100

            logger.info("parametric_var_calculated",
                       var_amount=var_amount,
                       var_percentage=var_percentage,
                       portfolio_volatility=portfolio_volatility)

            return {
                "var_amount": var_amount,
                "var_percentage": var_percentage,
                "confidence_level": confidence_level,
                "method": "parametric",
                "time_horizon_days": time_horizon_days,
                "portfolio_volatility": portfolio_volatility
            }

        except Exception as e:
            logger.error("parametric_var_calculation_failed", error=str(e))
            return {"var_amount": 0.0, "var_percentage": 0.0, "confidence_level": confidence_level}

    def _estimate_portfolio_volatility(self, positions: List[Dict[str, Any]]) -> float:
        """估算投資組合波動率"""
        try:
            total_value = sum(pos.get('current_value', 0) for pos in positions)
            if total_value <= 0:
                return 0.0

            weighted_volatility = 0.0

            for pos in positions:
                weight = pos.get('current_value', 0) / total_value
                strategy = pos.get('strategy_type', 'passive_high_tvl')

                # 基於策略類型的波動率估算
                if strategy == 'delta_neutral':
                    volatility = 0.02  # 2% 日波動
                elif strategy == 'ladder_ss':
                    volatility = 0.05  # 5% 日波動
                else:  # passive_high_tvl
                    volatility = 0.015  # 1.5% 日波動

                weighted_volatility += weight * volatility

            return weighted_volatility

        except Exception as e:
            logger.error("estimate_portfolio_volatility_failed", error=str(e))
            return 0.03  # 默認 3% 日波動

    def check_var_fuse(self, positions: List[Dict[str, Any]],
                      var_threshold_pct: float = 4.0) -> Optional[RiskAlert]:
        """檢查 VaR 熔斷條件"""
        try:
            # 計算 24 小時 VaR
            var_result = self.calculate_portfolio_var(positions, confidence_level=0.95, time_horizon_days=1)

            var_percentage = var_result.get('var_percentage', 0.0)

            if var_percentage >= var_threshold_pct:
                logger.warning("var_fuse_triggered",
                             var_percentage=var_percentage,
                             threshold=var_threshold_pct)

                return RiskAlert(
                    level=RiskLevel.CRITICAL,
                    message=f"VaR Fuse Triggered: 24h VaR {var_percentage:.2f}% >= {var_threshold_pct}% threshold",
                    affected_pools=[pos.get('pool_id', 'unknown') for pos in positions],
                    recommended_action="IMMEDIATE EXIT ALL POSITIONS",
                    timestamp=get_utc_timestamp()
                )

            return None

        except Exception as e:
            logger.error("var_fuse_check_failed", error=str(e))
            return None

    async def _analyze_pool_risk_with_ai(self, pool, market_risk, trend_analysis):
        """使用 AI 進行池子風險分析"""
        if not self.agno_pool_risk_analyzer:
            return None
            
        try:
            pool_prompt = f"""
            Perform comprehensive risk analysis for this liquidity pool:
            
            Pool Information:
            - Pool ID: {getattr(pool, 'id', 'unknown')}
            - Chain: {getattr(pool, 'chain', 'unknown')}
            - TVL: ${getattr(pool, 'tvl_usd', 0):,.2f}
            - 24h Volume: ${getattr(pool, 'volume_24h', 0):,.2f}
            
            Market Context:
            {market_risk.model_dump_json(indent=2) if market_risk and hasattr(market_risk, 'model_dump_json') else "No market risk assessment available"}
            
            Trend Context:
            {trend_analysis.model_dump_json(indent=2) if trend_analysis and hasattr(trend_analysis, 'model_dump_json') else "No trend analysis available"}
            
            Please analyze:
            1. Impermanent loss risk based on volatility and correlation
            2. Liquidity drain risk from protocol or market factors
            3. Token-specific risks (smart contract, team, regulatory)
            4. Protocol risks (smart contract bugs, governance risks)
            5. Market risks (price manipulation, low liquidity)
            
            Provide:
            - Specific risk scores for each category (0.0-1.0)
            - Overall risk level and score
            - Detected risk events requiring attention
            - Concrete mitigation strategies
            - Urgency assessment for any required actions
            
            Consider current market conditions and trends in your assessment.
            """
            
            response = await self.agno_pool_risk_analyzer.arun(pool_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                analysis = response.structured_content
                logger.debug("pool_risk_analysis_completed", 
                           pool_id=getattr(pool, 'id', 'unknown'),
                           risk_level=analysis.risk_level,
                           risk_score=analysis.risk_score)
                return analysis
            else:
                logger.warning("pool_risk_analysis_no_structured_response", 
                             pool_id=getattr(pool, 'id', 'unknown'))
                return None
                
        except Exception as e:
            logger.error("pool_risk_analysis_with_ai_failed", 
                        pool_id=getattr(pool, 'id', 'unknown'), 
                        error=str(e))
            return None

    def _deduplicate_and_prioritize_alerts(self, alerts: List[RiskAlert]) -> List[RiskAlert]:
        """去重和優先級排序告警"""
        # 去重邏輯：相同 pool_id 和相似 message 的告警
        unique_alerts = {}
        for alert in alerts:
            key = f"{alert.pool_id}_{alert.trigger_type}"
            if key not in unique_alerts or self._get_priority_score(alert) > self._get_priority_score(unique_alerts[key]):
                unique_alerts[key] = alert
        
        # 按優先級排序
        sorted_alerts = sorted(unique_alerts.values(), key=self._get_priority_score, reverse=True)
        return sorted_alerts

    def _get_priority_score(self, alert: RiskAlert) -> int:
        """計算告警優先級分數"""
        priority_map = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        return priority_map.get(alert.level, 0)

    def _update_risk_history(self, analyses: List[tuple]):
        """更新風險歷史記錄"""
        timestamp = get_utc_timestamp()
        for analysis_type, analysis_data in analyses:
            if analysis_type not in self.risk_history:
                self.risk_history[analysis_type] = []
            
            self.risk_history[analysis_type].append({
                "timestamp": timestamp,
                "data": analysis_data.model_dump() if hasattr(analysis_data, 'model_dump') else str(analysis_data)
            })
            
            # 保留最近24小時的記錄
            cutoff_time = timestamp - timedelta(hours=24)
            self.risk_history[analysis_type] = [
                record for record in self.risk_history[analysis_type] 
                if record["timestamp"] > cutoff_time
            ]

    async def get_risk_summary(self) -> Dict[str, Any]:
        """獲取風險摘要"""
        try:
            summary = {
                "agno_framework_available": self.agno_available,
                "market_risk_analyzer_available": self.agno_market_risk_analyzer is not None,
                "pool_risk_analyzer_available": self.agno_pool_risk_analyzer is not None,
                "trend_analyzer_available": self.agno_trend_analyzer is not None,
                "memory_enabled": self.enable_memory,
                "reasoning_enabled": self.use_reasoning,
                "market_data_enabled": self.enable_market_data,
                "last_market_analysis": self.last_market_analysis.isoformat() if self.last_market_analysis else None,
                "risk_history_entries": {k: len(v) for k, v in self.risk_history.items()}
            }
            
            return summary
            
        except Exception as e:
            logger.error("failed_to_get_risk_summary", error=str(e))
            return {"error": str(e)}

    def _get_active_positions(self) -> List[Dict[str, Any]]:
        """获取当前活跃位置（模拟数据）"""
        # 这里应该从数据库或交易系统获取真实的位置数据
        # 目前返回模拟数据
        return [
            {
                "position_id": "pos_001",
                "pool_id": "******************************************",
                "token0": "USDT",
                "token1": "BTCB",
                "amount_usd": 100000,
                "entry_price": 103000,
                "current_price": 103298,
                "pnl_pct": 0.29,
                "status": "active"
            },
            {
                "position_id": "pos_002",
                "pool_id": "******************************************",
                "token0": "BTCB",
                "token1": "WBNB",
                "amount_usd": 50000,
                "entry_price": 162.0,
                "current_price": 162.47,
                "pnl_pct": 0.29,
                "status": "active"
            }
        ]

    def _get_monitored_pools(self) -> List[Dict[str, Any]]:
        """获取监控的池子列表（模拟数据）"""
        # 这里应该从数据库获取真实的监控池子数据
        # 目前返回模拟数据
        return [
            {
                "pool_id": "******************************************",
                "token0": "USDT",
                "token1": "BTCB",
                "tvl_usd": 208639373544490046,
                "volume_24h": 55364159851017974607,
                "fee_tier": 500,
                "last_update": datetime.now()
            },
            {
                "pool_id": "******************************************",
                "token0": "BTCB",
                "token1": "WBNB",
                "tvl_usd": 81260772581431828,
                "volume_24h": 24946933560591401083,
                "fee_tier": 500,
                "last_update": datetime.now()
            }
        ]

    async def cleanup(self) -> None:
        """清理 Agno 資源"""
        try:
            # 清理基礎 risk sentinel (檢查是否存在)
            if hasattr(self, 'risk_sentinel') and self.risk_sentinel:
                await self.risk_sentinel.cleanup()

            # 清理風險歷史（可選）
            if hasattr(self, 'risk_history'):
                self.risk_history.clear()

            # 清理 Agno agents
            if hasattr(self, 'agno_market_risk_analyzer'):
                self.agno_market_risk_analyzer = None
            if hasattr(self, 'agno_pool_risk_analyzer'):
                self.agno_pool_risk_analyzer = None
            if hasattr(self, 'agno_trend_analyzer'):
                self.agno_trend_analyzer = None

            logger.info("risk_sentinel_agno_cleanup_completed")

        except Exception as e:
            logger.error("risk_sentinel_agno_cleanup_failed", error=str(e))

    def __str__(self) -> str:
        return f"RiskSentinelAgnoAgent(agno_enhanced={self.agno_available}, reasoning={self.use_reasoning})"

    # ========== DyFlow v3.3 動態風控方法 ==========

    async def monitor_position_risk(self, position_id: str, position_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """監控單個持倉的風險狀況"""
        logger.info("monitoring_position_risk", position_id=position_id)

        # 更新持倉數據
        self.active_positions[position_id] = position_data

        # 獲取風險配置
        risk_profile = position_data.get("risk_profile", {})
        if not risk_profile:
            logger.warning("no_risk_profile_found", position_id=position_id)
            return None

        # 檢查各項風險指標
        risk_breaches = []

        # 1. IL_net 熔斷檢查 (按照規範公式)
        il_net = self._calculate_il_net(position_data)
        il_cut = risk_profile.get("il_cut", -0.08)  # 默認 -8%

        if il_net <= il_cut:
            risk_breaches.append({
                "type": "il_net_breach",
                "current_value": il_net,
                "threshold": il_cut,
                "severity": "high"
            })

        # 2. VaR 檢查
        current_var = self._calculate_position_var(position_data)
        var_cut = risk_profile.get("var_cut", 0.05)  # 默認 5%

        if current_var > var_cut:
            risk_breaches.append({
                "type": "var_breach",
                "current_value": current_var,
                "threshold": var_cut,
                "severity": "medium"
            })

        # 3. 波動率檢查
        current_sigma = position_data.get("sigma", 0)
        sigma_cut = risk_profile.get("sigma_cut", 0.05)  # 默認 5%

        if current_sigma > sigma_cut:
            risk_breaches.append({
                "type": "sigma_breach",
                "current_value": current_sigma,
                "threshold": sigma_cut,
                "severity": "medium"
            })

        # 4. 持倉時間檢查
        holding_time = position_data.get("holding_time", 0)
        holding_window = risk_profile.get("holding_window", 3600)  # 默認 1 小時

        if holding_time >= holding_window:
            risk_breaches.append({
                "type": "holding_window_expired",
                "current_value": holding_time,
                "threshold": holding_window,
                "severity": "low"
            })

        # 如果有風險違規，生成 ExitRequest
        if risk_breaches:
            exit_request = await self._generate_exit_request(position_id, risk_breaches, risk_profile)
            return exit_request

        return None

    async def _generate_exit_request(self, position_id: str, risk_breaches: List[Dict],
                                   risk_profile: Dict[str, Any]) -> Dict[str, Any]:
        """生成退出請求"""
        # 選擇最嚴重的風險作為主要原因
        primary_breach = max(risk_breaches, key=lambda x: {
            "high": 3, "medium": 2, "low": 1
        }.get(x["severity"], 0))

        exit_asset = risk_profile.get("exit_asset", "SOL")  # 默認退出資產

        exit_request = {
            "position_id": position_id,
            "exit_asset": exit_asset,
            "reason": primary_breach["type"],
            "timestamp": datetime.now().isoformat(),
            "risk_breaches": risk_breaches,
            "urgency": primary_breach["severity"]
        }

        # 記錄退出請求
        self.exit_requests.append(exit_request)

        # 發佈 ExitRequest 事件到事件總線
        try:
            from ..events.dyflow_events import ExitRequestEvent, event_bus, EventType

            exit_event = ExitRequestEvent(
                position_id=position_id,
                exit_asset=exit_asset,
                reason=primary_breach["type"]
            )

            event_bus.publish(EventType.EXIT_REQUEST, exit_event)

            logger.info("exit_request_event_published",
                       position_id=position_id,
                       reason=primary_breach["type"],
                       exit_asset=exit_asset)
        except Exception as e:
            logger.error("exit_request_event_publish_failed", error=str(e))

        logger.info("exit_request_generated",
                   position_id=position_id,
                   reason=primary_breach["type"],
                   exit_asset=exit_asset,
                   urgency=primary_breach["severity"])

        return exit_request

    def _calculate_il_net(self, position_data: Dict[str, Any]) -> float:
        """
        計算 IL_net 根據規範公式:
        IL_net = IL_raw - fee_acc_usd / notional
        """
        # 獲取原始 IL
        il_raw = position_data.get("il_raw", position_data.get("il_pct", 0))

        # 獲取累積手續費 (USD)
        fee_acc_usd = position_data.get("fee_accrued_usd", 0)

        # 獲取名義本金
        notional = position_data.get("notional_usd", position_data.get("liquidityUsd", 1))

        # 計算 IL_net
        if notional > 0:
            il_net = il_raw - (fee_acc_usd / notional)
        else:
            il_net = il_raw

        logger.debug("il_net_calculation",
                    il_raw=il_raw,
                    fee_acc_usd=fee_acc_usd,
                    notional=notional,
                    il_net=il_net)

        return il_net

    def _calculate_position_var(self, position_data: Dict[str, Any]) -> float:
        """計算單個持倉的 VaR"""
        # 簡化的 VaR 計算
        liquidity_usd = position_data.get("liquidityUsd", 0)
        sigma = position_data.get("sigma", 0.02)  # 默認 2% 波動率

        # 使用 95% 置信度，1 天時間範圍
        var_95 = liquidity_usd * sigma * 1.645  # 95% 分位數
        var_pct = var_95 / liquidity_usd if liquidity_usd > 0 else 0

        return var_pct

    async def batch_monitor_positions(self) -> List[Dict[str, Any]]:
        """批量監控所有活躍持倉"""
        exit_requests = []

        for position_id, position_data in self.active_positions.items():
            try:
                exit_request = await self.monitor_position_risk(position_id, position_data)
                if exit_request:
                    exit_requests.append(exit_request)
            except Exception as e:
                logger.error("position_monitoring_failed",
                           position_id=position_id,
                           error=str(e))

        return exit_requests

    def update_position_data(self, position_id: str, updates: Dict[str, Any]):
        """更新持倉數據"""
        if position_id in self.active_positions:
            self.active_positions[position_id].update(updates)
        else:
            self.active_positions[position_id] = updates

    def remove_position(self, position_id: str):
        """移除已關閉的持倉"""
        if position_id in self.active_positions:
            del self.active_positions[position_id]

        # 清理相關的退出請求
        self.exit_requests = [
            req for req in self.exit_requests
            if req["position_id"] != position_id
        ]

    def get_risk_summary(self) -> Dict[str, Any]:
        """獲取風險摘要"""
        total_positions = len(self.active_positions)
        pending_exits = len(self.exit_requests)

        # 計算整體風險指標
        total_il = sum(pos.get("il_pct", 0) for pos in self.active_positions.values())
        avg_il = total_il / total_positions if total_positions > 0 else 0

        total_var = sum(self._calculate_position_var(pos) for pos in self.active_positions.values())
        avg_var = total_var / total_positions if total_positions > 0 else 0

        # 風險等級評估
        risk_level = "low"
        if avg_il <= -0.05 or avg_var > 0.03:
            risk_level = "medium"
        if avg_il <= -0.08 or avg_var > 0.05:
            risk_level = "high"
        if pending_exits > 0:
            risk_level = "critical"

        return {
            "total_positions": total_positions,
            "pending_exits": pending_exits,
            "avg_il_pct": avg_il,
            "avg_var_pct": avg_var,
            "risk_level": risk_level,
            "last_update": datetime.now().isoformat()
        }
