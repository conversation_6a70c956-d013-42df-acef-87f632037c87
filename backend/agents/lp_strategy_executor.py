"""
DyFlow v3.4 LP 策略執行器
實現四種 LP 策略的具體邏輯和價格範圍計算
基於 PRD 規範：SPOT_BALANCED, CURVE_BALANCED, BID_ASK_BALANCED, SPOT_IMBALANCED_DAMM
"""

import asyncio
import structlog
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Agno Framework imports
try:
    from agno import Agent
except ImportError:
    class Agent:
        pass

from ..utils.strategy_types import DLMMStrategyType, StrategyParameters
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

@dataclass
class LiquidityRange:
    """流動性範圍定義"""
    min_price: float
    max_price: float
    amount: float
    weight: float
    bin_id: Optional[int] = None

@dataclass
class StrategyExecution:
    """策略執行結果"""
    strategy_type: str
    pool_id: str
    chain: str
    ranges: List[LiquidityRange]
    total_amount: float
    expected_apr: float
    risk_score: float
    execution_time: datetime

class LPStrategyExecutor(Agent):
    """
    LP 策略執行器 v3.4
    
    實現四種策略的具體邏輯：
    1. SPOT_BALANCED: Low σ ≤ 1% + fee<0.2 → 對稱流動性
    2. CURVE_BALANCED: σ 1-3% no dynFee → 曲線分布
    3. BID_ASK_BALANCED: Spread<5 bps → 買賣價差
    4. SPOT_IMBALANCED_DAMM: σ>3% or fee≥0.4 → 單邊流動性
    """
    
    def __init__(self, config: Dict[str, Any]):
        # super().__init__()  # 暫時註釋避免初始化問題
        self.config = config
        self.name = "LPStrategyExecutor"
        
        # 策略映射矩陣 - 符合 PRD 規範
        self.strategy_matrix = {
            "low_vol_low_fee": DLMMStrategyType.SPOT_BALANCED,      # σ ≤ 1% + fee<0.2
            "med_vol_no_dyn": DLMMStrategyType.CURVE_BALANCED,      # σ 1-3% no dynFee
            "tight_spread": DLMMStrategyType.BID_ASK_BALANCED,      # Spread<5 bps
            "high_vol_high_fee": DLMMStrategyType.SPOT_IMBALANCED   # σ>3% or fee≥0.4
        }
        
        # Agno 上下文
        self.ctx = None
        
        logger.info("lp_strategy_executor_v34_initialized")
    
    def set_context(self, ctx):
        """設置 Agno 上下文"""
        self.ctx = ctx
    
    async def execute_strategy(self, pool_data: Dict[str, Any], 
                             notional_usd: float) -> StrategyExecution:
        """
        執行 LP 策略
        
        Args:
            pool_data: 池子數據 (包含 σ, fee_tvl_pct, spread 等)
            notional_usd: 投資金額 (USD)
            
        Returns:
            StrategyExecution: 策略執行結果
        """
        try:
            # 1. 根據池子特徵選擇策略
            strategy_type = self._select_strategy(pool_data)
            
            # 2. 計算流動性範圍
            ranges = await self._calculate_liquidity_ranges(
                strategy_type, pool_data, notional_usd
            )
            
            # 3. 評估風險和預期收益
            risk_score = self._calculate_risk_score(pool_data, strategy_type)
            expected_apr = self._estimate_apr(pool_data, strategy_type)
            
            # 4. 創建執行結果
            execution = StrategyExecution(
                strategy_type=strategy_type.value,
                pool_id=pool_data["pool_id"],
                chain=pool_data["chain"],
                ranges=ranges,
                total_amount=notional_usd,
                expected_apr=expected_apr,
                risk_score=risk_score,
                execution_time=datetime.now()
            )
            
            logger.info("strategy_executed",
                       strategy=strategy_type.value,
                       pool_id=pool_data["pool_id"],
                       ranges_count=len(ranges),
                       expected_apr=expected_apr)
            
            return execution
            
        except Exception as e:
            logger.error("strategy_execution_failed", 
                        pool_id=pool_data.get("pool_id"), error=str(e))
            raise DyFlowException(f"Strategy execution failed: {e}")
    
    def _select_strategy(self, pool_data: Dict[str, Any]) -> DLMMStrategyType:
        """
        根據池子特徵選擇策略 - PRD 規範映射矩陣
        
        Args:
            pool_data: 池子數據
            
        Returns:
            DLMMStrategyType: 選中的策略類型
        """
        sigma = pool_data.get("sigma", 0.02)  # 波動率
        fee_tvl_pct = pool_data.get("fee_tvl_pct", 0.1)  # 費用/TVL 百分比
        spread = pool_data.get("spread", 0.001)  # 價差
        
        # PRD 策略映射邏輯
        if sigma <= 0.01 and fee_tvl_pct < 0.2:
            # Low σ ≤ 1% + fee<0.2 → SPOT_BALANCED
            return DLMMStrategyType.SPOT_BALANCED
            
        elif 0.01 < sigma <= 0.03:
            # σ 1-3% no dynFee → CURVE_BALANCED
            return DLMMStrategyType.CURVE_BALANCED
            
        elif spread < 0.0005:  # 5 bps = 0.0005
            # Spread<5 bps → BID_ASK_BALANCED
            return DLMMStrategyType.BID_ASK_BALANCED
            
        elif sigma > 0.03 or fee_tvl_pct >= 0.4:
            # σ>3% or fee≥0.4 → SPOT_IMBALANCED_DAMM
            return DLMMStrategyType.SPOT_IMBALANCED
            
        else:
            # 默認使用 SPOT_BALANCED
            return DLMMStrategyType.SPOT_BALANCED
    
    async def _calculate_liquidity_ranges(self, strategy_type: DLMMStrategyType,
                                        pool_data: Dict[str, Any],
                                        notional_usd: float) -> List[LiquidityRange]:
        """
        計算流動性範圍 - 每種策略的具體實現
        
        Args:
            strategy_type: 策略類型
            pool_data: 池子數據
            notional_usd: 投資金額
            
        Returns:
            List[LiquidityRange]: 流動性範圍列表
        """
        current_price = pool_data.get("current_price", 100.0)
        sigma = pool_data.get("sigma", 0.02)
        
        if strategy_type == DLMMStrategyType.SPOT_BALANCED:
            return self._spot_balanced_ranges(current_price, sigma, notional_usd)
            
        elif strategy_type == DLMMStrategyType.CURVE_BALANCED:
            return self._curve_balanced_ranges(current_price, sigma, notional_usd)
            
        elif strategy_type == DLMMStrategyType.BID_ASK_BALANCED:
            return self._bid_ask_balanced_ranges(current_price, sigma, notional_usd)
            
        elif strategy_type == DLMMStrategyType.SPOT_IMBALANCED:
            return self._spot_imbalanced_ranges(current_price, sigma, notional_usd)
            
        else:
            raise DyFlowException(f"Unknown strategy type: {strategy_type}")
    
    def _spot_balanced_ranges(self, current_price: float, sigma: float, 
                            notional_usd: float) -> List[LiquidityRange]:
        """SPOT_BALANCED 策略：對稱流動性分布"""
        ranges = []
        
        # 基於波動率計算價格範圍 (±2σ)
        price_range = current_price * sigma * 2
        min_price = current_price - price_range
        max_price = current_price + price_range
        
        # 分成 5 個均勻範圍
        num_ranges = 5
        range_size = (max_price - min_price) / num_ranges
        amount_per_range = notional_usd / num_ranges
        
        for i in range(num_ranges):
            range_min = min_price + i * range_size
            range_max = min_price + (i + 1) * range_size
            
            ranges.append(LiquidityRange(
                min_price=range_min,
                max_price=range_max,
                amount=amount_per_range,
                weight=1.0 / num_ranges
            ))
        
        return ranges
    
    def _curve_balanced_ranges(self, current_price: float, sigma: float,
                             notional_usd: float) -> List[LiquidityRange]:
        """CURVE_BALANCED 策略：曲線分布，中心集中"""
        ranges = []
        
        # 更寬的價格範圍 (±3σ)
        price_range = current_price * sigma * 3
        min_price = current_price - price_range
        max_price = current_price + price_range
        
        # 7 個範圍，中心權重更高
        num_ranges = 7
        range_size = (max_price - min_price) / num_ranges
        
        # 正態分布權重 (中心高，兩端低)
        weights = [0.05, 0.1, 0.2, 0.3, 0.2, 0.1, 0.05]
        
        for i in range(num_ranges):
            range_min = min_price + i * range_size
            range_max = min_price + (i + 1) * range_size
            amount = notional_usd * weights[i]
            
            ranges.append(LiquidityRange(
                min_price=range_min,
                max_price=range_max,
                amount=amount,
                weight=weights[i]
            ))
        
        return ranges
    
    def _bid_ask_balanced_ranges(self, current_price: float, sigma: float,
                               notional_usd: float) -> List[LiquidityRange]:
        """BID_ASK_BALANCED 策略：買賣價差分布"""
        ranges = []
        
        # 緊密的價格範圍 (±1σ)
        price_range = current_price * sigma
        
        # 買單範圍 (低於當前價格)
        bid_min = current_price - price_range
        bid_max = current_price
        
        # 賣單範圍 (高於當前價格)
        ask_min = current_price
        ask_max = current_price + price_range
        
        # 50/50 分配
        bid_amount = notional_usd * 0.5
        ask_amount = notional_usd * 0.5
        
        ranges.extend([
            LiquidityRange(
                min_price=bid_min,
                max_price=bid_max,
                amount=bid_amount,
                weight=0.5
            ),
            LiquidityRange(
                min_price=ask_min,
                max_price=ask_max,
                amount=ask_amount,
                weight=0.5
            )
        ])
        
        return ranges
    
    def _spot_imbalanced_ranges(self, current_price: float, sigma: float,
                              notional_usd: float) -> List[LiquidityRange]:
        """SPOT_IMBALANCED_DAMM 策略：單邊流動性"""
        ranges = []
        
        # 基於波動率判斷方向 (簡化邏輯)
        # 高波動率通常意味著上漲趨勢，集中在上方
        if sigma > 0.05:
            # 看漲：集中在當前價格上方
            min_price = current_price
            max_price = current_price * (1 + sigma * 2)
        else:
            # 看跌：集中在當前價格下方
            min_price = current_price * (1 - sigma * 2)
            max_price = current_price
        
        # 3 個範圍，遞減分配
        num_ranges = 3
        range_size = (max_price - min_price) / num_ranges
        weights = [0.5, 0.3, 0.2]  # 遞減權重
        
        for i in range(num_ranges):
            range_min = min_price + i * range_size
            range_max = min_price + (i + 1) * range_size
            amount = notional_usd * weights[i]
            
            ranges.append(LiquidityRange(
                min_price=range_min,
                max_price=range_max,
                amount=amount,
                weight=weights[i]
            ))
        
        return ranges
    
    def _calculate_risk_score(self, pool_data: Dict[str, Any], 
                            strategy_type: DLMMStrategyType) -> float:
        """計算風險分數 (0-1, 越高越危險)"""
        sigma = pool_data.get("sigma", 0.02)
        tvl = pool_data.get("tvl", 1000000)
        
        # 基礎風險分數
        base_risk = min(sigma * 10, 1.0)  # 波動率風險
        
        # TVL 風險 (TVL 越低風險越高)
        tvl_risk = max(0, 1 - tvl / 10000000)  # $10M 為基準
        
        # 策略風險調整
        strategy_multipliers = {
            DLMMStrategyType.SPOT_BALANCED: 0.8,
            DLMMStrategyType.CURVE_BALANCED: 1.0,
            DLMMStrategyType.BID_ASK_BALANCED: 1.1,
            DLMMStrategyType.SPOT_IMBALANCED: 1.3
        }
        
        multiplier = strategy_multipliers.get(strategy_type, 1.0)
        
        return min((base_risk + tvl_risk) * multiplier, 1.0)
    
    def _estimate_apr(self, pool_data: Dict[str, Any], 
                     strategy_type: DLMMStrategyType) -> float:
        """估算年化收益率"""
        fee_tvl_pct = pool_data.get("fee_tvl_pct", 0.1)
        
        # 基礎 APR = 費用率 * 365
        base_apr = fee_tvl_pct * 365
        
        # 策略效率調整
        strategy_efficiency = {
            DLMMStrategyType.SPOT_BALANCED: 0.8,
            DLMMStrategyType.CURVE_BALANCED: 0.9,
            DLMMStrategyType.BID_ASK_BALANCED: 1.1,
            DLMMStrategyType.SPOT_IMBALANCED: 1.2
        }
        
        efficiency = strategy_efficiency.get(strategy_type, 1.0)
        
        return base_apr * efficiency
