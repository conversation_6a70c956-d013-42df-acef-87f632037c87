"""
OnboardingAgent - DyFlow v3.4 用戶引導
PRD Section 6 & 7 - Phase 4 Onboarding Agent

負責新用戶引導流程：
1. 接收 QuickScan 結果
2. 生成 LaunchProposal
3. 等待用戶確認 LaunchConfirmed
4. 觸發 Phase 5 解鎖
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

try:
    from agno.agent import Agent
    from agno.models.ollama import Ollama
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass

import structlog

# 嘗試導入工具，如果失敗則使用模擬
try:
    from ..tools.pool_scanner_tool import PoolScannerTool
    from ..tools.meteora_dlmm_tool import MeteoraDLMMTool
    from ..tools.pancake_subgraph_tool import PancakeSubgraphTool
except ImportError:
    # 模擬工具類
    class PoolScannerTool:
        pass
    class MeteoraDLMMTool:
        async def scan_pools(self, max_pools=20, min_tvl=10000):
            return []
    class PancakeSubgraphTool:
        async def scan_pools(self, max_pools=20, min_tvl=10000):
            return []

logger = structlog.get_logger(__name__)

@dataclass
class QuickScanResult:
    """快速掃描結果"""
    chain: str
    pools_found: int
    top_pools: List[Dict[str, Any]]
    scan_duration: float
    timestamp: str

@dataclass
class LaunchProposal:
    """啟動提案"""
    proposal_id: str
    total_pools: int
    recommended_pools: List[Dict[str, Any]]
    estimated_apy: float
    risk_score: float
    capital_requirement: float
    chains: List[str]
    timestamp: str

class OnboardingAgent:
    """
    OnboardingAgent - DyFlow v3.4 核心組件
    負責快速掃描和啟動流程管理
    
    符合 PRD 規範：
    1. QuickScan 功能
    2. LaunchProposal 生成
    3. 啟動確認流程
    4. trading_mode 控制
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.quickscan_chains = self.config.get('quickscan_chains', ['bsc', 'sol'])
        self.max_pools_per_chain = self.config.get('max_pools_per_chain', 20)
        self.min_tvl_threshold = self.config.get('min_tvl_threshold', 10000)
        self.scan_timeout = self.config.get('scan_timeout', 30)
        
        # 初始化工具
        try:
            self.pool_scanner = PoolScannerTool()
            self.meteora_tool = MeteoraDLMMTool({})
            self.pancake_tool = PancakeSubgraphTool({})
        except Exception:
            # 使用模擬工具
            self.pool_scanner = PoolScannerTool()
            self.meteora_tool = MeteoraDLMMTool()
            self.pancake_tool = PancakeSubgraphTool()
        
        # Agno Agent 初始化
        if AGNO_AVAILABLE:
            self.agno_agent = Agent(
                name="OnboardingAgent",
                role="快速掃描和啟動流程管理",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                tools=[self.pool_scanner],
                instructions=[
                    "你是 DyFlow v3.4 的啟動管理專家",
                    "負責快速掃描市場機會並生成啟動提案",
                    "確保掃描結果的準確性和時效性",
                    "根據風險和收益平衡推薦最佳池子組合",
                    "管理整個啟動流程的狀態轉換"
                ],
                show_tool_calls=True,
                add_datetime_to_instructions=True
            )
        
        logger.info("onboarding_agent_initialized",
                   chains=self.quickscan_chains,
                   max_pools=self.max_pools_per_chain)

    async def trigger_quick_scan(self) -> Dict[str, Any]:
        """
        觸發快速掃描 - 符合 PRD 規範
        GET /quickscan 端點處理器
        """
        try:
            logger.info("quick_scan_triggered", chains=self.quickscan_chains)
            
            # 並行掃描所有鏈
            scan_tasks = []
            for chain in self.quickscan_chains:
                task = self._scan_chain_pools(chain)
                scan_tasks.append(task)
            
            # 等待所有掃描完成
            scan_results = await asyncio.gather(*scan_tasks, return_exceptions=True)
            
            # 處理掃描結果
            valid_results = []
            for i, result in enumerate(scan_results):
                if isinstance(result, Exception):
                    logger.error("chain_scan_failed", 
                               chain=self.quickscan_chains[i], 
                               error=str(result))
                else:
                    valid_results.append(result)
            
            # 生成啟動提案
            proposal = await self._build_launch_proposal(valid_results)
            
            logger.info("quick_scan_completed",
                       total_pools=proposal.total_pools,
                       recommended_count=len(proposal.recommended_pools))
            
            return {
                "scan_results": [asdict(r) for r in valid_results],
                "launch_proposal": asdict(proposal),
                "status": "success",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("quick_scan_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def _scan_chain_pools(self, chain: str) -> QuickScanResult:
        """掃描單個鏈的池子"""
        start_time = datetime.utcnow()
        
        try:
            if chain == "sol":
                # Solana Meteora DLMM 掃描
                pools = await self.meteora_tool.scan_pools(
                    max_pools=self.max_pools_per_chain,
                    min_tvl=self.min_tvl_threshold
                )
            elif chain == "bsc":
                # BSC PancakeSwap V3 掃描
                pools = await self.pancake_tool.scan_pools(
                    max_pools=self.max_pools_per_chain,
                    min_tvl=self.min_tvl_threshold
                )
            else:
                raise ValueError(f"Unsupported chain: {chain}")
            
            # 選擇 top 池子
            top_pools = sorted(pools, key=lambda x: x.get('tvl_usd', 0), reverse=True)
            top_pools = top_pools[:10]  # 取前 10 個
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return QuickScanResult(
                chain=chain,
                pools_found=len(pools),
                top_pools=top_pools,
                scan_duration=duration,
                timestamp=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            logger.error("chain_scan_error", chain=chain, error=str(e))
            raise

    async def _build_launch_proposal(self, scan_results: List[QuickScanResult]) -> LaunchProposal:
        """構建啟動提案 - 符合 PRD 規範"""
        try:
            # 收集所有池子
            all_pools = []
            total_pools = 0
            chains = []
            
            for result in scan_results:
                all_pools.extend(result.top_pools)
                total_pools += result.pools_found
                chains.append(result.chain)
            
            # 選擇推薦池子 (基於 TVL 和收益率)
            recommended_pools = self._select_recommended_pools(all_pools)
            
            # 計算預估指標
            estimated_apy = self._calculate_estimated_apy(recommended_pools)
            risk_score = self._calculate_risk_score(recommended_pools)
            capital_requirement = self._calculate_capital_requirement(recommended_pools)
            
            proposal = LaunchProposal(
                proposal_id=f"proposal_{int(datetime.utcnow().timestamp())}",
                total_pools=total_pools,
                recommended_pools=recommended_pools,
                estimated_apy=estimated_apy,
                risk_score=risk_score,
                capital_requirement=capital_requirement,
                chains=chains,
                timestamp=datetime.utcnow().isoformat()
            )
            
            logger.info("launch_proposal_generated",
                       proposal_id=proposal.proposal_id,
                       recommended_count=len(recommended_pools),
                       estimated_apy=estimated_apy)
            
            return proposal
            
        except Exception as e:
            logger.error("proposal_generation_failed", error=str(e))
            raise

    def _select_recommended_pools(self, pools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """選擇推薦池子"""
        # 按照 TVL 和手續費率排序
        scored_pools = []
        
        for pool in pools:
            tvl = pool.get('tvl_usd', 0)
            fee_rate = pool.get('fee_tvl_pct', 0)
            
            # 簡單評分算法
            score = (tvl / 1000000) * 0.6 + (fee_rate * 100) * 0.4
            
            scored_pools.append({
                **pool,
                'score': score
            })
        
        # 排序並取前 5 個
        scored_pools.sort(key=lambda x: x['score'], reverse=True)
        return scored_pools[:5]

    def _calculate_estimated_apy(self, pools: List[Dict[str, Any]]) -> float:
        """計算預估 APY"""
        if not pools:
            return 0.0
        
        total_apy = sum(pool.get('fee_tvl_pct', 0) * 365 for pool in pools)
        return total_apy / len(pools)

    def _calculate_risk_score(self, pools: List[Dict[str, Any]]) -> float:
        """計算風險分數 (0-100)"""
        if not pools:
            return 50.0
        
        # 基於波動率和 TVL 計算風險
        total_risk = 0
        for pool in pools:
            sigma = pool.get('sigma', 0.02)
            tvl = pool.get('tvl_usd', 0)
            
            # TVL 越高風險越低，波動率越高風險越高
            risk = sigma * 100 - (tvl / 1000000) * 5
            total_risk += max(0, min(100, risk))
        
        return total_risk / len(pools)

    def _calculate_capital_requirement(self, pools: List[Dict[str, Any]]) -> float:
        """計算資本需求"""
        # 每個池子建議投入 $5000
        return len(pools) * 5000.0

    async def launch_confirmed(self, confirmation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        啟動確認處理 - 符合 PRD 規範
        POST /confirm 端點處理器
        """
        try:
            proposal_id = confirmation_data.get('proposal_id')
            confirmed_pools = confirmation_data.get('confirmed_pools', [])
            
            logger.info("launch_confirmation_received",
                       proposal_id=proposal_id,
                       pools_count=len(confirmed_pools))
            
            # 更新全局狀態為 active
            await self._set_trading_mode("active")
            
            # 啟用 intel 節點
            await self._enable_intel_node()
            
            # 發佈啟動事件
            await self._publish_launch_event(proposal_id, confirmed_pools)
            
            return {
                "status": "confirmed",
                "proposal_id": proposal_id,
                "trading_mode": "active",
                "pools_activated": len(confirmed_pools),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("launch_confirmation_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def _set_trading_mode(self, mode: str):
        """設置交易模式"""
        # 這裡應該更新 Agno 全局狀態
        logger.info("trading_mode_updated", mode=mode)

    async def _enable_intel_node(self):
        """啟用 intel 節點"""
        # 這裡應該通過 Agno 啟用 MarketIntelAgent
        logger.info("intel_node_enabled")

    async def _publish_launch_event(self, proposal_id: str, pools: List[Dict[str, Any]]):
        """發佈啟動事件"""
        event = {
            "event_type": "launch_confirmed",
            "proposal_id": proposal_id,
            "pools": pools,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 這裡應該通過 Agno 發佈到 bus.launch
        logger.info("launch_event_published", proposal_id=proposal_id)
