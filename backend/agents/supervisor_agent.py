"""
SupervisorAgent - DyFlow v3.4 監督代理
PRD Section 4 - Startup Phases (Guard-Rail)

負責：
1. 8-Phase 啟動序列管理
2. 階段門控和解鎖邏輯
3. 失敗處理和降級模式
4. 系統狀態協調
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from enum import Enum
import json

# Agno Framework imports
try:
    from agno import Agent, Context
except ImportError:
    class Agent:
        pass
    class Context:
        pass

logger = structlog.get_logger(__name__)

class Phase(Enum):
    """啟動階段枚舉"""
    CONFIG = 0      # Config + Vault OK
    HEALTH = 1      # 5 targets latency ≤ 300 ms, score ≥ 0.9
    UI = 2          # Core → /health 200
    WALLET = 3      # ≥ 1 keystore decrypt OK
    ONBOARDING = 4  # OnboardingAgent 完成 LaunchConfirmed
    INTEL = 5       # bus.pool emits ≥ 1 event
    PORTFOLIO = 6   # NAV lock acquired
    STRATEGY_EXEC = 7  # 首 Tx 成功
    RISK_FEE = 8    # IL_net & VaR within limits

class SystemMode(Enum):
    """系統模式枚舉"""
    STARTING = "starting"
    ACTIVE = "active"
    DEGRADED = "degraded"
    EXIT_ONLY = "exit_only"
    FAILED = "failed"

class SupervisorAgent(Agent):
    """
    SupervisorAgent - 監督代理 v3.4
    
    PRD Section 4 職責：
    1. 管理 8-Phase 啟動序列
    2. 階段門控：上一 Phase 未完成，不解鎖下一 Node
    3. 失敗處理：三次連敗自動 exit_only
    4. 系統狀態協調和監控
    """
    
    def __init__(self, config: Dict[str, Any]):
        # super().__init__()  # 暫時註釋，避免初始化問題
        self.config = config
        self.name = "SupervisorAgent"
        
        # 階段管理
        self.current_phase = Phase.CONFIG
        self.phase_status = {phase: False for phase in Phase}
        self.phase_attempts = {phase: 0 for phase in Phase}
        self.max_attempts = config.get('max_phase_attempts', 3)
        
        # 系統狀態
        self.system_mode = SystemMode.STARTING
        self.failure_count = 0
        self.max_failures = 3
        
        # 階段成功標準
        self.phase_criteria = {
            Phase.CONFIG: self._check_config_phase,
            Phase.HEALTH: self._check_health_phase,
            Phase.UI: self._check_ui_phase,
            Phase.WALLET: self._check_wallet_phase,
            Phase.ONBOARDING: self._check_onboarding_phase,
            Phase.INTEL: self._check_intel_phase,
            Phase.PORTFOLIO: self._check_portfolio_phase,
            Phase.STRATEGY_EXEC: self._check_strategy_exec_phase,
            Phase.RISK_FEE: self._check_risk_fee_phase
        }
        
        # 監控數據
        self.agent_status = {}
        self.system_health = {}
        self.startup_log = []
        
        # Agno 上下文
        self.ctx = None
        
        # 運行狀態
        self.is_running = False
        
        logger.info("supervisor_agent_v34_initialized",
                   current_phase=self.current_phase.name,
                   system_mode=self.system_mode.value)
    
    def set_context(self, ctx: Context):
        """設置 Agno 上下文"""
        self.ctx = ctx
    
    async def start_supervision(self):
        """開始監督流程"""
        self.is_running = True
        logger.info("supervisor_started", phase=self.current_phase.name)
        
        # 記錄啟動
        self._log_startup_event("supervisor_started", {
            "initial_phase": self.current_phase.name,
            "system_mode": self.system_mode.value
        })
        
        # 開始階段推進循環
        await self._phase_progression_loop()
    
    async def stop_supervision(self):
        """停止監督"""
        self.is_running = False
        logger.info("supervisor_stopped")
    
    async def _phase_progression_loop(self):
        """階段推進主循環"""
        while self.is_running and self.current_phase != Phase.RISK_FEE:
            try:
                # 檢查當前階段是否完成
                if await self._check_current_phase():
                    # 階段成功，推進到下一階段
                    await self._advance_to_next_phase()
                else:
                    # 階段失敗，處理失敗邏輯
                    await self._handle_phase_failure()
                
                # 等待一段時間再檢查
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error("phase_progression_error", error=str(e))
                await asyncio.sleep(10)
        
        # 如果到達最終階段，切換到 ACTIVE 模式
        if self.current_phase == Phase.RISK_FEE and self.phase_status[Phase.RISK_FEE]:
            await self._enter_active_mode()
    
    async def _check_current_phase(self) -> bool:
        """檢查當前階段是否完成"""
        if self.current_phase in self.phase_criteria:
            try:
                success = await self.phase_criteria[self.current_phase]()
                self.phase_status[self.current_phase] = success
                
                if success:
                    logger.info("phase_check_passed", phase=self.current_phase.name)
                    self._log_startup_event("phase_completed", {
                        "phase": self.current_phase.name,
                        "attempts": self.phase_attempts[self.current_phase] + 1
                    })
                else:
                    logger.warning("phase_check_failed", phase=self.current_phase.name)
                
                return success
                
            except Exception as e:
                logger.error("phase_check_error", phase=self.current_phase.name, error=str(e))
                return False
        
        return False
    
    async def _advance_to_next_phase(self):
        """推進到下一階段"""
        old_phase = self.current_phase
        
        # 獲取下一階段
        phase_values = list(Phase)
        current_index = phase_values.index(self.current_phase)
        
        if current_index < len(phase_values) - 1:
            self.current_phase = phase_values[current_index + 1]
            self.phase_attempts[self.current_phase] = 0
            
            logger.info("phase_advanced", 
                       old_phase=old_phase.name,
                       new_phase=self.current_phase.name)
            
            # 發佈階段變更事件
            if self.ctx:
                await self.ctx.publish("supervisor.phase_changed", {
                    "old_phase": old_phase.value,
                    "new_phase": self.current_phase.value,
                    "timestamp": datetime.now(timezone.utc).timestamp()
                })
    
    async def _handle_phase_failure(self):
        """處理階段失敗"""
        self.phase_attempts[self.current_phase] += 1
        
        logger.warning("phase_attempt_failed",
                      phase=self.current_phase.name,
                      attempt=self.phase_attempts[self.current_phase],
                      max_attempts=self.max_attempts)
        
        if self.phase_attempts[self.current_phase] >= self.max_attempts:
            # 達到最大嘗試次數
            self.failure_count += 1
            
            logger.error("phase_max_attempts_reached",
                        phase=self.current_phase.name,
                        failure_count=self.failure_count)
            
            if self.failure_count >= self.max_failures:
                # 三次連敗，進入 exit_only 模式
                await self._enter_exit_only_mode()
            else:
                # 進入降級模式
                await self._enter_degraded_mode()
    
    async def _enter_active_mode(self):
        """進入活躍模式"""
        self.system_mode = SystemMode.ACTIVE
        
        logger.info("system_entered_active_mode")
        
        self._log_startup_event("system_active", {
            "total_phases": len(Phase),
            "startup_duration": self._calculate_startup_duration()
        })
        
        if self.ctx:
            await self.ctx.publish("supervisor.system_active", {
                "mode": self.system_mode.value,
                "timestamp": datetime.now(timezone.utc).timestamp()
            })
    
    async def _enter_degraded_mode(self):
        """進入降級模式"""
        self.system_mode = SystemMode.DEGRADED
        
        logger.warning("system_entered_degraded_mode",
                      failed_phase=self.current_phase.name,
                      failure_count=self.failure_count)
        
        # 發送運維告警
        await self._send_ops_alert("degraded", {
            "failed_phase": self.current_phase.name,
            "failure_count": self.failure_count
        })
    
    async def _enter_exit_only_mode(self):
        """進入僅退出模式"""
        self.system_mode = SystemMode.EXIT_ONLY
        
        logger.critical("system_entered_exit_only_mode",
                       failure_count=self.failure_count)
        
        # 發送緊急告警
        await self._send_ops_alert("exit_only", {
            "failure_count": self.failure_count,
            "failed_phase": self.current_phase.name
        })
        
        if self.ctx:
            await self.ctx.publish("supervisor.emergency_exit", {
                "mode": self.system_mode.value,
                "reason": "max_failures_reached",
                "timestamp": datetime.now(timezone.utc).timestamp()
            })
    
    async def _send_ops_alert(self, alert_type: str, data: Dict[str, Any]):
        """發送運維告警"""
        alert = {
            "type": alert_type,
            "system": "DyFlow v3.4",
            "data": data,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # 這裡應該整合實際的告警系統 (Slack, Email 等)
        logger.critical("ops_alert", **alert)
    
    def _log_startup_event(self, event_type: str, data: Dict[str, Any]):
        """記錄啟動事件"""
        event = {
            "event_type": event_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data
        }
        self.startup_log.append(event)
    
    def _calculate_startup_duration(self) -> float:
        """計算啟動持續時間"""
        if not self.startup_log:
            return 0.0
        
        start_time = datetime.fromisoformat(self.startup_log[0]["timestamp"])
        end_time = datetime.now(timezone.utc)
        return (end_time - start_time).total_seconds()
    
    # 階段檢查方法 (簡化實現)
    async def _check_config_phase(self) -> bool:
        """檢查配置階段"""
        # 檢查配置和 Vault
        return True  # 簡化實現
    
    async def _check_health_phase(self) -> bool:
        """檢查健康階段"""
        # 檢查 5 個目標延遲 ≤ 300ms，分數 ≥ 0.9
        return True  # 簡化實現
    
    async def _check_ui_phase(self) -> bool:
        """檢查 UI 階段"""
        # 檢查 Core → /health 200
        return True  # 簡化實現
    
    async def _check_wallet_phase(self) -> bool:
        """檢查錢包階段"""
        # 檢查 ≥ 1 keystore decrypt OK
        return True  # 簡化實現
    
    async def _check_onboarding_phase(self) -> bool:
        """檢查引導階段"""
        # 檢查 OnboardingAgent 完成 LaunchConfirmed
        return True  # 簡化實現
    
    async def _check_intel_phase(self) -> bool:
        """檢查情報階段"""
        # 檢查 bus.pool emits ≥ 1 event
        return True  # 簡化實現
    
    async def _check_portfolio_phase(self) -> bool:
        """檢查投資組合階段"""
        # 檢查 NAV lock acquired
        return True  # 簡化實現
    
    async def _check_strategy_exec_phase(self) -> bool:
        """檢查策略執行階段"""
        # 檢查首 Tx 成功
        return True  # 簡化實現
    
    async def _check_risk_fee_phase(self) -> bool:
        """檢查風險費用階段"""
        # 檢查 IL_net & VaR within limits
        return True  # 簡化實現
    
    def get_metrics(self) -> Dict[str, Any]:
        """獲取監督指標"""
        return {
            'supervisor_current_phase': self.current_phase.value,
            'supervisor_system_mode': self.system_mode.value,
            'supervisor_failure_count': self.failure_count,
            'supervisor_phase_status': {p.name: status for p, status in self.phase_status.items()},
            'supervisor_running': self.is_running
        }
