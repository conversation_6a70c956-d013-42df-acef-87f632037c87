#!/usr/bin/env python3
"""
DyFlow v3.3 ExecutionAgent
負責執行 LP 策略和處理 ExitRequest 事件
支援 swap_to_exit_asset 功能
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import structlog

logger = structlog.get_logger(__name__)

@dataclass
class ExecutionTask:
    """執行任務"""
    task_id: str
    task_type: str  # 'close_position', 'open_position', 'rebalance', 'emergency_exit', 'exit_request'
    chain: str
    parameters: Dict[str, Any]
    priority: str  # 'low', 'medium', 'high', 'critical'
    status: str  # 'pending', 'executing', 'completed', 'failed'
    created_at: str
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@dataclass
class LPPlan:
    """LP 計劃"""
    plan_id: str
    pool_id: str
    strategy: str
    k: float
    notional_usd: float
    risk_profile: Dict[str, Any]
    status: str = 'pending'

@dataclass
class ExitRequest:
    """退出請求"""
    position_id: str
    exit_asset: str
    reason: str
    timestamp: str

class ExecutionAgent:
    """
    DyFlow v3.4 ExecutionAgent - 負責執行 LP 策略和處理退出請求
    符合 PRD 規範的狀態機實現
    """

    # 狀態機定義 - 符合 v3.4 PRD 規範
    class State:
        IDLE = "Idle"
        SIGN = "Sign"
        BROADCAST = "Broadcast"
        CONFIRMED = "Confirmed"
        FAILED = "Failed"
        EXIT_SWAP = "ExitSwap"
        EXIT_BROADCAST = "ExitBroadcast"
        EXIT_CONFIRMED = "ExitConfirmed"

    def __init__(self):
        self.name = "ExecutionAgent"
        self.task_queue: List[ExecutionTask] = []
        self.execution_history: List[ExecutionTask] = []
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        self.lp_plans: Dict[str, LPPlan] = {}

        # 狀態機初始化 - 符合 PRD 規範
        self.current_state = self.State.IDLE
        self.state_history = []
        self.retry_count = 0
        self.max_retries = 3
        self.current_task_id = None

        # 執行配置
        self.config = {
            "max_slippage": 0.01,  # 1%
            "gas_price_multiplier": 1.2,  # Gas 價格倍數
            "confirmation_blocks": 3,  # 確認區塊數
            "retry_attempts": 3,  # 重試次數
            "timeout_seconds": 300,  # 超時時間
            "jupiter_api": "https://quote-api.jup.ag/v6",  # Jupiter API for Solana
            "pancake_router": "0x13f4EA83D0bd40E75C8222255bc855a974568Dd4"  # PancakeSwap Router
        }

        logger.info("dyflow_v34_execution_agent_initialized",
                   initial_state=self.current_state)

    def transition_state(self, new_state: str, reason: str = None):
        """
        狀態轉換 - 符合 PRD 狀態機規範
        """
        old_state = self.current_state
        self.current_state = new_state

        # 記錄狀態歷史
        self.state_history.append({
            "from": old_state,
            "to": new_state,
            "reason": reason,
            "timestamp": datetime.now().isoformat(),
            "task_id": self.current_task_id
        })

        logger.info("state_transition",
                   from_state=old_state,
                   to_state=new_state,
                   reason=reason,
                   task_id=self.current_task_id)

        # 發佈狀態變更事件
        self._emit_task_state_changed(old_state, new_state, reason)

    def _emit_task_state_changed(self, old_state: str, new_state: str, reason: str = None):
        """發佈任務狀態變更事件"""
        event = {
            "event_type": "task_state_changed",
            "task_id": self.current_task_id or "unknown",
            "agent_id": "ExecutionAgent",
            "old_state": old_state,
            "new_state": new_state,
            "transition_reason": reason,
            "timestamp": datetime.now().isoformat()
        }

        # 這裡應該通過 Agno 發佈事件
        logger.info("task_state_changed_event", **event)

        # 發佈到 WebSocket (如果有上下文)
        if hasattr(self, 'ctx') and self.ctx:
            asyncio.create_task(self._publish_task_state_changed(old_state, new_state, reason))

    async def _publish_task_state_changed(self, old_state: str, new_state: str, reason: str = None):
        """發佈任務狀態變更到 WebSocket"""
        try:
            if hasattr(self, 'ctx') and self.ctx:
                await self.ctx.publish("task_state_changed", {
                    "agent": "ExecutionAgent",
                    "state": new_state,
                    "task": reason or f"State transition: {old_state} → {new_state}",
                    "ts": datetime.now().timestamp()
                })
        except Exception as e:
            logger.error("failed_to_publish_task_state", error=str(e))

    async def reply_ack(self, status: str = "ACK"):
        """回覆 ACK/NACK 狀態"""
        try:
            if hasattr(self, 'ctx') and self.ctx:
                await self.ctx.reply(status=status)

                # 同時發佈 agent_task_ack 事件
                await self.ctx.publish("agent_task_ack", {
                    "agent": "ExecutionAgent",
                    "status": status,
                    "timestamp": datetime.now().timestamp()
                })

                logger.info("execution_agent_ack_sent", status=status)
        except Exception as e:
            logger.error("failed_to_send_ack", error=str(e), status=status)

    def set_context(self, ctx):
        """設置 Agno 上下文"""
        self.ctx = ctx

    def can_transition(self, from_state: str, to_state: str, trigger: str = None) -> bool:
        """
        檢查狀態轉換是否有效 - 符合 PRD 狀態機規範
        """
        valid_transitions = {
            self.State.IDLE: [self.State.SIGN, self.State.EXIT_SWAP],
            self.State.SIGN: [self.State.BROADCAST, self.State.FAILED],
            self.State.BROADCAST: [self.State.CONFIRMED, self.State.FAILED],
            self.State.CONFIRMED: [self.State.IDLE],
            self.State.FAILED: [self.State.IDLE],
            self.State.EXIT_SWAP: [self.State.EXIT_BROADCAST, self.State.FAILED],
            self.State.EXIT_BROADCAST: [self.State.EXIT_CONFIRMED, self.State.FAILED],
            self.State.EXIT_CONFIRMED: [self.State.IDLE]
        }

        return to_state in valid_transitions.get(from_state, [])
    
    async def parse_user_command(self, command: str) -> List[ExecutionTask]:
        """解析用户指令并生成执行任务"""
        tasks = []
        command_lower = command.lower()
        timestamp = datetime.now().isoformat()
        
        if '出清' in command_lower or '清仓' in command_lower:
            # 出清所有持仓
            tasks.extend(await self._create_close_all_tasks(timestamp))
            
        elif '关闭' in command_lower and ('bnb/usdc' in command_lower or 'bnb/usdt' in command_lower):
            # 关闭特定BSC持仓
            pair = 'BNB/USDC' if 'bnb/usdc' in command_lower else 'BNB/USDT'
            tasks.append(await self._create_close_position_task('bsc', pair, timestamp))
            
        elif '关闭' in command_lower and 'sol/usdc' in command_lower:
            # 关闭SOL持仓
            tasks.append(await self._create_close_position_task('solana', 'SOL/USDC', timestamp))
            
        elif '增加' in command_lower or '开仓' in command_lower:
            # 开新仓
            if 'bnb/usdc' in command_lower:
                tasks.append(await self._create_open_position_task('bsc', 'BNB/USDC', command, timestamp))
            elif 'bnb/usdt' in command_lower:
                tasks.append(await self._create_open_position_task('bsc', 'BNB/USDT', command, timestamp))
            elif 'sol/usdc' in command_lower:
                tasks.append(await self._create_open_position_task('solana', 'SOL/USDC', command, timestamp))
        
        elif '紧急' in command_lower or '急停' in command_lower:
            # 紧急停止
            tasks.extend(await self._create_emergency_exit_tasks(timestamp))
        
        return tasks

    async def handle_lp_plan(self, lp_plan: LPPlan) -> bool:
        """
        處理 LP 計劃 - bus.lpplan 事件
        使用狀態機管理執行流程
        """
        logger.info("handling_lp_plan", plan_id=lp_plan.plan_id, strategy=lp_plan.strategy)

        # 設置當前任務 ID
        self.current_task_id = f"lp_plan_{lp_plan.plan_id}"

        # 狀態轉換: Idle → Sign
        if not self.can_transition(self.current_state, self.State.SIGN):
            logger.error("invalid_state_transition",
                        current_state=self.current_state,
                        target_state=self.State.SIGN)
            return False

        self.transition_state(self.State.SIGN, "on_lpplan")

        # 儲存 LP 計劃
        self.lp_plans[lp_plan.plan_id] = lp_plan

        try:
            # 簽名階段
            sign_result = await self._execute_signing(lp_plan)

            if sign_result["success"]:
                # 狀態轉換: Sign → Broadcast
                self.transition_state(self.State.BROADCAST, "sign_ok")

                # 廣播階段
                broadcast_result = await self._execute_broadcast(lp_plan, sign_result)

                if broadcast_result["success"]:
                    # 狀態轉換: Broadcast → Confirmed
                    self.transition_state(self.State.CONFIRMED, "receipt_ok")

                    lp_plan.status = 'executed'
                    # 發佈 bus.tx 事件
                    await self._publish_tx_event(broadcast_result, "open")

                    # 回到 Idle 狀態
                    self.transition_state(self.State.IDLE, "task_completed")
                    return True
                else:
                    # 廣播失敗，檢查重試
                    if self.retry_count < self.max_retries:
                        self.retry_count += 1
                        logger.info("retrying_broadcast", retry_count=self.retry_count)
                        return await self.handle_lp_plan(lp_plan)  # 重試
                    else:
                        # 狀態轉換: Broadcast → Failed
                        self.transition_state(self.State.FAILED, "receipt_fail_3x")
            else:
                # 狀態轉換: Sign → Failed
                self.transition_state(self.State.FAILED, "sign_err")

        except Exception as e:
            logger.error("lp_plan_execution_failed", error=str(e))
            self.transition_state(self.State.FAILED, f"exception: {str(e)}")

        # 失敗處理
        lp_plan.status = 'failed'
        self.transition_state(self.State.IDLE, "task_failed")
        self.retry_count = 0  # 重置重試計數
        return False

    async def _execute_signing(self, lp_plan: LPPlan) -> Dict[str, Any]:
        """執行簽名階段 - 符合狀態機規範"""
        logger.info("executing_signing", plan_id=lp_plan.plan_id)

        try:
            # 模擬簽名過程
            await asyncio.sleep(1)  # 模擬簽名時間

            # 模擬簽名結果
            return {
                "success": True,
                "signed_tx": f"signed_tx_{lp_plan.plan_id}",
                "signature": f"sig_{lp_plan.plan_id}",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("signing_failed", plan_id=lp_plan.plan_id, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_broadcast(self, lp_plan: LPPlan, sign_result: Dict[str, Any]) -> Dict[str, Any]:
        """執行廣播階段 - 符合狀態機規範"""
        logger.info("executing_broadcast", plan_id=lp_plan.plan_id)

        try:
            # 模擬廣播過程
            await asyncio.sleep(2)  # 模擬廣播時間

            # 模擬廣播結果
            return {
                "success": True,
                "transaction_hash": f"tx_hash_{lp_plan.plan_id}",
                "position_id": f"pos_{lp_plan.plan_id}",
                "status": "open",
                "chain": "solana" if "SOL" in lp_plan.pool_id else "bsc",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("broadcast_failed", plan_id=lp_plan.plan_id, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }

    async def handle_exit_request(self, exit_request: ExitRequest) -> bool:
        """處理退出請求 - ExitRequest 事件"""
        logger.info("handling_exit_request",
                   position_id=exit_request.position_id,
                   reason=exit_request.reason,
                   exit_asset=exit_request.exit_asset)

        # 檢查持倉是否存在
        if exit_request.position_id not in self.active_positions:
            logger.error("position_not_found", position_id=exit_request.position_id)
            return False

        position = self.active_positions[exit_request.position_id]

        # 創建退出任務
        task = ExecutionTask(
            task_id=f"exit_{exit_request.position_id}",
            task_type="exit_request",
            chain=position.get("chain", "solana"),
            parameters={
                "position_id": exit_request.position_id,
                "exit_asset": exit_request.exit_asset,
                "reason": exit_request.reason,
                "position_data": position
            },
            priority="high",
            status="pending",
            created_at=datetime.now().isoformat()
        )

        self.add_task(task)

        # 執行退出
        success = await self.execute_task(task)

        if success:
            # 移除活躍持倉
            del self.active_positions[exit_request.position_id]
            # 發佈 bus.tx 事件
            await self._publish_tx_event(task.result, "closed")

        return success
    
    async def _create_close_all_tasks(self, timestamp: str) -> List[ExecutionTask]:
        """创建关闭所有持仓的任务"""
        tasks = []
        
        # 模拟当前持仓
        current_positions = [
            {"chain": "bsc", "pair": "BNB/USDC", "pool_address": "0xabc123"},
            {"chain": "bsc", "pair": "BNB/USDT", "pool_address": "0xdef456"},
            {"chain": "solana", "pair": "SOL/USDC", "pool_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"}
        ]
        
        for i, position in enumerate(current_positions):
            task = ExecutionTask(
                task_id=f"close_all_{i}_{int(datetime.now().timestamp())}",
                task_type="close_position",
                chain=position["chain"],
                parameters={
                    "pair": position["pair"],
                    "pool_address": position["pool_address"],
                    "exit_token": "BNB" if position["chain"] == "bsc" else "SOL",
                    "close_percentage": 100,  # 100%关闭
                    "max_slippage": self.config["max_slippage"]
                },
                priority="high",
                status="pending",
                created_at=timestamp
            )
            tasks.append(task)
        
        return tasks
    
    async def _create_close_position_task(self, chain: str, pair: str, timestamp: str) -> ExecutionTask:
        """创建关闭特定持仓的任务"""
        return ExecutionTask(
            task_id=f"close_{chain}_{pair.replace('/', '_')}_{int(datetime.now().timestamp())}",
            task_type="close_position",
            chain=chain,
            parameters={
                "pair": pair,
                "exit_token": "BNB" if chain == "bsc" else "SOL",
                "close_percentage": 100,
                "max_slippage": self.config["max_slippage"]
            },
            priority="medium",
            status="pending",
            created_at=timestamp
        )
    
    async def _create_open_position_task(self, chain: str, pair: str, command: str, timestamp: str) -> ExecutionTask:
        """创建开仓任务"""
        # 从指令中提取金额
        amount = 5000  # 默认金额
        if '$' in command:
            try:
                amount_str = command.split('$')[1].split()[0].replace(',', '')
                amount = float(amount_str)
            except:
                pass
        
        return ExecutionTask(
            task_id=f"open_{chain}_{pair.replace('/', '_')}_{int(datetime.now().timestamp())}",
            task_type="open_position",
            chain=chain,
            parameters={
                "pair": pair,
                "amount_usd": amount,
                "max_slippage": self.config["max_slippage"],
                "price_range": "auto"  # 自动价格范围
            },
            priority="low",
            status="pending",
            created_at=timestamp
        )
    
    async def _create_emergency_exit_tasks(self, timestamp: str) -> List[ExecutionTask]:
        """创建紧急退出任务"""
        tasks = await self._create_close_all_tasks(timestamp)
        
        # 设置为最高优先级
        for task in tasks:
            task.priority = "critical"
            task.task_type = "emergency_exit"
            task.parameters["emergency"] = True
            task.parameters["max_slippage"] = 0.05  # 紧急情况下允许更高滑点
        
        return tasks
    
    async def execute_task(self, task: ExecutionTask) -> bool:
        """执行单个任务"""
        logger.info("executing_task", task_id=task.task_id, task_type=task.task_type)
        
        task.status = "executing"
        
        try:
            if task.task_type in ["close_position", "emergency_exit"]:
                result = await self._execute_close_position(task)
            elif task.task_type == "open_position":
                result = await self._execute_open_position(task)
            elif task.task_type == "exit_request":
                result = await self._execute_exit_request(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            task.status = "completed"
            task.completed_at = datetime.now().isoformat()
            task.result = result
            
            logger.info("task_completed", task_id=task.task_id, result=result)
            return True
            
        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            task.completed_at = datetime.now().isoformat()
            
            logger.error("task_failed", task_id=task.task_id, error=str(e))
            return False
    
    async def _execute_close_position(self, task: ExecutionTask) -> Dict[str, Any]:
        """执行关闭持仓"""
        params = task.parameters
        chain = task.chain
        pair = params["pair"]
        exit_token = params["exit_token"]
        
        # 模拟执行步骤
        steps = [
            f"连接到{chain.upper()}网络",
            f"获取{pair}池子信息",
            "计算最优退出路径",
            f"移除流动性",
            f"将代币换为{exit_token}",
            "确认交易"
        ]
        
        # 模拟执行时间
        for step in steps:
            logger.info("execution_step", step=step, task_id=task.task_id)
            await asyncio.sleep(1)  # 模拟执行时间
        
        # 模拟执行结果
        result = {
            "transaction_hash": f"0x{'a' * 64}" if chain == "bsc" else f"{'b' * 88}",
            "exit_amount": 8500.00 if pair == "BNB/USDC" else 6200.00 if pair == "BNB/USDT" else 12000.00,
            "exit_token": exit_token,
            "exit_token_amount": 12.5 if exit_token == "BNB" else 85.5,
            "gas_used": 0.002 if chain == "bsc" else 0.000005,
            "slippage": 0.008,  # 0.8%
            "execution_time": "45s"
        }
        
        return result
    
    async def _execute_open_position(self, task: ExecutionTask) -> Dict[str, Any]:
        """执行开仓"""
        params = task.parameters
        chain = task.chain
        pair = params["pair"]
        amount = params["amount_usd"]
        
        # 模拟执行步骤
        steps = [
            f"连接到{chain.upper()}网络",
            f"分析{pair}池子",
            "计算最优价格范围",
            "准备代币",
            "添加流动性",
            "确认交易"
        ]
        
        for step in steps:
            logger.info("execution_step", step=step, task_id=task.task_id)
            await asyncio.sleep(1)
        
        # 模拟执行结果
        result = {
            "transaction_hash": f"0x{'c' * 64}" if chain == "bsc" else f"{'d' * 88}",
            "position_value": amount,
            "token0_amount": amount / 2 / 680 if "BNB" in pair else amount / 2 / 140,  # 模拟价格
            "token1_amount": amount / 2,
            "price_range": {"min": 650, "max": 710} if "BNB" in pair else {"min": 130, "max": 150},
            "gas_used": 0.003 if chain == "bsc" else 0.000008,
            "execution_time": "60s"
        }
        
        return result
    
    async def process_task_queue(self):
        """处理任务队列"""
        if not self.task_queue:
            return
        
        # 按优先级排序
        priority_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        self.task_queue.sort(key=lambda x: priority_order.get(x.priority, 4))
        
        # 执行任务
        for task in self.task_queue[:]:  # 复制列表避免修改问题
            if task.status == "pending":
                success = await self.execute_task(task)
                
                # 移动到历史记录
                self.execution_history.append(task)
                self.task_queue.remove(task)
                
                if not success and task.priority == "critical":
                    logger.error("critical_task_failed", task_id=task.task_id)
                    # 关键任务失败时的处理逻辑
    
    def add_task(self, task: ExecutionTask):
        """添加任务到队列"""
        self.task_queue.append(task)
        logger.info("task_added", task_id=task.task_id, task_type=task.task_type)
    
    def get_execution_status(self) -> Dict[str, Any]:
        """获取执行状态"""
        pending_tasks = [t for t in self.task_queue if t.status == "pending"]
        executing_tasks = [t for t in self.task_queue if t.status == "executing"]
        
        recent_completed = [t for t in self.execution_history[-10:] if t.status == "completed"]
        recent_failed = [t for t in self.execution_history[-10:] if t.status == "failed"]
        
        return {
            "pending_tasks": len(pending_tasks),
            "executing_tasks": len(executing_tasks),
            "recent_completed": len(recent_completed),
            "recent_failed": len(recent_failed),
            "queue_details": [
                {
                    "task_id": t.task_id,
                    "task_type": t.task_type,
                    "chain": t.chain,
                    "priority": t.priority,
                    "status": t.status
                } for t in self.task_queue
            ]
        }
    
    async def execute_user_command(self, command: str) -> Dict[str, Any]:
        """执行用户指令的主入口"""
        try:
            # 解析指令
            tasks = await self.parse_user_command(command)
            
            if not tasks:
                return {
                    "success": False,
                    "message": "无法理解该指令，请重新输入",
                    "tasks_created": 0
                }
            
            # 添加任务到队列
            for task in tasks:
                self.add_task(task)
            
            # 处理任务队列
            await self.process_task_queue()
            
            return {
                "success": True,
                "message": f"成功创建并执行了{len(tasks)}个任务",
                "tasks_created": len(tasks),
                "tasks": [
                    {
                        "task_id": t.task_id,
                        "task_type": t.task_type,
                        "status": t.status,
                        "result": t.result
                    } for t in tasks
                ]
            }
            
        except Exception as e:
            logger.error("execute_user_command_failed", error=str(e))
            return {
                "success": False,
                "message": f"执行指令时发生错误: {str(e)}",
                "tasks_created": 0
            }

    async def _execute_exit_request(self, task: ExecutionTask) -> Dict[str, Any]:
        """執行退出請求 - 包含 swap_to_exit_asset"""
        params = task.parameters
        position_id = params["position_id"]
        exit_asset = params["exit_asset"]
        reason = params["reason"]
        position_data = params["position_data"]
        chain = task.chain

        logger.info("executing_exit_request",
                   position_id=position_id,
                   exit_asset=exit_asset,
                   reason=reason)

        # 執行步驟
        steps = [
            f"檢查持倉狀態 ({position_id})",
            "移除流動性",
            "獲取代幣餘額",
            f"執行 swap_to_exit_asset → {exit_asset}",
            "確認交易並更新狀態"
        ]

        for step in steps:
            logger.info("exit_execution_step", step=step, position_id=position_id)
            await asyncio.sleep(1)  # 模擬執行時間

        # 模擬收到的代幣
        tokens_received = {
            "SOL": 8.5,
            "USDC": 1200.0
        } if chain == "solana" else {
            "BNB": 12.5,
            "USDT": 8500.0
        }

        # 執行 swap_to_exit_asset
        swap_result = await self.swap_to_exit_asset(
            chain=chain,
            tokens_received=tokens_received,
            exit_asset=exit_asset,
            position_id=position_id
        )

        # 構建結果
        result = {
            "position_id": position_id,
            "status": "closed",
            "exit_asset": exit_asset,
            "exit_amount": swap_result["exit_amount"],
            "transaction_hash": swap_result["tx_hash"],
            "reason": reason,
            "gas_used": swap_result["gas_used"],
            "slippage": swap_result["slippage"],
            "execution_time": "35s",
            "swap_details": swap_result
        }

        return result

    async def swap_to_exit_asset(self, chain: str, tokens_received: Dict[str, float],
                                exit_asset: str, position_id: str) -> Dict[str, Any]:
        """核心方法：將收到的代幣兌換為指定的退出資產"""
        logger.info("swap_to_exit_asset_start",
                   chain=chain,
                   exit_asset=exit_asset,
                   tokens=tokens_received)

        if chain == "solana":
            return await self._swap_solana_jupiter(tokens_received, exit_asset, position_id)
        elif chain == "bsc":
            return await self._swap_bsc_pancake(tokens_received, exit_asset, position_id)
        else:
            raise ValueError(f"Unsupported chain: {chain}")

    async def _swap_solana_jupiter(self, tokens: Dict[str, float],
                                  exit_asset: str, position_id: str) -> Dict[str, Any]:
        """使用 Jupiter 在 Solana 上執行代幣兌換"""
        logger.info("jupiter_swap_start", exit_asset=exit_asset, tokens=tokens)

        # 模擬 Jupiter API 調用
        total_exit_amount = 0
        swap_transactions = []

        for token_symbol, amount in tokens.items():
            if token_symbol == exit_asset:
                # 已經是目標資產，無需兌換
                total_exit_amount += amount
                continue

            # 模擬 Jupiter 兌換
            await asyncio.sleep(1)  # 模擬 API 調用時間

            # 模擬兌換率
            if token_symbol == "USDC" and exit_asset == "SOL":
                rate = 1 / 140  # 1 USDC = 1/140 SOL
            elif token_symbol == "SOL" and exit_asset == "USDC":
                rate = 140  # 1 SOL = 140 USDC
            else:
                rate = 1  # 默認 1:1

            exit_amount = amount * rate * 0.997  # 扣除 0.3% 手續費
            total_exit_amount += exit_amount

            swap_transactions.append({
                "from_token": token_symbol,
                "to_token": exit_asset,
                "from_amount": amount,
                "to_amount": exit_amount,
                "rate": rate,
                "tx_hash": f"jupiter_{position_id}_{token_symbol}_{int(datetime.now().timestamp())}"
            })

            logger.info("jupiter_swap_completed",
                       from_token=token_symbol,
                       to_token=exit_asset,
                       amount=exit_amount)

        return {
            "exit_amount": total_exit_amount,
            "tx_hash": swap_transactions[0]["tx_hash"] if swap_transactions else f"no_swap_{position_id}",
            "gas_used": 0.000012,  # SOL
            "slippage": 0.003,  # 0.3%
            "swap_count": len(swap_transactions),
            "swap_details": swap_transactions
        }

    async def _swap_bsc_pancake(self, tokens: Dict[str, float],
                               exit_asset: str, position_id: str) -> Dict[str, Any]:
        """使用 PancakeSwap 在 BSC 上執行代幣兌換"""
        logger.info("pancake_swap_start", exit_asset=exit_asset, tokens=tokens)

        # 模擬 PancakeSwap Router 調用
        total_exit_amount = 0
        swap_transactions = []

        for token_symbol, amount in tokens.items():
            if token_symbol == exit_asset:
                # 已經是目標資產，無需兌換
                total_exit_amount += amount
                continue

            # 模擬 PancakeSwap 兌換
            await asyncio.sleep(1)  # 模擬交易時間

            # 模擬兌換率
            if token_symbol == "USDT" and exit_asset == "BNB":
                rate = 1 / 680  # 1 USDT = 1/680 BNB
            elif token_symbol == "BNB" and exit_asset == "USDT":
                rate = 680  # 1 BNB = 680 USDT
            elif token_symbol == "USDC" and exit_asset == "BNB":
                rate = 1 / 680  # 1 USDC = 1/680 BNB
            else:
                rate = 1  # 默認 1:1

            exit_amount = amount * rate * 0.9975  # 扣除 0.25% 手續費
            total_exit_amount += exit_amount

            swap_transactions.append({
                "from_token": token_symbol,
                "to_token": exit_asset,
                "from_amount": amount,
                "to_amount": exit_amount,
                "rate": rate,
                "tx_hash": f"0x{position_id}_{token_symbol}_{int(datetime.now().timestamp()):x}"
            })

            logger.info("pancake_swap_completed",
                       from_token=token_symbol,
                       to_token=exit_asset,
                       amount=exit_amount)

        return {
            "exit_amount": total_exit_amount,
            "tx_hash": swap_transactions[0]["tx_hash"] if swap_transactions else f"0x{position_id}_no_swap",
            "gas_used": 0.0025,  # BNB
            "slippage": 0.0025,  # 0.25%
            "swap_count": len(swap_transactions),
            "swap_details": swap_transactions
        }

    async def _publish_tx_event(self, result: Dict[str, Any], status: str):
        """發佈 bus.tx 事件"""
        from ..events.dyflow_events import TxEvent, event_bus, EventType

        tx_event = TxEvent(
            position_id=result.get("position_id", "unknown"),
            tx_hash=result.get("transaction_hash", ""),
            status=status,
            exit_asset=result.get("exit_asset") if status == "closed" else None,
            qty=result.get("exit_amount") if status == "closed" else None
        )

        # 發佈到事件總線
        event_bus.publish(EventType.TX_OPEN if status == "open" else EventType.TX_CLOSED, tx_event)

        logger.info("bus_tx_event_published",
                   position_id=tx_event.position_id,
                   status=tx_event.status,
                   tx_hash=tx_event.tx_hash)
