"""
DyFlow v3.4 Agno Framework 事件系統
基於 Agno Framework 的內建事件通訊機制
移除 Avro Schema 依賴，使用 Agno Teams + Workflows
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
import json

class EventType(Enum):
    """事件類型枚舉"""
    # 1️⃣ 掃描線 (bus.pool)
    POOL_DISCOVERED = "bus.pool"
    
    # 2️⃣ 選擇線 (bus.lpplan)  
    LP_PLAN_CREATED = "bus.lpplan"
    
    # 3️⃣ 執行線 (bus.tx)
    TX_OPEN = "bus.tx.open"
    TX_CLOSED = "bus.tx.closed"
    TX_FAILED = "bus.tx.failed"
    
    # 4️⃣ 風控線 (ExitRequest event)
    EXIT_REQUEST = "ExitRequest"
    
    # 5️⃣ 收益線 (FeeCollector)
    FEE_COLLECTED = "FeeCollected"
    
    # 系統事件
    GLOBALS_DIFF = "globalsDiff"
    AGENT_STATE_CHANGED = "agent_state_changed"
    HEALTH_UPDATE = "health_update"

class TradingMode(Enum):
    """交易模式"""
    ACTIVE = "active"
    EXIT_ONLY = "exit_only" 
    PAUSED = "paused"

class StrategyType(Enum):
    """策略類型"""
    SPOT_BALANCED = "SPOT_BALANCED"
    CURVE_BALANCED = "CURVE_BALANCED"
    BID_ASK_BALANCED = "BID_ASK_BALANCED"
    SPOT_IMBALANCED_DAMM = "SPOT_IMBALANCED_DAMM"

@dataclass
class PoolEvent:
    """bus.pool 事件格式"""
    pool_id: str  # "Sol.../USDC"
    chain: str    # "sol" | "bsc"
    tvl: float
    fee_tvl_pct: float
    sigma: float
    spread: float
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class RiskProfile:
    """風險配置"""
    il_cut: float = -0.08
    var_cut: float = 0.05
    sigma_cut: float = 0.05
    holding_window: int = 3600  # 秒
    exit_asset: str = "SOL"  # 或 "BNB"

@dataclass
class LPPlanEvent:
    """bus.lpplan 事件格式"""
    plan_id: str
    pool_id: str
    strategy: StrategyType
    k: float  # 寬度參數
    notional_usd: float
    risk_profile: RiskProfile
    timestamp: str = None
    approved: bool = False
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class TxEvent:
    """bus.tx 事件格式"""
    position_id: str
    tx_hash: str
    status: str  # "open" | "closed" | "failed"
    exit_asset: Optional[str] = None
    qty: Optional[float] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class ExitRequestEvent:
    """ExitRequest 事件格式"""
    position_id: str
    exit_asset: str
    reason: str  # "il_net_breach" | "var_breach" | "sigma_breach" | "time_limit"
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class GlobalsDiffEvent:
    """globalsDiff 事件格式"""
    trading_mode: TradingMode
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class AgentStateEvent:
    """Agent 狀態變化事件"""
    agent_name: str
    state: str  # "idle" | "active" | "completed" | "failed"
    task: str
    progress: float = 0.0
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class AgnoEventBus:
    """
    Agno Framework 事件總線
    替代 Avro + JetStream，使用 Agno 內建通訊機制
    """

    def __init__(self):
        self.subscribers = {}
        self.event_history = []
        self.async_subscribers = {}

    def subscribe(self, event_type: EventType, callback):
        """訂閱事件 (同步回調)"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)

    def subscribe_async(self, event_type: EventType, callback):
        """訂閱事件 (異步回調)"""
        if event_type not in self.async_subscribers:
            self.async_subscribers[event_type] = []
        self.async_subscribers[event_type].append(callback)

    async def publish(self, event_type: EventType, event_data):
        """發布事件"""
        import asyncio

        # 記錄事件歷史
        self.event_history.append({
            'type': event_type.value,
            'data': asdict(event_data) if hasattr(event_data, '__dataclass_fields__') else event_data,
            'timestamp': datetime.now().isoformat()
        })

        # 通知同步訂閱者
        if event_type in self.subscribers:
            for callback in self.subscribers[event_type]:
                try:
                    callback(event_data)
                except Exception as e:
                    print(f"Event callback error: {e}")

        # 通知異步訂閱者
        if event_type in self.async_subscribers:
            tasks = []
            for callback in self.async_subscribers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        tasks.append(callback(event_data))
                    else:
                        callback(event_data)
                except Exception as e:
                    print(f"Async event callback error: {e}")

            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

    def get_recent_events(self, limit: int = 100) -> List[Dict[str, Any]]:
        """獲取最近的事件"""
        return self.event_history[-limit:]

    def get_events_by_type(self, event_type: EventType, limit: int = 50) -> List[Dict[str, Any]]:
        """根據事件類型獲取事件"""
        filtered_events = [
            event for event in self.event_history
            if event['type'] == event_type.value
        ]
        return filtered_events[-limit:]

# 全局 Agno 事件總線實例
agno_event_bus = AgnoEventBus()

def create_pool_event(pool_id: str, chain: str, tvl: float, fee_tvl_pct: float, 
                     sigma: float, spread: float) -> PoolEvent:
    """創建池子事件"""
    return PoolEvent(
        pool_id=pool_id,
        chain=chain,
        tvl=tvl,
        fee_tvl_pct=fee_tvl_pct,
        sigma=sigma,
        spread=spread
    )

def create_lpplan_event(plan_id: str, pool_id: str, strategy: StrategyType,
                       k: float, notional_usd: float, risk_profile: RiskProfile) -> LPPlanEvent:
    """創建 LP 計劃事件"""
    return LPPlanEvent(
        plan_id=plan_id,
        pool_id=pool_id,
        strategy=strategy,
        k=k,
        notional_usd=notional_usd,
        risk_profile=risk_profile
    )

def create_exit_request(position_id: str, exit_asset: str, reason: str) -> ExitRequestEvent:
    """創建退出請求事件"""
    return ExitRequestEvent(
        position_id=position_id,
        exit_asset=exit_asset,
        reason=reason
    )

def create_globals_diff(trading_mode: TradingMode) -> GlobalsDiffEvent:
    """創建全局狀態變更事件"""
    return GlobalsDiffEvent(trading_mode=trading_mode)
