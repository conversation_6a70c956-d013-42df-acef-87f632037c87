from dataclasses import dataclass, field
from typing import Optional, Dict, Any


@dataclass
class TokenInfo:
    symbol: str
    address: str
    decimals: int = 18


@dataclass
class PoolMetrics:
    apr: float
    volume_24h: float
    tvl: float
    volatility: float
    token0: str
    token1: str
    fee_rate: float
    chain: str
    pool_id: str
    liquidity_depth: Optional[float] = None
    price_impact: Optional[float] = None
    trading_frequency: Optional[float] = None
