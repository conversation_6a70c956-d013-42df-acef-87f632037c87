"""
DyFlow v3.4 Agno Workflow - 標準 Agno 架構實現
參考標準 Agno Workflow 寫法，使用 Agent 和 Team 協調
"""

from textwrap import dedent
from typing import Iterator
from datetime import datetime
import asyncio
import structlog
import sys
from pathlib import Path

from agno.agent import Agent, RunResponse
from agno.models.ollama import Ollama
from agno.team.team import Team
from agno.utils.log import logger
from agno.utils.pprint import pprint_run_response
from agno.workflow import Workflow

# 添加 backend 目錄到 Python 路徑
backend_dir = Path(__file__).parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# 導入真實的 API 工具
from tools.agno_coingecko_tool import CoinGeckoTool
from tools.agno_pancakeswap_tool import PancakeSwapTool
from tools.agno_meteora_tool import MeteoraTool
from tools.agno_strategy_tool import StrategyAnalysisTool

# 設置 logger
logger = structlog.get_logger(__name__)

class DyFlowAgnoWorkflow(Workflow):
    """
    DyFlow v3.4 Agno Workflow - 標準架構實現
    使用 Agno Agent 和 Team 實現 LP 自動化流水線
    """
    
    description: str = "DyFlow v3.4 automated LP strategy system using standard Agno architecture"

    # ========== 定義 Agents ==========
    
    # 市場情報 Agent - 負責挑池
    market_intel_agent = Agent(
        name="MarketIntelAgent",
        role="Market data collection and pool scanning for BSC and Solana",
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        tools=[
            PancakeSwapTool(),
            MeteoraTool(),
            CoinGeckoTool()
        ],
        instructions=dedent("""
            You are a market intelligence agent for DyFlow LP strategy system.
            Your primary responsibilities:
            1. Use PancakeSwapTool to scan BSC PancakeSwap V3 pools for high-yield opportunities
            2. Use MeteoraTool to scan Solana Meteora DLMM v2 pools for LP strategies
            3. Filter pools based on criteria: TVL >= $10M, Created within 2 days, Fee/TVL >= 5%
            4. Use CoinGeckoTool to collect real-time price data
            5. Identify top performing pools for LP deployment

            Focus on these base tokens:
            - BSC: BNB, USDT, USDC, USD1
            - Solana: SOL, USDC, ETH, WBTC

            Use the available tools to get real market data:
            - scan_bsc_pools() to get BSC pools
            - scan_solana_pools() to get Solana pools
            - get_prices() to get token prices

            Return structured data with pool information, TVL, APR, and risk assessment.
        """),
        add_name_to_instructions=True,
        show_tool_calls=True,
        markdown=True,
    )

    # 策略生成 Agent - 負責建倉
    strategy_agent = Agent(
        name="StrategyAgent",
        role="LP strategy generation and portfolio optimization",
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        tools=[
            StrategyAnalysisTool()
        ],
        instructions=dedent("""
            You are a strategy generation agent for DyFlow LP system.
            Your primary responsibilities:
            1. Use StrategyAnalysisTool to generate optimal LP strategies based on market data
            2. Create detailed execution plans with 4 strategy types:
               - SPOT_BALANCED: Balanced exposure around current price (40% allocation)
               - CURVE_BALANCED: Concentrated liquidity with curve optimization (30% allocation)
               - BID_ASK_BALANCED: Market making with bid-ask spread (20% allocation)
               - SPOT_IMBALANCED_DAMM: Dynamic AMM with imbalanced ranges (10% allocation)
            3. Use analyze_pools() to optimize portfolio allocation across different strategies
            4. Use calculate_risk_metrics() to perform risk assessment for each strategy
            5. Generate LPPlan with allocation amounts and expected APY

            Available tools:
            - analyze_pools(pools_data) to optimize portfolio allocation
            - calculate_risk_metrics(price_changes) to assess IL and VaR risks

            Consider factors: market volatility, IL risk, capital efficiency, expected returns.
            Provide detailed reasoning for each strategy selection with quantitative analysis.
        """),
        add_name_to_instructions=True,
        show_tool_calls=True,
        markdown=True,
    )

    # 執行 Agent - 負責交易執行
    execution_agent = Agent(
        name="ExecutionAgent",
        role="Transaction execution and position management", 
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        instructions=dedent("""
            You are an execution agent for DyFlow LP system.
            Your primary responsibilities:
            1. Execute LP transactions on BSC and Solana networks
            2. Manage transaction signing and broadcasting
            3. Monitor transaction status and handle failures
            4. Implement state machine: Idle → Sign → Broadcast → Confirmed
            5. Handle retry logic for failed transactions
            6. Ensure successful deployment of LP strategies
            
            Follow the execution workflow:
            - Validate strategy parameters
            - Sign transactions with proper gas/fee estimation
            - Broadcast to respective networks
            - Monitor confirmation status
            - Report execution results
        """),
        add_name_to_instructions=True,
        show_tool_calls=True,
        markdown=True,
    )

    # 風險監控 Agent - 負責風控 + 收費
    risk_monitor_agent = Agent(
        name="RiskMonitorAgent",
        role="Risk monitoring and fee collection management",
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        instructions=dedent("""
            You are a risk monitoring agent for DyFlow LP system.
            Your primary responsibilities:
            1. Monitor Impermanent Loss (IL) with -8% fuse trigger
            2. Calculate and monitor VaR (Value at Risk) with 4% threshold
            3. Trigger emergency exits when risk thresholds are exceeded
            4. Manage automated fee collection at UTC 02:00
            5. Implement DCA (Dollar Cost Averaging) exit strategies
            6. Provide continuous risk assessment and alerts
            
            Risk monitoring includes:
            - Real-time IL calculation and monitoring
            - VaR_95 calculation and threshold monitoring
            - Position health scoring
            - Automated harvest scheduling
            - Emergency exit coordination
        """),
        add_name_to_instructions=True,
        show_tool_calls=True,
        markdown=True,
    )

    # ========== 定義 Agent Team ==========
    
    lp_strategy_team = Team(
        name="DyFlow LP Strategy Team",
        mode="coordinate",  # 使用協調模式
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        members=[
            market_intel_agent,
            strategy_agent, 
            execution_agent,
            risk_monitor_agent
        ],
        instructions=[
            "You are the DyFlow LP strategy coordination team.",
            "Execute the 4-stage LP automation pipeline in sequence:",
            "1. Market Intelligence: Scan BSC PancakeSwap V3 + Solana Meteora DLMM v2 pools",
            "2. Strategy Generation: Create optimal LP strategies with risk assessment", 
            "3. Transaction Execution: Deploy strategies on respective networks",
            "4. Risk Monitoring: Start IL/VaR monitoring and fee collection",
            "Each stage must complete successfully before proceeding to the next.",
            "Coordinate between agents to ensure smooth workflow execution.",
            "Handle failures gracefully and provide detailed status reports.",
            "Focus on real API data integration and quantitative analysis."
        ],
        success_criteria="All 4 stages of LP automation pipeline completed successfully",
        enable_agentic_context=True,
        show_tool_calls=True,
        markdown=True,
        debug_mode=True,
        show_members_responses=True,
    )

    # ========== 系統監控 Agent ==========
    
    system_monitor = Agent(
        name="SystemMonitor",
        role="System health monitoring and reporting",
        model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
        instructions=[
            "You are the system monitoring agent for DyFlow.",
            "Monitor the execution of the LP automation pipeline.",
            "Generate comprehensive reports on system performance.",
            "Track key metrics: pool count, strategy deployment, risk levels.",
            "Provide real-time status updates and final execution summary.",
            "Ensure all components are functioning within acceptable parameters."
        ],
        show_tool_calls=True,
        markdown=True,
    )

    def run(self) -> Iterator[RunResponse]:
        """
        執行 DyFlow LP 自動化流水線
        4個核心階段：挑池 → 建倉 → 收費 → 風控
        """
        logger.info("DyFlow v3.4 LP automation pipeline started")
        
        # 階段 1-4: 執行 LP 自動化流水線
        pipeline_prompt = dedent("""
            Execute the complete DyFlow LP automation pipeline:
            
            🔄 **4-Stage LP Automation Pipeline**:
            1. **挑池 (Pool Selection)**: Scan BSC PancakeSwap V3 + Solana Meteora DLMM v2
               - Filter: TVL >= $10M, Created within 2 days, Fee/TVL >= 5%
               - Focus: BNB/USDT/USDC/USD1 (BSC), SOL/USDC/ETH/WBTC (Solana)
               - Use real API data from subgraphs and Meteora API
            
            2. **建倉 (Position Opening)**: Generate and deploy LP strategies
               - 4 strategy types: SPOT_BALANCED, CURVE_BALANCED, BID_ASK_BALANCED, SPOT_IMBALANCED_DAMM
               - Quantitative risk assessment and portfolio optimization
               - Execute transactions on respective networks
            
            3. **收費 (Fee Collection)**: Setup automated fee harvesting
               - Schedule UTC 02:00 auto-harvest
               - Implement DCA exit logic
               - Configure fee collection parameters
            
            4. **風控 (Risk Management)**: Start monitoring systems
               - IL fuse at -8% threshold
               - VaR monitoring with 4% limit
               - Real-time position health tracking
            
            **Success Criteria**: Complete 24/7 automated LP system ready for operation
            **Data Requirements**: Use real API calls, no simulated data for market scanning
        """)
        
        logger.info("Executing LP automation pipeline with agent team")
        pipeline_result: RunResponse = self.lp_strategy_team.run(pipeline_prompt)
        
        if pipeline_result is None or not pipeline_result.content:
            yield RunResponse(
                run_id=self.run_id, 
                content="❌ LP automation pipeline failed to execute."
            )
            return

        logger.info("Generating final system report")
        yield from self.system_monitor.run(
            f"Generate a comprehensive report on the DyFlow LP automation pipeline execution:\n\n{pipeline_result.content}",
            stream=True
        )


if __name__ == "__main__":
    # 執行 workflow
    workflow = DyFlowAgnoWorkflow(debug_mode=True)
    report: Iterator[RunResponse] = workflow.run()
    
    # 打印報告
    pprint_run_response(report, markdown=True, show_time=True)
