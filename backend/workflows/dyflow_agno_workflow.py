"""
DyFlow v3.3 Agno Workflow - 使用 Agno Framework 的正確架構
替代 NATS 消息總線，使用 Agno 內建的 Agent 通訊機制
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional, Iterator
from datetime import datetime
from pathlib import Path

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.team import Team
    from agno.workflow import Workflow, RunResponse, RunEvent
    from agno.models.ollama import Ollama
    from agno.storage.sqlite import SqliteStorage
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Workflow:
        pass
    class BaseModel:
        pass
    class SqliteStorage:
        def __init__(self, *args, **kwargs):
            pass
    def Field(*args, **kwargs):
        return None

try:
    from utils.helpers import get_utc_timestamp
    from utils.config import Config
except ImportError:
    # 如果相對導入失敗，嘗試絕對導入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from utils.helpers import get_utc_timestamp
    from utils.config import Config

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

class PhaseResult(BaseModel):
    """階段執行結果"""
    phase_id: int = Field(..., description="階段ID")
    phase_name: str = Field(..., description="階段名稱")
    status: str = Field(..., description="執行狀態", pattern="^(completed|failed|running)$")
    started_at: datetime = Field(..., description="開始時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    duration_seconds: Optional[float] = Field(None, description="執行時長")
    result_data: Optional[Dict[str, Any]] = Field(None, description="執行結果數據")
    error_message: Optional[str] = Field(None, description="錯誤信息")

class SystemStatus(BaseModel):
    """系統狀態"""
    current_phase: int = Field(..., description="當前階段")
    total_phases: int = Field(default=9, description="總階段數")
    overall_status: str = Field(..., description="整體狀態")
    progress_percentage: float = Field(..., description="進度百分比")
    phase_results: List[PhaseResult] = Field(default_factory=list, description="各階段結果")

# ========== DyFlow Agno Workflow ==========

class DyFlowAgnoWorkflow(Workflow):
    """
    DyFlow v3.3 Agno Workflow
    使用 Agno Framework 實現 8-phase 啟動序列和 Agent 協調
    """
    
    description: str = "DyFlow v3.3 automated LP strategy system using Agno Framework"
    
    def __init__(self, session_id: str = None, storage: SqliteStorage = None):
        # 設置存儲
        if storage is None:
            storage = SqliteStorage(
                table_name="dyflow_workflows",
                db_file="data/agno_workflows.db"
            )
        
        super().__init__(
            session_id=session_id or f"dyflow-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            storage=storage
        )
        
        # 配置
        self.config = Config()
        self.current_phase = 0
        self.phase_results: List[PhaseResult] = []
        
        # 初始化 Agno Agents
        self._initialize_agents()
        
        # 創建 Agent Team
        self._create_agent_team()
    
    def _initialize_agents(self):
        """初始化各個 Agno Agents"""
        if not AGNO_AVAILABLE:
            logger.error("agno_framework_not_available")
            return
        
        try:
            # 系統初始化 Agent
            self.supervisor_agent = Agent(
                name="SupervisorAgent",
                role="System initialization and configuration management",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are the DyFlow system supervisor responsible for initialization.",
                    "Read configuration files, manage system startup, and coordinate other agents.",
                    "Ensure all components are properly initialized before proceeding."
                ],
                show_tool_calls=True
            )
            
            # 健康檢查 Agent
            self.health_agent = Agent(
                name="HealthGuardAgent",
                role="System health monitoring and validation",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "Monitor system health including RPC connections, databases, and APIs.",
                    "Ensure all components meet the 90% health threshold.",
                    "Report detailed health status and recommendations."
                ],
                show_tool_calls=True
            )
            
            # 市場情報 Agent
            self.market_intel_agent = Agent(
                name="MarketIntelAgent",
                role="Market data collection and pool scanning",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "Scan BSC and Solana pools for LP opportunities.",
                    "Collect real-time market data and analyze trends.",
                    "Identify high-yield, low-risk pool candidates."
                ],
                show_tool_calls=True
            )
            
            # 策略生成 Agent
            self.strategy_agent = Agent(
                name="StrategyAgent",
                role="LP strategy generation and optimization",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "Generate optimal LP strategies based on market conditions.",
                    "Create detailed execution plans with risk assessments.",
                    "Optimize portfolio allocation across different strategies."
                ],
                show_tool_calls=True
            )
            
            # 執行 Agent
            self.execution_agent = Agent(
                name="ExecutionAgent",
                role="Transaction execution and monitoring",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "Execute LP transactions on BSC and Solana.",
                    "Monitor transaction status and handle failures.",
                    "Ensure successful deployment of strategies."
                ],
                show_tool_calls=True
            )
            
            # 風險監控 Agent
            self.risk_agent = Agent(
                name="RiskSentinelAgent",
                role="Risk monitoring and portfolio protection",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "Monitor IL (Impermanent Loss) and VaR metrics.",
                    "Trigger emergency exits when risk thresholds are exceeded.",
                    "Provide continuous risk assessment and alerts."
                ],
                show_tool_calls=True
            )
            
            logger.info("agno_agents_initialized", agents_count=6)
            
        except Exception as e:
            logger.error("agno_agents_initialization_failed", error=str(e))
            raise
    
    def _create_agent_team(self):
        """創建 Agno Agent Team"""
        if not AGNO_AVAILABLE:
            return
        
        try:
            # 創建協調模式的 Agent Team
            self.dyflow_team = Team(
                name="DyFlow LP Strategy Team",
                mode="coordinate",  # 使用協調模式
                model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
                members=[
                    self.supervisor_agent,
                    self.health_agent,
                    self.market_intel_agent,
                    self.strategy_agent,
                    self.execution_agent,
                    self.risk_agent
                ],
                instructions=[
                    "You are the DyFlow team coordinator managing the 8-phase startup sequence.",
                    "Execute phases sequentially: Init → Health → UI → Wallet → Market → Portfolio → Strategy → Execution → Risk.",
                    "Each phase must complete successfully before proceeding to the next.",
                    "Coordinate between agents to ensure smooth workflow execution.",
                    "Handle failures gracefully and provide detailed status reports."
                ],
                show_members_responses=True,
                markdown=True,
                debug_mode=True
            )
            
            logger.info("agno_team_created", team_name="DyFlow LP Strategy Team")
            
        except Exception as e:
            logger.error("agno_team_creation_failed", error=str(e))
            raise
    
    def run(self, start_phase: int = 0) -> Iterator[RunResponse]:
        """
        執行 DyFlow 8-phase 啟動序列
        使用 Agno Team 協調各個階段
        """
        if not AGNO_AVAILABLE:
            yield RunResponse(
                content="Agno Framework not available. Cannot execute workflow.",
                event=RunEvent.workflow_completed
            )
            return
        
        logger.info("dyflow_agno_workflow_started", start_phase=start_phase)
        
        try:
            # 執行 8-phase 序列
            for phase_id in range(start_phase, 9):
                yield from self._execute_phase(phase_id)
                
                # 檢查階段是否成功
                if self.phase_results and self.phase_results[-1].status == "failed":
                    yield RunResponse(
                        content=f"Phase {phase_id} failed. Stopping workflow.",
                        event=RunEvent.workflow_completed
                    )
                    return
            
            # 生成最終狀態報告
            final_status = self._generate_status_report()
            yield RunResponse(
                content=f"DyFlow workflow completed successfully!\n\n{final_status}",
                event=RunEvent.workflow_completed
            )
            
        except Exception as e:
            logger.error("dyflow_workflow_execution_failed", error=str(e))
            yield RunResponse(
                content=f"Workflow execution failed: {str(e)}",
                event=RunEvent.workflow_completed
            )
    
    def _execute_phase(self, phase_id: int) -> Iterator[RunResponse]:
        """執行單個階段"""
        phase_names = [
            "系統初始化", "健康檢查", "UI啟動", "錢包測試", 
            "市場情報", "投資組合", "策略生成", "交易執行", "風控監控"
        ]
        
        phase_name = phase_names[phase_id] if phase_id < len(phase_names) else f"Phase {phase_id}"
        
        logger.info("executing_phase", phase_id=phase_id, phase_name=phase_name)
        
        # 記錄階段開始
        phase_result = PhaseResult(
            phase_id=phase_id,
            phase_name=phase_name,
            status="running",
            started_at=datetime.now()
        )
        
        yield RunResponse(
            content=f"🔄 Phase {phase_id}: {phase_name} - 開始執行",
            event=RunEvent.run_started
        )
        
        try:
            # 使用 Team 協調執行階段
            phase_prompt = f"""
            Execute Phase {phase_id}: {phase_name}
            
            Phase Requirements:
            {self._get_phase_requirements(phase_id)}
            
            Current System State:
            - Current Phase: {self.current_phase}
            - Previous Phases: {[p.phase_name for p in self.phase_results if p.status == 'completed']}
            
            Please coordinate the appropriate team members to complete this phase.
            Provide detailed status and results.
            """
            
            # 執行 Team 協調
            team_response = self.dyflow_team.run(phase_prompt)
            
            # 處理結果
            if team_response and team_response.content:
                phase_result.status = "completed"
                phase_result.completed_at = datetime.now()
                phase_result.duration_seconds = (phase_result.completed_at - phase_result.started_at).total_seconds()
                phase_result.result_data = {"team_response": team_response.content}
                
                yield RunResponse(
                    content=f"✅ Phase {phase_id}: {phase_name} - 完成\n\n{team_response.content}",
                    event=RunEvent.run_completed
                )
            else:
                raise Exception("Team coordination failed - no response")
                
        except Exception as e:
            phase_result.status = "failed"
            phase_result.completed_at = datetime.now()
            phase_result.error_message = str(e)
            
            yield RunResponse(
                content=f"❌ Phase {phase_id}: {phase_name} - 失敗: {str(e)}",
                event=RunEvent.run_completed
            )
        
        # 保存階段結果
        self.phase_results.append(phase_result)
        self.current_phase = phase_id + 1
        
        # 更新 session state
        self.session_state["current_phase"] = self.current_phase
        self.session_state["phase_results"] = [p.model_dump() for p in self.phase_results]
    
    def _get_phase_requirements(self, phase_id: int) -> str:
        """獲取階段要求"""
        requirements = {
            0: "讀取 YAML/ENV 配置，初始化 Vault 密鑰分發",
            1: "檢查 RPC/Subgraph/DB/UI 健康狀態，要求 ≥ 90% 健康",
            2: "啟動 WebUI 和 PrometheusExporter，確保 http://localhost:3000 返回 200 OK",
            3: "執行 MPC 簽名和 nonce 測試，確保錢包功能正常",
            4: "啟動市場情報收集，開始推送池事件到消息總線",
            5: "初始化投資組合管理，確保 NAV ≥ 0 且資金鎖可寫入",
            6: "生成 LP 策略計劃，產生 LPPlan.approved",
            7: "執行交易，確保至少 1 筆交易成功廣播",
            8: "啟動風險監控，確保 IL_net 和 VaR 均在限制範圍內"
        }
        return requirements.get(phase_id, f"Execute phase {phase_id}")
    
    def _generate_status_report(self) -> str:
        """生成狀態報告"""
        completed_phases = len([p for p in self.phase_results if p.status == "completed"])
        failed_phases = len([p for p in self.phase_results if p.status == "failed"])
        
        report = f"""
## DyFlow v3.3 執行報告

**整體狀態**: {'✅ 成功' if failed_phases == 0 else '❌ 部分失敗'}
**進度**: {completed_phases}/9 階段完成
**當前階段**: Phase {self.current_phase}

### 階段詳情:
"""
        
        for phase in self.phase_results:
            status_icon = "✅" if phase.status == "completed" else "❌" if phase.status == "failed" else "🔄"
            duration = f" ({phase.duration_seconds:.1f}s)" if phase.duration_seconds else ""
            report += f"- {status_icon} Phase {phase.phase_id}: {phase.phase_name}{duration}\n"
            if phase.error_message:
                report += f"  錯誤: {phase.error_message}\n"
        
        return report
    
    def get_current_status(self) -> SystemStatus:
        """獲取當前系統狀態"""
        completed_count = len([p for p in self.phase_results if p.status == "completed"])
        progress = (completed_count / 9) * 100
        
        overall_status = "running"
        if completed_count == 9:
            overall_status = "completed"
        elif any(p.status == "failed" for p in self.phase_results):
            overall_status = "failed"
        
        return SystemStatus(
            current_phase=self.current_phase,
            total_phases=9,
            overall_status=overall_status,
            progress_percentage=progress,
            phase_results=self.phase_results
        )

# ========== 使用示例 ==========

if __name__ == "__main__":
    # 創建 workflow
    workflow = DyFlowAgnoWorkflow()
    
    # 執行 workflow
    for response in workflow.run():
        print(response.content)
        print("-" * 50)
