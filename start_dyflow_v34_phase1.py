#!/usr/bin/env python3
"""
DyFlow v3.4 Phase 1 啟動腳本
專注於實際交易執行和錢包整合
"""

import asyncio
import os
import sys
import structlog
from datetime import datetime
from typing import Dict, Any

# 添加項目根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入 DyFlow 組件
from backend.utils.wallet_manager import UnifiedWalletManager
from backend.agents.enhanced_market_intel_agent import EnhancedMarketIntelAgent
from backend.agents.execution_agent import ExecutionAgent
from backend.agents.lp_strategy_executor import LPStrategyExecutor
from backend.agents.dca_exit_strategy import DCAExitStrategy
from backend.tools.bsc_transaction_builder import BSCTransactionBuilder
from backend.tools.solana_transaction_builder import SolanaTransactionBuilder

# 配置日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

class DyFlowV34Phase1:
    """
    DyFlow v3.4 Phase 1 主系統
    
    重點功能：
    1. 錢包管理和私鑰配置
    2. 實際交易執行 (BSC + Solana)
    3. 狀態持久化
    4. 基礎監控告警
    """
    
    def __init__(self):
        self.config = self._load_config()
        
        # 核心組件
        self.wallet_manager = None
        self.market_intel = None
        self.execution_agent = None
        self.strategy_executor = None
        self.dca_exit = None
        
        # 交易構建器
        self.bsc_builder = None
        self.solana_builder = None
        
        # 系統狀態
        self.system_status = {
            'phase': 'Phase 1',
            'version': 'v3.4',
            'started_at': None,
            'wallet_initialized': False,
            'agents_running': False,
            'transactions_executed': 0
        }
        
        logger.info("dyflow_v34_phase1_initialized")
    
    def _load_config(self) -> Dict[str, Any]:
        """載入配置"""
        return {
            'wallet': {
                'mpc_threshold': '2/3',
                'signature_timeout': 30,
                'backup_wallets': True
            },
            'trading': {
                'max_slippage': float(os.getenv('MAX_SLIPPAGE_PERCENT', '2.0')),
                'daily_limit': float(os.getenv('DAILY_TRADING_LIMIT', '100000')),
                'single_tx_limit': float(os.getenv('SINGLE_TX_LIMIT', '10000'))
            },
            'monitoring': {
                'bsc_scan_interval': int(os.getenv('BSC_SCAN_INTERVAL', '15')),
                'solana_scan_interval': int(os.getenv('SOLANA_SCAN_INTERVAL', '12')),
                'health_check_interval': int(os.getenv('HEALTH_CHECK_INTERVAL', '30'))
            },
            'risk': {
                'il_fuse_threshold': float(os.getenv('IL_FUSE_THRESHOLD', '-8.0')),
                'var_threshold': float(os.getenv('VAR_THRESHOLD', '4.0')),
                'min_pool_tvl': float(os.getenv('MIN_POOL_TVL', '10000000'))
            }
        }
    
    async def start(self):
        """啟動 DyFlow v3.4 Phase 1"""
        try:
            logger.info("starting_dyflow_v34_phase1")
            self.system_status['started_at'] = datetime.now().isoformat()
            
            # Phase 1.1: 錢包初始化
            await self._initialize_wallet_system()
            
            # Phase 1.2: 交易構建器初始化
            await self._initialize_transaction_builders()
            
            # Phase 1.3: 核心 Agents 初始化
            await self._initialize_core_agents()
            
            # Phase 1.4: 系統健康檢查
            await self._perform_system_health_check()
            
            # Phase 1.5: 啟動主循環
            await self._start_main_loop()
            
        except Exception as e:
            logger.error("dyflow_v34_phase1_startup_failed", error=str(e))
            raise
    
    async def _initialize_wallet_system(self):
        """初始化錢包系統"""
        logger.info("initializing_wallet_system")
        
        try:
            # 檢查環境變量
            required_env_vars = ['BSC_PRIVATE_KEY', 'SOLANA_PRIVATE_KEY']
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]
            
            if missing_vars:
                raise Exception(f"Missing required environment variables: {missing_vars}")
            
            # 初始化錢包管理器
            self.wallet_manager = UnifiedWalletManager(self.config['wallet'])
            
            # 執行錢包初始化
            init_result = await self.wallet_manager.initialize()
            
            if not init_result['success']:
                raise Exception(f"Wallet initialization failed: {init_result.get('error')}")
            
            self.system_status['wallet_initialized'] = True
            
            logger.info("wallet_system_initialized",
                       bsc_connected=init_result['wallet_connections']['connections']['bsc']['connected'],
                       solana_connected=init_result['wallet_connections']['connections']['solana']['connected'])
            
        except Exception as e:
            logger.error("wallet_system_initialization_failed", error=str(e))
            raise
    
    async def _initialize_transaction_builders(self):
        """初始化交易構建器"""
        logger.info("initializing_transaction_builders")
        
        try:
            # BSC 交易構建器
            self.bsc_builder = BSCTransactionBuilder({
                'bsc_rpc_url': os.getenv('BSC_RPC_URL'),
                'max_slippage': self.config['trading']['max_slippage']
            })
            
            # Solana 交易構建器
            self.solana_builder = SolanaTransactionBuilder({
                'solana_rpc_url': os.getenv('SOLANA_RPC_URL'),
                'priority_fee': int(os.getenv('SOLANA_PRIORITY_FEE', '10000'))
            })
            
            logger.info("transaction_builders_initialized",
                       bsc_strategies=len(self.bsc_builder.get_supported_strategies()),
                       solana_strategies=len(self.solana_builder.get_supported_strategies()))
            
        except Exception as e:
            logger.error("transaction_builders_initialization_failed", error=str(e))
            raise
    
    async def _initialize_core_agents(self):
        """初始化核心 Agents"""
        logger.info("initializing_core_agents")
        
        try:
            # MarketIntelAgent
            self.market_intel = EnhancedMarketIntelAgent({
                'bsc_scan_interval': self.config['monitoring']['bsc_scan_interval'],
                'sol_scan_interval': self.config['monitoring']['solana_scan_interval']
            })
            
            # ExecutionAgent
            self.execution_agent = ExecutionAgent({
                'max_retries': 3,
                'retry_delay': 5,
                'timeout': 120
            })
            
            # LPStrategyExecutor
            self.strategy_executor = LPStrategyExecutor({
                'risk_threshold': self.config['risk']['var_threshold'],
                'min_tvl': self.config['risk']['min_pool_tvl']
            })
            
            # DCAExitStrategy
            self.dca_exit = DCAExitStrategy({
                'default_batches': int(os.getenv('DEFAULT_DCA_BATCHES', '5')),
                'max_slippage': self.config['trading']['max_slippage']
            })
            
            self.system_status['agents_running'] = True
            
            logger.info("core_agents_initialized",
                       agents=['MarketIntel', 'Execution', 'StrategyExecutor', 'DCAExit'])
            
        except Exception as e:
            logger.error("core_agents_initialization_failed", error=str(e))
            raise
    
    async def _perform_system_health_check(self):
        """執行系統健康檢查"""
        logger.info("performing_system_health_check")
        
        try:
            health_results = {}
            
            # 錢包健康檢查
            wallet_status = self.wallet_manager.get_wallet_status()
            health_results['wallet'] = {
                'bsc_connected': wallet_status['wallet_status']['bsc']['connected'],
                'solana_connected': wallet_status['wallet_status']['solana']['connected'],
                'private_keys_configured': wallet_status['private_keys_configured']
            }
            
            # 交易構建器健康檢查
            health_results['transaction_builders'] = {
                'bsc': self.bsc_builder.get_chain_info(),
                'solana': self.solana_builder.get_chain_info()
            }
            
            # 測試錢包功能
            wallet_test_result = await self.wallet_manager.test_all_functions()
            health_results['wallet_tests'] = wallet_test_result
            
            # 檢查整體健康狀態
            overall_health = all([
                health_results['wallet']['bsc_connected'],
                health_results['wallet']['solana_connected'],
                wallet_test_result['success']
            ])
            
            if not overall_health:
                logger.warning("system_health_check_warnings", results=health_results)
            else:
                logger.info("system_health_check_passed", results=health_results)
            
            return health_results
            
        except Exception as e:
            logger.error("system_health_check_failed", error=str(e))
            raise
    
    async def _start_main_loop(self):
        """啟動主循環"""
        logger.info("starting_main_loop")
        
        try:
            # 啟動市場掃描
            market_intel_task = asyncio.create_task(
                self.market_intel.start_scanning()
            )
            
            # 啟動健康監控
            health_monitor_task = asyncio.create_task(
                self._health_monitor_loop()
            )
            
            # 啟動交易處理循環
            transaction_processor_task = asyncio.create_task(
                self._transaction_processor_loop()
            )
            
            logger.info("main_loop_started",
                       tasks=['market_intel', 'health_monitor', 'transaction_processor'])
            
            # 等待所有任務
            await asyncio.gather(
                market_intel_task,
                health_monitor_task,
                transaction_processor_task,
                return_exceptions=True
            )
            
        except Exception as e:
            logger.error("main_loop_failed", error=str(e))
            raise
    
    async def _health_monitor_loop(self):
        """健康監控循環"""
        while True:
            try:
                await asyncio.sleep(self.config['monitoring']['health_check_interval'])
                
                # 執行健康檢查
                health_status = await self._perform_system_health_check()
                
                # 記錄系統狀態
                logger.info("system_health_status",
                           status=self.system_status,
                           health=health_status)
                
            except Exception as e:
                logger.error("health_monitor_loop_error", error=str(e))
                await asyncio.sleep(5)
    
    async def _transaction_processor_loop(self):
        """交易處理循環"""
        while True:
            try:
                await asyncio.sleep(1)  # 1 秒檢查一次
                
                # 這裡應該處理待執行的交易
                # 暫時只記錄狀態
                
            except Exception as e:
                logger.error("transaction_processor_loop_error", error=str(e))
                await asyncio.sleep(5)
    
    async def stop(self):
        """停止系統"""
        logger.info("stopping_dyflow_v34_phase1")
        
        try:
            # 停止市場掃描
            if self.market_intel:
                await self.market_intel.stop_scanning()
            
            # 關閉 Solana 客戶端
            if self.solana_builder:
                await self.solana_builder.close()
            
            logger.info("dyflow_v34_phase1_stopped")
            
        except Exception as e:
            logger.error("system_stop_failed", error=str(e))

async def main():
    """主函數"""
    dyflow = DyFlowV34Phase1()
    
    try:
        await dyflow.start()
    except KeyboardInterrupt:
        logger.info("received_keyboard_interrupt")
        await dyflow.stop()
    except Exception as e:
        logger.error("dyflow_v34_phase1_failed", error=str(e))
        await dyflow.stop()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
