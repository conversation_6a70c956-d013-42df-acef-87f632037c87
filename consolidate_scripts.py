#!/usr/bin/env python3
"""
DyFlow v3.4 腳本整合工具
分析並整合 scripts 目錄中重複的代碼
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class ScriptConsolidator:
    """腳本整合器"""
    
    def __init__(self):
        self.scripts_dir = Path("scripts")
        self.analysis_results = {}
        
    def analyze_scripts(self):
        """分析腳本重複性"""
        print("🔍 分析 scripts 目錄中的重複代碼...")
        
        # 分類腳本
        script_categories = {
            "檢查腳本": [
                "check_agno_workflow.py",
                "check_dyflow_v34_system.py"
            ],
            "WebSocket 服務器": [
                "websocket_server.py", 
                "simple_websocket_server.py"
            ],
            "啟動腳本": [
                "start_dyflow_v33.py",
                "start_real_data_dyflow.py"
            ],
            "測試腳本": [
                "test_backend_functionality.py",
                "simple_agno_test.py",
                "debug_agno.py"
            ],
            "工具腳本": [
                "demo_trading_executor.py",
                "install_dependencies.py",
                "cleanup_project.py"
            ]
        }
        
        print("\n📊 腳本分類分析:")
        for category, scripts in script_categories.items():
            print(f"\n{category}:")
            existing_scripts = []
            for script in scripts:
                script_path = self.scripts_dir / script
                if script_path.exists():
                    size = script_path.stat().st_size
                    lines = len(script_path.read_text().splitlines())
                    existing_scripts.append((script, size, lines))
                    print(f"  ✅ {script} ({lines} 行, {size} bytes)")
                else:
                    print(f"  ❌ {script} (不存在)")
            
            self.analysis_results[category] = existing_scripts
        
        return script_categories
    
    def identify_duplicates(self):
        """識別重複功能"""
        print("\n🔍 識別重複功能...")
        
        duplicates = {
            "WebSocket 服務器功能": {
                "files": ["websocket_server.py", "simple_websocket_server.py"],
                "duplicate_functions": [
                    "register_client", "unregister_client", "broadcast_to_all",
                    "handle_client", "send_initial_data", "start_data_update_loop",
                    "fetch_bsc_pools", "fetch_sol_pools"
                ],
                "recommendation": "保留 simple_websocket_server.py (更完整)，移除 websocket_server.py"
            },
            "系統檢查功能": {
                "files": ["check_agno_workflow.py", "check_dyflow_v34_system.py"],
                "duplicate_functions": [
                    "check_agno_framework", "check_ollama_connection", 
                    "print_header", "print_section"
                ],
                "recommendation": "整合為單一檢查腳本 check_dyflow_system.py"
            },
            "啟動腳本功能": {
                "files": ["start_dyflow_v33.py", "start_real_data_dyflow.py"],
                "duplicate_functions": [
                    "啟動邏輯", "依賴檢查", "服務器啟動"
                ],
                "recommendation": "已有統一啟動腳本，可以移除這些"
            }
        }
        
        for category, info in duplicates.items():
            print(f"\n{category}:")
            print(f"  重複文件: {', '.join(info['files'])}")
            print(f"  重複功能: {', '.join(info['duplicate_functions'])}")
            print(f"  建議: {info['recommendation']}")
        
        return duplicates
    
    def consolidate_websocket_servers(self):
        """整合 WebSocket 服務器"""
        print("\n🔄 整合 WebSocket 服務器...")
        
        # 保留 simple_websocket_server.py，移除 websocket_server.py
        old_server = self.scripts_dir / "websocket_server.py"
        if old_server.exists():
            backup_path = self.scripts_dir / "backup_websocket_server.py"
            shutil.move(str(old_server), str(backup_path))
            print(f"  📦 備份: websocket_server.py -> backup_websocket_server.py")
        
        # 重命名 simple_websocket_server.py 為主要服務器
        simple_server = self.scripts_dir / "simple_websocket_server.py"
        main_server = self.scripts_dir / "dyflow_websocket_server.py"
        if simple_server.exists() and not main_server.exists():
            shutil.copy2(str(simple_server), str(main_server))
            print(f"  ✅ 創建: dyflow_websocket_server.py (基於 simple_websocket_server.py)")
    
    def consolidate_check_scripts(self):
        """整合檢查腳本"""
        print("\n🔄 整合檢查腳本...")
        
        # 創建統一的檢查腳本
        unified_check_content = '''#!/usr/bin/env python3
"""
DyFlow v3.4 統一系統檢查腳本
整合所有系統檢查功能
"""

import sys
import os
import json
import yaml
import requests
from pathlib import Path
from datetime import datetime

def print_header(title):
    """打印標題"""
    print("\\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)

def print_section(title):
    """打印段落標題"""
    print(f"\\n{'-' * 60}")
    print(f"  {title}")
    print(f"{'-' * 60}")

def check_agno_framework():
    """檢查 Agno Framework"""
    print_section("Agno Framework 檢查")
    
    try:
        import agno
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        print("✅ Agno Framework: 已安裝且可用")
        return True
    except ImportError as e:
        print(f"❌ Agno Framework: 未安裝 ({e})")
        return False

def check_ollama_service():
    """檢查 Ollama 服務"""
    print_section("Ollama 服務檢查")
    
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Ollama 服務: 運行中 (v{version_info.get('version', 'unknown')})")
            
            # 檢查模型
            models_response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if models_response.status_code == 200:
                models = models_response.json().get('models', [])
                model_names = [m['name'] for m in models]
                print(f"✅ 可用模型: {len(model_names)} 個")
                
                qwen_models = [m for m in model_names if 'qwen' in m.lower()]
                if qwen_models:
                    print(f"✅ Qwen 模型: {', '.join(qwen_models)}")
                else:
                    print("❌ Qwen 模型: 未找到")
                    print("建議運行: ollama pull qwen2.5:3b")
            return True
        else:
            print(f"❌ Ollama 服務: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama 服務: 無法連接 ({e})")
        return False

def check_dyflow_api():
    """檢查 DyFlow API"""
    print_section("DyFlow API 檢查")
    
    try:
        response = requests.get('http://localhost:8001/api/system/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ DyFlow API: 運行中")
            print(f"  狀態: {data.get('overall_status')}")
            print(f"  階段: {data.get('current_phase')}")
            print(f"  活躍 Agents: {len(data.get('active_agents', []))}")
            return True
        else:
            print(f"❌ DyFlow API: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ DyFlow API: 無法連接 ({e})")
        return False

def check_react_ui():
    """檢查 React UI"""
    print_section("React UI 檢查")
    
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            print("✅ React UI: 運行中")
            return True
        else:
            print(f"❌ React UI: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ React UI: 無法連接 ({e})")
        return False

def main():
    """主檢查函數"""
    print_header("DyFlow v3.4 統一系統檢查")
    print(f"檢查時間: {datetime.now().isoformat()}")
    print(f"工作目錄: {os.getcwd()}")
    
    # 執行所有檢查
    checks = [
        ("Agno Framework", check_agno_framework),
        ("Ollama 服務", check_ollama_service),
        ("DyFlow API", check_dyflow_api),
        ("React UI", check_react_ui)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}: 檢查異常 ({e})")
            results.append((check_name, False))
    
    # 總結
    print_header("檢查結果總結")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{check_name:<20} {status}")
    
    print(f"\\n總計: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\\n🎉 DyFlow v3.4 系統檢查全部通過！")
    else:
        print(f"\\n⚠️ 還有 {total - passed} 項檢查未通過")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        unified_check_path = self.scripts_dir / "check_dyflow_system.py"
        with open(unified_check_path, 'w', encoding='utf-8') as f:
            f.write(unified_check_content)
        
        print(f"  ✅ 創建: check_dyflow_system.py (統一檢查腳本)")
        
        # 備份原始檢查腳本
        for script in ["check_agno_workflow.py", "check_dyflow_v34_system.py"]:
            script_path = self.scripts_dir / script
            if script_path.exists():
                backup_path = self.scripts_dir / f"backup_{script}"
                shutil.move(str(script_path), str(backup_path))
                print(f"  📦 備份: {script} -> backup_{script}")
    
    def cleanup_obsolete_scripts(self):
        """清理過時的腳本"""
        print("\n🗑️  清理過時的腳本...")
        
        obsolete_scripts = [
            "start_dyflow_v33.py",  # 已有統一啟動腳本
            "start_real_data_dyflow.py",  # 已有統一啟動腳本
        ]
        
        for script in obsolete_scripts:
            script_path = self.scripts_dir / script
            if script_path.exists():
                backup_path = self.scripts_dir / f"obsolete_{script}"
                shutil.move(str(script_path), str(backup_path))
                print(f"  📦 移動到過時: {script} -> obsolete_{script}")
    
    def generate_scripts_readme(self):
        """生成 scripts 目錄說明"""
        readme_content = '''# DyFlow v3.4 Scripts 目錄

## 📁 腳本分類

### 🔍 系統檢查
- `check_dyflow_system.py` - 統一系統檢查腳本 (推薦使用)
- `backup_check_*.py` - 原始檢查腳本備份

### 🌐 WebSocket 服務器
- `dyflow_websocket_server.py` - 主要 WebSocket 服務器 (推薦使用)
- `simple_websocket_server.py` - 原始簡化服務器
- `backup_websocket_server.py` - 原始服務器備份

### 🧪 測試和調試
- `test_backend_functionality.py` - 後端功能測試
- `simple_agno_test.py` - Agno 框架測試
- `debug_agno.py` - Agno 調試工具

### 🔧 工具腳本
- `demo_trading_executor.py` - 交易執行演示
- `install_dependencies.py` - 依賴安裝工具
- `cleanup_project.py` - 項目清理工具

### 📦 過時腳本
- `obsolete_*.py` - 已被統一啟動腳本替代的過時腳本

## 🚀 推薦使用

1. **系統檢查**: `python scripts/check_dyflow_system.py`
2. **獨立 WebSocket**: `python scripts/dyflow_websocket_server.py`
3. **後端測試**: `python scripts/test_backend_functionality.py`

## 📝 說明

- 所有重複功能已整合
- 原始文件已備份，可安全使用新的統一腳本
- 過時腳本已移動到 `obsolete_` 前綴，可以刪除
'''
        
        readme_path = self.scripts_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"  ✅ 創建: scripts/README.md")
    
    def run_consolidation(self):
        """執行完整整合"""
        print("🔄 DyFlow v3.4 腳本整合")
        print("=" * 50)
        
        # 分析腳本
        script_categories = self.analyze_scripts()
        
        # 識別重複
        duplicates = self.identify_duplicates()
        
        # 執行整合
        self.consolidate_websocket_servers()
        self.consolidate_check_scripts()
        self.cleanup_obsolete_scripts()
        self.generate_scripts_readme()
        
        # 生成報告
        print("\n" + "=" * 50)
        print("📊 整合完成報告")
        print("=" * 50)
        
        print("✅ 已整合的功能:")
        print("  - WebSocket 服務器 -> dyflow_websocket_server.py")
        print("  - 系統檢查腳本 -> check_dyflow_system.py")
        print("  - 過時啟動腳本已移動")
        
        print("\n📦 備份文件:")
        backup_files = list(self.scripts_dir.glob("backup_*"))
        obsolete_files = list(self.scripts_dir.glob("obsolete_*"))
        
        for backup in backup_files:
            print(f"  - {backup.name}")
        for obsolete in obsolete_files:
            print(f"  - {obsolete.name}")
        
        print(f"\n📋 scripts 目錄現在有 {len(list(self.scripts_dir.glob('*.py')))} 個 Python 腳本")
        print("💡 查看 scripts/README.md 了解使用說明")

def main():
    """主函數"""
    consolidator = ScriptConsolidator()
    
    print("⚠️  即將整合 scripts 目錄中的重複代碼")
    print("📦 原始文件將備份，不會丟失")
    
    response = input("\n是否繼續整合？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 整合已取消")
        return
    
    try:
        consolidator.run_consolidation()
        print("\n🎉 腳本整合完成！")
    except Exception as e:
        print(f"\n❌ 整合過程中出現錯誤: {e}")

if __name__ == "__main__":
    main()
