# DyFlow v3.4 Ultimate Workflow Configuration
# 符合 PRD 規範的完整工作流配置

description: "DyFlow v3.4 autopilot"

# 全局配置 - 符合 PRD 規範
globals:
  trading_mode: pending                # pending|active|exit_only|paused
  guard_policy:
    default_timeout: 4
    default_retries: 2
  portfolio:
    lock_scope: pool                  # global|chain|pool

# 業務 KPI 目標 - 符合 PRD 規範
kpi_targets:
  annual_fee_apr: 0.25               # ≥ 25%
  il_net_drawdown: -0.08             # ≥ -8% auto exit
  agent_ack_latency: 3               # ≤ 3s
  infra_health_score: 0.9            # ≥ 0.9

# Agno Framework 配置
agno_config:
  model:
    provider: ollama
    model_id: qwen2.5:3b
    host: http://localhost:11434
  
  storage:
    type: sqlite
    db_file: data/agno_workflows.db
    table_name: dyflow_v34_workflows
  
  team_mode: coordinate
  show_members_responses: true
  debug_mode: true

# 節點定義 - 符合 PRD 規範
nodes:
  # 新增：OnboardingAgent - v3.4 核心組件
  - id: onboarding
    class: OnboardingAgent
    needs: [health]
    description: "快速掃描和啟動流程管理"
    publish:
      - topic: bus.launch
        persist: yes
    config:
      quickscan_chains: ["bsc", "sol"]
      max_pools_per_chain: 20
      min_tvl_threshold: 10000
      scan_timeout: 30

  # 市場情報 Agent
  - id: intel
    class: MarketIntelAgent
    needs: [onboarding]
    description: "池子掃描和市場數據收集"
    publish:
      - topic: bus.pool               # Avro PoolEvent
        persist: yes
    config:
      scan_interval: 15               # seconds
      chains: ["bsc", "sol"]
      max_pools: 100

  # 策略 Agent
  - id: strategy
    class: StrategyAgent
    subscribe:
      - bus.pool
    publish:
      - topic: bus.lpplan             # Avro LPPlan
    description: "策略選擇和風險配置"
    config:
      strategy_matrix_enabled: true
      risk_profile_auto_gen: true

  # 執行 Agent - 包含狀態機
  - id: exec
    class: ExecutionAgent
    subscribe: [bus.lpplan]
    publish: [bus.tx]
    description: "交易執行和狀態管理"
    state_machine:
      initial_state: "Idle"
      states:
        - Idle
        - Sign
        - Broadcast
        - Confirmed
        - Failed
        - ExitSwap
        - ExitBroadcast
        - ExitConfirmed
      transitions:
        - from: Idle
          to: Sign
          trigger: on_lpplan
        - from: Sign
          to: Broadcast
          trigger: sign_ok
        - from: Sign
          to: Failed
          trigger: sign_err
        - from: Broadcast
          to: Confirmed
          trigger: receipt_ok
        - from: Broadcast
          to: Failed
          trigger: receipt_fail_3x
        - from: "*"
          to: ExitSwap
          trigger: ExitRequest

  # 風險監控 Agent - 包含狀態機
  - id: risk
    class: RiskSentinelAgent
    subscribe: [bus.tx]
    publish:
      - event: ExitRequest
    description: "風險監控和熔斷管理"
    state_machine:
      initial_state: "Watching"
      states:
        - Watching
        - Breach
        - EmitExit
      transitions:
        - from: Watching
          to: Breach
          trigger: "il|var|sigma|timeout"
        - from: Breach
          to: EmitExit
          trigger: breach_confirmed
        - from: EmitExit
          to: Watching
          trigger: exit_emitted
    config:
      monitoring_interval: 10         # seconds
      il_net_formula: "il_raw - fee_acc_usd / notional"

  # 手續費收集工具
  - id: fees
    tool: FeeCollectorTool
    schedule: "0 2 * * *"            # UTC 02:00
    description: "手續費收集和 NAV 更新"
    config:
      collection_schedule: "0 2 * * *"
      update_interval: 600           # 10 minutes

# 事件主題配置 - 符合 Avro Schema
event_topics:
  bus.pool:
    schema: "schemas/PoolEvent.avsc"
    persist: true
    retention: "7d"
  
  bus.lpplan:
    schema: "schemas/LPPlan.avsc"
    persist: true
    retention: "30d"
  
  bus.tx:
    schema: "schemas/TxEvent.avsc"
    persist: true
    retention: "90d"
  
  bus.launch:
    schema: "schemas/LaunchEvent.avsc"
    persist: true
    retention: "30d"

# 安全配置 - 符合 PRD Vault 要求
security:
  vault:
    enabled: true
    transit_engine: "dyflow-transit"
    key_name: "wallet-signing-key"
    auto_wipe_timeout: 15           # seconds
  
  wallet:
    mpc_threshold: "2-of-3"
    nonce_check: true
    signing_timeout: 10

# WebSocket 配置 - 前端事件映射
websocket:
  enabled: true
  port: 8001
  path: "/ws/dyflow"
  cors_origins: ["http://localhost:3000", "http://localhost:3001"]
  
  # 事件映射 - 符合 PRD 規範
  event_mapping:
    globalsDiff: "globals"
    nav_update: "nav"
    bus.pool: "pools"
    task_state_changed: "flow"
    lp_update: "positions"
    tx_exit: "positions"
    risk_update: "riskSummary"
    health_update: "infra"
    QuickScanResult: "wizardState"
    LaunchProposal: "wizardState"

# 監控配置
monitoring:
  prometheus:
    enabled: true
    port: 9090
    metrics_path: "/metrics"
    
  logging:
    level: "INFO"
    format: "json"
    file: "logs/dyflow_v34.log"
    max_size: "100MB"
    backup_count: 5

# API 端點配置
api_endpoints:
  - path: "/quickscan"
    method: "GET"
    handler: "OnboardingAgent.trigger_quick_scan"
  
  - path: "/confirm"
    method: "POST"
    handler: "OnboardingAgent.launch_confirmed"
  
  - path: "/trading_mode"
    method: "POST"
    handler: "CoreAgent.set_trading_mode"

# 部署配置
deployment:
  environment: "production"
  namespace: "dyflow-v34"
  replicas: 1
  resources:
    cpu: "2"
    memory: "4Gi"
  
  health_checks:
    liveness_probe: "/health"
    readiness_probe: "/ready"
    startup_probe: "/startup"
