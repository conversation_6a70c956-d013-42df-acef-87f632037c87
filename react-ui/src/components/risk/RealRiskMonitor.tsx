/**
 * 真實風險監控組件
 * 顯示實時風險指標和警報
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  Shield, 
  AlertTriangle, 
  TrendingDown,
  Activity,
  Target,
  Settings,
  RefreshCw,
  Bell,
  BellOff
} from 'lucide-react'

interface RiskMetrics {
  riskScore: number
  ilCurrent: number
  ilThreshold: number
  var95: number
  varThreshold: number
  maxDrawdown: number
  sharpeRatio: number
  volatility: number
  correlations: { [key: string]: number }
  alerts: RiskAlert[]
}

interface RiskAlert {
  id: string
  level: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: Date
  acknowledged: boolean
}

export const RealRiskMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<RiskMetrics>({
    riskScore: 75,
    ilCurrent: -1.9,
    ilThreshold: -8.0,
    var95: 2.8,
    varThreshold: 4.0,
    maxDrawdown: -3.2,
    sharpeRatio: 1.85,
    volatility: 0.25,
    correlations: {
      'BNB/USDT': 0.65,
      'ETH/USDC': 0.72,
      'SOL/USDC': 0.58
    },
    alerts: [
      {
        id: '1',
        level: 'medium',
        message: 'BNB/USDT 池子波動率上升至 28%',
        timestamp: new Date(Date.now() - 300000),
        acknowledged: false
      },
      {
        id: '2',
        level: 'low',
        message: 'SOL/USDC 相關性降低至 0.58',
        timestamp: new Date(Date.now() - 600000),
        acknowledged: true
      }
    ]
  })
  
  const [alertsEnabled, setAlertsEnabled] = useState(true)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // 模擬實時數據更新
    const interval = setInterval(() => {
      updateMetrics()
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const updateMetrics = () => {
    setMetrics(prev => ({
      ...prev,
      riskScore: Math.max(0, Math.min(100, prev.riskScore + (Math.random() - 0.5) * 5)),
      ilCurrent: prev.ilCurrent + (Math.random() - 0.5) * 0.2,
      var95: Math.max(0, prev.var95 + (Math.random() - 0.5) * 0.3),
      maxDrawdown: Math.min(0, prev.maxDrawdown + (Math.random() - 0.5) * 0.5),
      volatility: Math.max(0, prev.volatility + (Math.random() - 0.5) * 0.02)
    }))
  }

  const refreshMetrics = async () => {
    setLoading(true)
    // 模擬 API 調用
    setTimeout(() => {
      updateMetrics()
      setLoading(false)
    }, 1000)
  }

  const acknowledgeAlert = (alertId: string) => {
    setMetrics(prev => ({
      ...prev,
      alerts: prev.alerts.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    }))
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600'
    if (score >= 60) return 'text-amber-600'
    return 'text-rose-600'
  }

  const getRiskScoreLabel = (score: number) => {
    if (score >= 80) return '低風險'
    if (score >= 60) return '中風險'
    return '高風險'
  }

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-rose-100 border-rose-300 text-rose-800'
      case 'high': return 'bg-orange-100 border-orange-300 text-orange-800'
      case 'medium': return 'bg-amber-100 border-amber-300 text-amber-800'
      case 'low': return 'bg-blue-100 border-blue-300 text-blue-800'
      default: return 'bg-gray-100 border-gray-300 text-gray-800'
    }
  }

  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="w-4 h-4" />
      case 'medium':
        return <Activity className="w-4 h-4" />
      case 'low':
        return <Target className="w-4 h-4" />
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const unacknowledgedAlerts = metrics.alerts.filter(alert => !alert.acknowledged)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 風險總覽 */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-5 h-5 text-blue-500" />
              <CardTitle className="text-lg font-bold">風險監控</CardTitle>
              {unacknowledgedAlerts.length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {unacknowledgedAlerts.length} 警報
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setAlertsEnabled(!alertsEnabled)}
              >
                {alertsEnabled ? <Bell className="w-4 h-4" /> : <BellOff className="w-4 h-4" />}
              </Button>
              
              <Button 
                size="sm" 
                variant="outline"
                onClick={refreshMetrics}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 風險分數 */}
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">
              <span className={getRiskScoreColor(metrics.riskScore)}>
                {metrics.riskScore.toFixed(0)}
              </span>
              <span className="text-lg text-gray-500 ml-1">/100</span>
            </div>
            <div className={`text-sm font-medium ${getRiskScoreColor(metrics.riskScore)}`}>
              {getRiskScoreLabel(metrics.riskScore)}
            </div>
            <Progress 
              value={metrics.riskScore} 
              className="mt-3"
            />
          </div>

          {/* 關鍵指標 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">IL 風險</div>
              <div className={`text-lg font-bold ${metrics.ilCurrent <= -5 ? 'text-rose-600' : 'text-gray-900'}`}>
                {metrics.ilCurrent.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">
                閾值: {metrics.ilThreshold}%
              </div>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">VaR 95%</div>
              <div className={`text-lg font-bold ${metrics.var95 > metrics.varThreshold ? 'text-rose-600' : 'text-gray-900'}`}>
                {metrics.var95.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">
                閾值: {metrics.varThreshold}%
              </div>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">最大回撤</div>
              <div className={`text-lg font-bold ${metrics.maxDrawdown <= -5 ? 'text-rose-600' : 'text-gray-900'}`}>
                {metrics.maxDrawdown.toFixed(1)}%
              </div>
            </div>
            
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">夏普比率</div>
              <div className={`text-lg font-bold ${metrics.sharpeRatio >= 1.5 ? 'text-emerald-600' : 'text-gray-900'}`}>
                {metrics.sharpeRatio.toFixed(2)}
              </div>
            </div>
          </div>

          {/* 波動率 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">組合波動率</span>
              <span className="text-sm font-bold">{(metrics.volatility * 100).toFixed(1)}%</span>
            </div>
            <Progress 
              value={metrics.volatility * 100} 
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* 風險警報 */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              <CardTitle className="text-lg font-bold">風險警報</CardTitle>
            </div>
            
            <Button size="sm" variant="outline">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <div className="space-y-2 p-4">
            {metrics.alerts.length > 0 ? (
              metrics.alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`border rounded-lg p-3 ${getAlertColor(alert.level)} ${
                    alert.acknowledged ? 'opacity-60' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2">
                      {getAlertIcon(alert.level)}
                      <div className="flex-1">
                        <div className="text-sm font-medium">{alert.message}</div>
                        <div className="text-xs opacity-75 mt-1">
                          {alert.timestamp.toLocaleString()}
                        </div>
                      </div>
                    </div>
                    
                    {!alert.acknowledged && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs px-2 py-1"
                        onClick={() => acknowledgeAlert(alert.id)}
                      >
                        確認
                      </Button>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Shield className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">暫無風險警報</p>
                <p className="text-xs">系統運行正常</p>
              </div>
            )}
          </div>

          {/* 相關性矩陣 */}
          <div className="border-t p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">池子相關性</h4>
            <div className="space-y-2">
              {Object.entries(metrics.correlations).map(([pool, correlation]) => (
                <div key={pool} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{pool}</span>
                  <div className="flex items-center space-x-2">
                    <Progress 
                      value={Math.abs(correlation) * 100} 
                      className="w-16 h-2"
                    />
                    <span className={`text-sm font-medium ${
                      Math.abs(correlation) > 0.7 ? 'text-rose-600' : 'text-gray-900'
                    }`}>
                      {correlation.toFixed(2)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
