/**
 * Toast 通知組件
 * 用於顯示 ExitRequest 等重要事件通知
 */

import React, { useEffect, useState } from 'react'
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'

interface ToastData {
  title: string
  message: string
  variant?: 'default' | 'destructive' | 'success' | 'warning'
  duration?: number
  position_id?: string
  exit_asset?: string
}

interface ToastProps extends ToastData {
  onClose: () => void
}

const Toast: React.FC<ToastProps> = ({
  title,
  message,
  variant = 'default',
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 300) // 等待動畫完成
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  const getVariantStyles = () => {
    switch (variant) {
      case 'destructive':
        return 'bg-rose-50 border-rose-200 text-rose-800'
      case 'success':
        return 'bg-emerald-50 border-emerald-200 text-emerald-800'
      case 'warning':
        return 'bg-amber-50 border-amber-200 text-amber-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <AlertTriangle className="w-5 h-5 text-rose-500" />
      case 'success':
        return <CheckCircle className="w-5 h-5 text-emerald-500" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-amber-500" />
      default:
        return <Info className="w-5 h-5 text-blue-500" />
    }
  }

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-md w-full
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <div className={`
        rounded-lg border p-4 shadow-lg backdrop-blur-sm
        ${getVariantStyles()}
      `}>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold mb-1">{title}</h4>
            <p className="text-sm opacity-90">{message}</p>
          </div>
          
          <button
            onClick={() => {
              setIsVisible(false)
              setTimeout(onClose, 300)
            }}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<Array<ToastData & { id: string }>>([])

  useEffect(() => {
    const handleToast = (event: CustomEvent<ToastData>) => {
      const newToast = {
        ...event.detail,
        id: Date.now().toString()
      }
      
      setToasts(prev => [...prev, newToast])
    }

    window.addEventListener('dyflow-toast', handleToast as EventListener)
    
    return () => {
      window.removeEventListener('dyflow-toast', handleToast as EventListener)
    }
  }, [])

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  return (
    <div className="fixed top-0 right-0 z-50 pointer-events-none">
      <div className="flex flex-col space-y-2 p-4 pointer-events-auto">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            style={{ 
              transform: `translateY(${index * 10}px)`,
              zIndex: 1000 - index
            }}
          >
            <Toast
              {...toast}
              onClose={() => removeToast(toast.id)}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

// 便捷函數
export const showToast = (data: ToastData) => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('dyflow-toast', { detail: data }))
  }
}

// 預設的通知類型
export const showExitRequestToast = (position_id: string, reason: string, exit_asset: string) => {
  const reasonMessages = {
    'il_net_breach': 'IL 熔斷觸發：IL ≤ -8%',
    'var_breach': 'VaR 風險超標',
    'sigma_breach': '波動率過高',
    'holding_window_expired': '持倉時間到期'
  }

  const reasonText = reasonMessages[reason as keyof typeof reasonMessages] || reason

  showToast({
    title: '🚨 風控熔斷',
    message: `熔斷原因：${reasonText}，將兌回 ${exit_asset}`,
    variant: 'destructive',
    duration: 8000,
    position_id,
    exit_asset
  })
}

export const showSuccessToast = (message: string) => {
  showToast({
    title: '✅ 操作成功',
    message,
    variant: 'success',
    duration: 4000
  })
}

export const showWarningToast = (message: string) => {
  showToast({
    title: '⚠️ 注意',
    message,
    variant: 'warning',
    duration: 6000
  })
}

export const showErrorToast = (message: string) => {
  showToast({
    title: '❌ 錯誤',
    message,
    variant: 'destructive',
    duration: 8000
  })
}

export default Toast
