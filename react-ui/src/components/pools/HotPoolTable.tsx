/**
 * 熱門池子表格
 * MarketIntelAgent 掃描結果展示
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs'
import {
  Search,
  TrendingUp,
  ExternalLink,
  Filter,
  RefreshCw,
  Target,
  Clock,
  Eye
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

type ChainFilter = 'all' | 'bsc' | 'solana'
type SortField = 'tvl' | 'apr' | 'fees24h' | 'volatility'

export const HotPoolTable: React.FC = () => {
  const hotPools = useAgnoStore(state => state.hotPools)
  const [activeTab, setActiveTab] = useState<'BSC' | 'SOL'>('BSC')
  const [sortField, setSortField] = useState<SortField>('apr')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const filteredAndSortedPools = hotPools
    .filter(pool => {
      // 根據當前 Tab 過濾
      if (activeTab === 'BSC') return pool.chain === 'bsc'
      if (activeTab === 'SOL') return pool.chain === 'solana'
      return true
    })
    .sort((a, b) => {
      let aValue: number, bValue: number
      
      switch (sortField) {
        case 'tvl':
          aValue = a.tvl_usd || 0
          bValue = b.tvl_usd || 0
          break
        case 'apr':
          aValue = a.apr || 0
          bValue = b.apr || 0
          break
        case 'fees24h':
          aValue = a.fees_24h || 0
          bValue = b.fees_24h || 0
          break
        case 'volatility':
          aValue = a.volatility || 0
          bValue = b.volatility || 0
          break
        default:
          return 0
      }
      
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    })

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const getChainColor = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'bg-amber-500'
      case 'solana': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getChainText = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'BSC'
      case 'solana': return 'SOL'
      default: return chain.toUpperCase()
    }
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-emerald-600 bg-emerald-50'
      case 'medium': return 'text-amber-600 bg-amber-50'
      case 'high': return 'text-rose-600 bg-rose-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getRiskText = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '低風險'
      case 'medium': return '中風險'
      case 'high': return '高風險'
      default: return '未知'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return 'bg-blue-500'
      case 'CURVE_BALANCED': return 'bg-green-500'
      case 'BID_ASK_BALANCED': return 'bg-purple-500'
      case 'SPOT_IMBALANCED_DAMM': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  const getStrategyText = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return '對稱'
      case 'CURVE_BALANCED': return '曲線'
      case 'BID_ASK_BALANCED': return '價差'
      case 'SPOT_IMBALANCED_DAMM': return '單邊'
      default: return '未知'
    }
  }

  const bscPools = hotPools.filter(p => p.chain === 'bsc')
  const solanaPools = hotPools.filter(p => p.chain === 'solana')

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold">池子監控</CardTitle>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <Clock className="w-3 h-3" />
            <span>最後更新：{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'BSC' | 'SOL')}>
          <div className="px-4 pb-3">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="BSC" className="text-sm">
                <span className="mr-2">🟡</span>
                BSC
              </TabsTrigger>
              <TabsTrigger value="SOL" className="text-sm">
                <span className="mr-2">🟣</span>
                Solana
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="BSC" className="mt-0">
            <div className="overflow-hidden">
              <table className="w-full text-xs">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left font-medium text-gray-700">Pool</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">TVL</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">APR</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">σ</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">Spread</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">Fee%</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {bscPools.map((pool, index) => (
                    <PoolTableRow key={`bsc-${pool.address || index}`} pool={pool} />
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="SOL" className="mt-0">
            <div className="overflow-hidden">
              <table className="w-full text-xs">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left font-medium text-gray-700">Pool</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">TVL</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">APR</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">σ</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">Spread</th>
                    <th className="px-3 py-2 text-right font-medium text-gray-700">Fee%</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {solanaPools.map((pool, index) => (
                    <PoolTableRow key={`sol-${pool.address || index}`} pool={pool} />
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>

        {/* 底部統計 */}
        <div className="px-4 py-3 bg-gray-50 border-t">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>顯示 {activeTab === 'BSC' ? bscPools.length : solanaPools.length} 個池子</span>
            <div className="flex items-center space-x-4">
              <span>平均 APR: {
                activeTab === 'BSC'
                  ? bscPools.length > 0 ? (bscPools.reduce((sum, p) => sum + (p.apr || 0), 0) / bscPools.length).toFixed(1) : 0
                  : solanaPools.length > 0 ? (solanaPools.reduce((sum, p) => sum + (p.apr || 0), 0) / solanaPools.length).toFixed(1) : 0
              }%</span>
              <span>總 TVL: ${(activeTab === 'BSC'
                ? bscPools.reduce((sum, p) => sum + (p.tvl_usd || 0), 0)
                : solanaPools.reduce((sum, p) => sum + (p.tvl_usd || 0), 0)
              ).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 池子表格行組件
function PoolTableRow({ pool }: { pool: any }) {
  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(0)}`
  }

  const formatPercentage = (value: number) => `${(value * 100).toFixed(2)}%`

  const getChainColor = (chain: string) => {
    return chain === 'bsc' ? 'bg-amber-100 text-amber-800' : 'bg-purple-100 text-purple-800'
  }

  return (
    <tr className="border-b border-gray-100 hover:bg-gray-50">
      <td className="px-3 py-2">
        <div className="flex items-center space-x-2">
          <Badge className={getChainColor(pool.chain)} variant="secondary">
            {pool.chain === 'bsc' ? 'BSC' : 'SOL'}
          </Badge>
          <span className="font-medium text-sm">{pool.pair}</span>
        </div>
      </td>
      <td className="px-3 py-2 text-right">
        <span className="font-medium">{formatCurrency(pool.tvl_usd || 0)}</span>
      </td>
      <td className="px-3 py-2 text-right">
        <span className="font-bold text-emerald-600">{(pool.apr || 0).toFixed(1)}%</span>
      </td>
      <td className="px-3 py-2 text-right">
        <span className="text-sm">{formatPercentage(pool.volatility || 0.02)}</span>
      </td>
      <td className="px-3 py-2 text-right">
        <span className="text-sm">{formatPercentage(pool.spread || 0.0003)}</span>
      </td>
      <td className="px-3 py-2 text-right">
        <span className="font-medium">{formatPercentage(pool.fee_tvl_ratio || 0.05)}</span>
      </td>
      <td className="px-3 py-2">
        <Button size="sm" variant="outline" className="text-xs px-2 py-1">
          <Eye className="w-3 h-3 mr-1" />
          查看
        </Button>
      </td>
    </tr>
  )
}
