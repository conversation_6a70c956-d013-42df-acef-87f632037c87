/**
 * LaunchWizard - DyFlow v3.4 四步驟啟動引導
 * 符合 PRD 規範的完整啟動流程
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { 
  Loader2, 
  Search, 
  Target, 
  CheckCircle, 
  Rocket,
  TrendingUp,
  Shield,
  DollarSign,
  Clock
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

interface QuickScanResult {
  chain: string
  pools_found: number
  top_pools: Array<{
    pool_id: string
    tvl_usd: number
    fee_tvl_pct: number
    sigma: number
    score: number
  }>
  scan_duration: number
}

interface LaunchProposal {
  proposal_id: string
  total_pools: number
  recommended_pools: Array<{
    pool_id: string
    chain: string
    tvl_usd: number
    fee_tvl_pct: number
    score: number
  }>
  estimated_apy: number
  risk_score: number
  capital_requirement: number
  chains: string[]
}

interface LaunchWizardProps {
  isOpen: boolean
  onClose: () => void
}

export default function LaunchWizard({ isOpen, onClose }: LaunchWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState<QuickScanResult[]>([])
  const [proposal, setProposal] = useState<LaunchProposal | null>(null)
  const [selectedPools, setSelectedPools] = useState<string[]>([])
  const [isLaunching, setIsLaunching] = useState(false)

  const { wizardState, setWizardState } = useAgnoStore()

  // 步驟配置
  const steps = [
    { id: 1, title: '快速掃描', description: '掃描 BSC 和 Solana 最佳機會', icon: Search },
    { id: 2, title: '機會分析', description: '分析池子質量和收益潛力', icon: Target },
    { id: 3, title: '策略配置', description: '選擇池子和風險參數', icon: Shield },
    { id: 4, title: '啟動確認', description: '確認配置並啟動系統', icon: Rocket }
  ]

  // 重置狀態
  const resetWizard = () => {
    setCurrentStep(1)
    setIsScanning(false)
    setScanResults([])
    setProposal(null)
    setSelectedPools([])
    setIsLaunching(false)
  }

  // 關閉處理
  const handleClose = () => {
    resetWizard()
    onClose()
  }

  // 步驟 1: 快速掃描
  const handleQuickScan = async () => {
    setIsScanning(true)
    
    try {
      const response = await fetch('/api/quickscan')
      const data = await response.json()
      
      if (data.status === 'success') {
        setScanResults(data.scan_results)
        setProposal(data.launch_proposal)
        setCurrentStep(2)
      } else {
        throw new Error(data.error || '掃描失敗')
      }
    } catch (error) {
      console.error('Quick scan failed:', error)
      // TODO: 顯示錯誤提示
    } finally {
      setIsScanning(false)
    }
  }

  // 步驟 2: 繼續到策略配置
  const handleContinueToStrategy = () => {
    if (proposal) {
      // 預設選擇所有推薦池子
      setSelectedPools(proposal.recommended_pools.map(p => p.pool_id))
      setCurrentStep(3)
    }
  }

  // 步驟 3: 池子選擇切換
  const togglePoolSelection = (poolId: string) => {
    setSelectedPools(prev => 
      prev.includes(poolId) 
        ? prev.filter(id => id !== poolId)
        : [...prev, poolId]
    )
  }

  // 步驟 4: 確認啟動
  const handleConfirmLaunch = async () => {
    if (!proposal) return
    
    setIsLaunching(true)
    
    try {
      const response = await fetch('/api/confirm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          proposal_id: proposal.proposal_id,
          confirmed_pools: selectedPools
        })
      })
      
      const data = await response.json()
      
      if (data.status === 'confirmed') {
        setCurrentStep(4)
        // 延遲關閉，顯示成功狀態
        setTimeout(() => {
          handleClose()
        }, 3000)
      } else {
        throw new Error(data.error || '啟動失敗')
      }
    } catch (error) {
      console.error('Launch confirmation failed:', error)
      // TODO: 顯示錯誤提示
    } finally {
      setIsLaunching(false)
    }
  }

  // 渲染步驟 1: 快速掃描
  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
          <Search className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">快速掃描市場機會</h3>
        <p className="text-gray-600">
          我們將掃描 BSC 和 Solana 上的最佳 LP 機會，這通常需要 30-60 秒
        </p>
      </div>
      
      <div className="flex justify-center">
        <Button 
          onClick={handleQuickScan}
          disabled={isScanning}
          size="lg"
          className="px-8"
        >
          {isScanning ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              掃描中...
            </>
          ) : (
            <>
              <Search className="w-4 h-4 mr-2" />
              開始掃描
            </>
          )}
        </Button>
      </div>
      
      {isScanning && (
        <div className="space-y-3">
          <Progress value={33} className="w-full" />
          <p className="text-sm text-center text-gray-500">
            正在掃描 BSC PancakeSwap V3 和 Solana Meteora DLMM...
          </p>
        </div>
      )}
    </div>
  )

  // 渲染步驟 2: 機會分析
  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
          <Target className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">掃描完成！</h3>
        <p className="text-gray-600">
          發現了 {proposal?.total_pools} 個池子，為您推薦 {proposal?.recommended_pools.length} 個最佳機會
        </p>
      </div>

      {/* 掃描結果摘要 */}
      <div className="grid grid-cols-2 gap-4">
        {scanResults.map((result, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline" className="uppercase">
                  {result.chain}
                </Badge>
                <span className="text-sm text-gray-500">
                  {result.scan_duration.toFixed(1)}s
                </span>
              </div>
              <div className="text-2xl font-bold">{result.pools_found}</div>
              <div className="text-sm text-gray-600">池子發現</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 關鍵指標 */}
      {proposal && (
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="w-5 h-5 text-green-500 mr-1" />
              <span className="text-2xl font-bold text-green-600">
                {proposal.estimated_apy.toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-600">預估 APY</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Shield className="w-5 h-5 text-blue-500 mr-1" />
              <span className="text-2xl font-bold text-blue-600">
                {proposal.risk_score.toFixed(0)}
              </span>
            </div>
            <div className="text-sm text-gray-600">風險分數</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <DollarSign className="w-5 h-5 text-purple-500 mr-1" />
              <span className="text-2xl font-bold text-purple-600">
                ${(proposal.capital_requirement / 1000).toFixed(0)}k
              </span>
            </div>
            <div className="text-sm text-gray-600">建議資金</div>
          </div>
        </div>
      )}

      <div className="flex justify-center">
        <Button onClick={handleContinueToStrategy} size="lg" className="px-8">
          繼續配置策略
        </Button>
      </div>
    </div>
  )

  // 渲染步驟 3: 策略配置
  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
          <Shield className="w-8 h-8 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">選擇池子和策略</h3>
        <p className="text-gray-600">
          選擇您想要參與的池子，系統將自動配置最佳策略
        </p>
      </div>

      {/* 推薦池子列表 */}
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {proposal?.recommended_pools.map((pool, index) => (
          <Card 
            key={pool.pool_id}
            className={`cursor-pointer transition-all ${
              selectedPools.includes(pool.pool_id) 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:bg-gray-50'
            }`}
            onClick={() => togglePoolSelection(pool.pool_id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded border-2 ${
                    selectedPools.includes(pool.pool_id)
                      ? 'bg-blue-500 border-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedPools.includes(pool.pool_id) && (
                      <CheckCircle className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{pool.pool_id}</div>
                    <div className="text-sm text-gray-500">
                      <Badge variant="outline" className="mr-2 uppercase">
                        {pool.chain}
                      </Badge>
                      TVL: ${(pool.tvl_usd / 1000000).toFixed(1)}M
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-green-600">
                    {(pool.fee_tvl_pct * 365 * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-500">APR</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep(2)}>
          返回
        </Button>
        <Button 
          onClick={handleConfirmLaunch}
          disabled={selectedPools.length === 0 || isLaunching}
          size="lg"
        >
          {isLaunching ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              啟動中...
            </>
          ) : (
            <>
              確認啟動 ({selectedPools.length} 個池子)
            </>
          )}
        </Button>
      </div>
    </div>
  )

  // 渲染步驟 4: 啟動確認
  const renderStep4 = () => (
    <div className="space-y-6 text-center">
      <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      <h3 className="text-lg font-semibold mb-2">🎉 啟動成功！</h3>
      <p className="text-gray-600">
        DyFlow v3.4 系統已成功啟動，開始監控 {selectedPools.length} 個池子
      </p>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <div className="text-sm text-green-800">
          系統將在 3 秒後自動關閉此對話框...
        </div>
      </div>
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Rocket className="w-5 h-5" />
            <span>DyFlow v3.4 啟動引導</span>
          </DialogTitle>
          <DialogDescription>
            四步驟快速啟動您的自動化 LP 策略系統
          </DialogDescription>
        </DialogHeader>

        {/* 步驟指示器 */}
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${currentStep >= step.id 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-500'
                }
              `}>
                {currentStep > step.id ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  step.id
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  w-16 h-0.5 mx-2
                  ${currentStep > step.id ? 'bg-blue-500' : 'bg-gray-200'}
                `} />
              )}
            </div>
          ))}
        </div>

        {/* 步驟內容 */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
          </motion.div>
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
