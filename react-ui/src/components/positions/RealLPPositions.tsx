/**
 * 真實 LP 持倉組件
 * 顯示用戶的真實 LP 持倉數據
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Wallet, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  AlertTriangle,
  Eye,
  X,
  RefreshCw,
  Plus,
  Settings
} from 'lucide-react'
import { realDataService, RealLPPosition } from '../../services/realDataService'

export const RealLPPositions: React.FC = () => {
  const [positions, setPositions] = useState<RealLPPosition[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedPosition, setSelectedPosition] = useState<string | null>(null)

  useEffect(() => {
    loadPositions()
  }, [])

  const loadPositions = async () => {
    setLoading(true)
    try {
      // 模擬用戶地址，實際應該從錢包獲取
      const userAddress = '******************************************'
      const positionsData = await realDataService.getUserLPPositions(userAddress)
      setPositions(positionsData)
    } catch (error) {
      console.error('Error loading positions:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(2)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  }

  const getPnLColor = (value: number) => {
    return value >= 0 ? 'text-emerald-600' : 'text-rose-600'
  }

  const getILColor = (value: number) => {
    if (value <= -5) return 'text-rose-600 font-bold'
    if (value <= -2) return 'text-amber-600'
    return 'text-gray-600'
  }

  const getChainBadge = (chain: string) => {
    return chain === 'bsc' 
      ? <Badge className="bg-amber-100 text-amber-800" variant="secondary">BSC</Badge>
      : <Badge className="bg-purple-100 text-purple-800" variant="secondary">SOL</Badge>
  }

  const getStatusBadge = (status: string) => {
    return status === 'active'
      ? <Badge className="bg-emerald-100 text-emerald-800" variant="secondary">活躍</Badge>
      : <Badge className="bg-gray-100 text-gray-800" variant="secondary">已關閉</Badge>
  }

  const totalValue = positions.reduce((sum, pos) => sum + pos.liquidityUsd, 0)
  const totalPnL = positions.reduce((sum, pos) => sum + pos.pnlUsd, 0)
  const totalFees = positions.reduce((sum, pos) => sum + pos.feesUsd, 0)
  const totalIL = positions.reduce((sum, pos) => sum + pos.ilUsd, 0)
  const activePositions = positions.filter(pos => pos.status === 'active').length

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Wallet className="w-5 h-5 text-emerald-500" />
            <CardTitle className="text-lg font-bold">LP 持倉管理</CardTitle>
            <Badge variant="outline" className="text-xs">
              {activePositions} 活躍
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadPositions}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700">
              <Plus className="w-4 h-4 mr-1" />
              新增持倉
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* 總覽統計 */}
        <div className="px-6 pb-4">
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">總價值</div>
              <div className="text-lg font-bold text-gray-900">{formatCurrency(totalValue)}</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">總 PnL</div>
              <div className={`text-lg font-bold ${getPnLColor(totalPnL)}`}>
                {formatCurrency(totalPnL)}
              </div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">費用收益</div>
              <div className="text-lg font-bold text-emerald-600">{formatCurrency(totalFees)}</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-500">總 IL</div>
              <div className={`text-lg font-bold ${getILColor(totalIL / totalValue * 100)}`}>
                {formatCurrency(totalIL)}
              </div>
            </div>
          </div>
        </div>

        {/* 持倉列表 */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left font-medium text-gray-700">池子</th>
                <th className="px-4 py-3 text-right font-medium text-gray-700">價值</th>
                <th className="px-4 py-3 text-right font-medium text-gray-700">PnL</th>
                <th className="px-4 py-3 text-right font-medium text-gray-700">IL</th>
                <th className="px-4 py-3 text-right font-medium text-gray-700">費用</th>
                <th className="px-4 py-3 text-right font-medium text-gray-700">APR</th>
                <th className="px-4 py-3 text-left font-medium text-gray-700">狀態</th>
                <th className="px-4 py-3 text-left font-medium text-gray-700">操作</th>
              </tr>
            </thead>
            <tbody>
              {positions.map((position) => (
                <tr 
                  key={position.id} 
                  className={`border-b border-gray-100 hover:bg-gray-50 ${
                    selectedPosition === position.id ? 'bg-blue-50' : ''
                  }`}
                >
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      {getChainBadge(position.chain)}
                      <div>
                        <div className="font-medium">
                          {position.token0Amount.toFixed(2)} / {position.token1Amount.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Range: {position.tickLower} - {position.tickUpper}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="font-medium">{formatCurrency(position.liquidityUsd)}</div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className={`font-medium ${getPnLColor(position.pnlUsd)}`}>
                      {formatCurrency(position.pnlUsd)}
                    </div>
                    <div className={`text-xs ${getPnLColor(position.pnlPct)}`}>
                      {formatPercentage(position.pnlPct)}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className={`font-medium ${getILColor(position.ilPct)}`}>
                      {formatCurrency(position.ilUsd)}
                    </div>
                    <div className={`text-xs ${getILColor(position.ilPct)}`}>
                      {formatPercentage(position.ilPct)}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="font-medium text-emerald-600">
                      {formatCurrency(position.feesUsd)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {position.fees0.toFixed(4)} / {position.fees1.toFixed(2)}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="font-bold text-emerald-600">
                      {position.apr.toFixed(1)}%
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    {getStatusBadge(position.status)}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-1">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="text-xs px-2 py-1"
                        onClick={() => setSelectedPosition(
                          selectedPosition === position.id ? null : position.id
                        )}
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        {selectedPosition === position.id ? '收起' : '詳情'}
                      </Button>
                      
                      {position.status === 'active' && (
                        <>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="text-xs px-2 py-1"
                          >
                            <Settings className="w-3 h-3 mr-1" />
                            調整
                          </Button>
                          
                          <Button 
                            size="sm" 
                            variant="destructive" 
                            className="text-xs px-2 py-1"
                          >
                            <X className="w-3 h-3 mr-1" />
                            關閉
                          </Button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 詳細信息展開 */}
        {selectedPosition && (
          <div className="px-6 py-4 bg-blue-50 border-t">
            {(() => {
              const position = positions.find(p => p.id === selectedPosition)
              if (!position) return null
              
              return (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">持倉詳情</h4>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => setSelectedPosition(null)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">池子地址</div>
                      <div className="font-mono text-xs">{position.poolId}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Token ID</div>
                      <div className="font-medium">{position.tokenId || 'N/A'}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">流動性</div>
                      <div className="font-mono text-xs">{position.liquidity}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">創建時間</div>
                      <div>{new Date(position.createdAt).toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">最後更新</div>
                      <div>{new Date(position.lastUpdated).toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">用戶地址</div>
                      <div className="font-mono text-xs">
                        {position.userAddress.slice(0, 6)}...{position.userAddress.slice(-4)}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })()}
          </div>
        )}

        {positions.length === 0 && !loading && (
          <div className="text-center py-12">
            <Wallet className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暫無 LP 持倉</h3>
            <p className="text-gray-500 mb-4">開始您的第一個 LP 策略</p>
            <Button className="bg-emerald-600 hover:bg-emerald-700">
              <Plus className="w-4 h-4 mr-2" />
              創建 LP 持倉
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
