/**
 * DyFlow v3.4 簡化版佈局
 * 重新設計為用戶友好的界面，使用真實數據
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Play,
  Pause,
  MessageCircle,
  TrendingUp,
  Shield,
  Zap,
  Eye,
  Plus,
  RefreshCw,
  Bot,
  User,
  Send
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'
import { realDataService } from '../../services/realDataService.js'
import { agnoWorkflowService } from '../../services/agnoWorkflowService.js'

export const DyFlowV34Simple = () => {
  const [chatOpen, setChatOpen] = useState(true)
  const [chatMessages, setChatMessages] = useState([
    {
      id: '1',
      type: 'agent',
      content: '👋 您好！我是 DyFlow v3.4 智能助手。我可以幫您分析最佳 LP 機會、管理風險和執行交易策略。',
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [bscPools, setBscPools] = useState([])
  const [solanaPools, setSolanaPools] = useState([])
  const [poolsLoading, setPoolsLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState(new Date())

  const systemOverview = useAgnoStore(state => state.systemOverview)
  const tradingMode = useAgnoStore(state => state.tradingMode)
  const workflowRunning = useAgnoStore(state => state.workflowRunning)
  const connectionStatus = useAgnoStore(state => state.connectionStatus)
  const hotPools = useAgnoStore(state => state.hotPools)

  // 載入真實池子數據
  useEffect(() => {
    loadRealPoolData()

    // 每 30 秒自動更新真實數據
    const interval = setInterval(loadRealPoolData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadRealPoolData = async () => {
    setPoolsLoading(true)
    try {
      console.log('🔄 開始載入篩選後的真實池子數據...')
      console.log('📋 篩選條件: TVL >= $10M | Fee/TVL >= 5% | 24h費用 > $5 | 包含 preferred tokens')

      const [bscPoolsData, solanaPoolsData] = await Promise.all([
        realDataService.getBscPools(),
        realDataService.getSolanaPools()
      ])

      setBscPools(bscPoolsData)
      setSolanaPools(solanaPoolsData)
      setLastUpdate(new Date())

      console.log(`✅ 成功載入篩選後的池子:`)
      console.log(`   BSC 池子: ${bscPoolsData.length} 個`)
      console.log(`   Solana 池子: ${solanaPoolsData.length} 個`)
      console.log(`   總計: ${bscPoolsData.length + solanaPoolsData.length} 個`)

    } catch (error) {
      console.error('❌ 載入真實數據失敗:', error)
    } finally {
      setPoolsLoading(false)
    }
  }

  const formatCurrency = (value) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const getTradingModeColor = (mode) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500'
      case 'exit_only': return 'bg-amber-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTradingModeText = (mode) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  // 一鍵啟動功能 - 連接真實的 Agno Workflow
  const handleOneClickStart = async () => {
    try {
      console.log('🚀 一鍵啟動 DyFlow 系統...')

      // 添加啟動開始消息
      const startMessage = {
        id: Date.now().toString(),
        type: 'agent',
        content: '🚀 正在啟動 DyFlow v3.4 系統...\n\n🔍 檢查 Agno Workflow 服務連接...',
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, startMessage])
      setChatOpen(true)

      // 嘗試連接真實的 Agno Workflow
      const workflowResult = await agnoWorkflowService.startWorkflow()

      let statusMessage
      if (workflowResult.success) {
        // 真實 Workflow 啟動成功
        statusMessage = {
          id: (Date.now() + 1).toString(),
          type: 'agent',
          content: `✅ Agno Workflow 啟動成功！\n\n📋 Workflow ID: ${workflowResult.workflowId}\n\n🎯 啟動序列:\n1. ✅ 連接 Agno Workflow (port 8001)\n2. ✅ 初始化 MarketIntel Agent (15s 間隔)\n3. ✅ 啟動 Strategy Agent\n4. ✅ 準備 Execution Agent\n5. ✅ 激活 RiskSentinel Agent (10s 監控)\n\n🔄 系統已準備就緒，開始監控 LP 機會！\n\n⚙️ 配置:\n• BSC Preferred tokens: BNB, USDC, USD1, USDT\n• Solana Preferred tokens: ETH, WBTC, WSOL, SOL, USDT, USDC\n• IL 熔斷線: -8%\n• VaR 95% 熔斷線: 4%`,
          timestamp: new Date()
        }
      } else {
        // 使用模擬模式
        const simulationResult = await agnoWorkflowService.simulateWorkflowStart()
        statusMessage = {
          id: (Date.now() + 1).toString(),
          type: 'agent',
          content: `⚠️ Agno Workflow 服務不可用，使用模擬模式\n\n🎭 模擬 Workflow ID: ${simulationResult.workflowId}\n\n📋 啟動序列:\n1. ✅ 模擬 Agno Workflow 連接\n2. ✅ 初始化 MarketIntel Agent\n3. ✅ 啟動 Strategy Agent\n4. ✅ 準備 Execution Agent\n5. ✅ 激活 RiskSentinel Agent\n\n🔄 系統已準備就緒（模擬模式），開始監控 LP 機會！\n\n💡 提示: 要使用真實 Agno Workflow，請確保服務運行在 localhost:8001`,
          timestamp: new Date()
        }
      }

      setChatMessages(prev => [...prev, statusMessage])

      // 觸發數據載入
      await loadRealPoolData()

      // 更新 Zustand store 狀態
      const { setWorkflowRunning, setConnectionStatus } = useAgnoStore.getState()
      setWorkflowRunning(true)
      setConnectionStatus('connected')

    } catch (error) {
      console.error('❌ 啟動失敗:', error)

      const errorMessage = {
        id: Date.now().toString(),
        type: 'agent',
        content: `❌ 系統啟動失敗: ${error.message}\n\n請檢查:\n• Agno Workflow 服務是否運行在 localhost:8001\n• 網絡連接是否正常\n• 防火牆設置是否正確`,
        timestamp: new Date()
      }

      setChatMessages(prev => [...prev, errorMessage])
    }
  }

  // 添加 LP 持倉功能
  const handleAddPosition = (pool) => {
    console.log('➕ 添加 LP 持倉:', pool)

    const addMessage = {
      id: Date.now().toString(),
      type: 'agent',
      content: `➕ 準備添加 LP 持倉:\n\n🎯 池子: ${pool.pair}\n💰 TVL: ${formatCurrency(pool.tvlUsd)}\n📈 APR: ${pool.apr.toFixed(1)}%\n💵 24h費用: ${formatCurrency(pool.fees24hUsd)}\n\n⚠️ 注意: 這是演示模式，實際交易功能需要連接錢包。`,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, addMessage])
    setChatOpen(true) // 打開聊天窗口顯示消息
  }

  // 創建 LP 持倉功能
  const handleCreatePosition = () => {
    const createMessage = {
      id: Date.now().toString(),
      type: 'agent',
      content: '🎯 創建新的 LP 持倉:\n\n請從上方的池子列表中選擇一個合適的池子，或者告訴我您的偏好:\n• 風險偏好 (低/中/高)\n• 預期收益率\n• 投資金額\n• 持有時間\n\n我會為您推薦最適合的 LP 策略。',
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, createMessage])
    setChatOpen(true)
  }

  // 新增持倉功能
  const handleNewPosition = () => {
    const newMessage = {
      id: Date.now().toString(),
      type: 'agent',
      content: '➕ 新增 LP 持倉:\n\n當前可用的高質量池子:\n\n🟡 BSC 推薦:\n• DMX/BTCB - APR: 1,593,908.8%\n• WRB/BTCB - APR: 550,246.8%\n• BTCB/TRX - APR: 118,588.8%\n\n🟣 Solana 推薦:\n• THDR/SOL - APR: 30,208.6%\n• ELITE/SOL - APR: 18,420.9%\n• VIBE/USDC - APR: 12,031.8%\n\n請點擊池子旁邊的 ➕ 按鈕來添加持倉。',
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, newMessage])
    setChatOpen(true)
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, userMessage])
    const currentInput = inputValue
    setInputValue('')

    try {
      // 調用真正的 DyFlow CoreAgent API
      const response = await fetch('http://localhost:8001/api/agent/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput,
          timestamp: new Date().toISOString()
        })
      })

      if (response.ok) {
        const result = await response.json()
        const agentResponse = {
          id: (Date.now() + 1).toString(),
          type: 'agent',
          content: result.response || '收到您的指令，正在處理中...',
          timestamp: new Date(),
          agent: result.agent || 'DyFlow Agent',
          mode: result.mode || 'unknown'
        }
        setChatMessages(prev => [...prev, agentResponse])
      } else {
        throw new Error(`API 響應失敗: ${response.status}`)
      }
    } catch (error) {
      console.error('發送消息失敗:', error)
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: `❌ 抱歉，我暫時無法處理您的請求。\n\n錯誤信息: ${error.message}\n\n請檢查:\n• DyFlow Agent 服務是否運行在 localhost:8001\n• Ollama qwen2.5:3b 模型是否可用\n• 網絡連接是否正常`,
        timestamp: new Date(),
        agent: 'System Error',
        mode: 'error'
      }
      setChatMessages(prev => [...prev, errorMessage])
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部控制欄 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左側：品牌和狀態 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">DyFlow v3.4</h1>
                <p className="text-sm text-gray-500">24/7 自動化 LP 策略系統</p>
              </div>
            </div>

            {/* 系統狀態指示器 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-emerald-500' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium text-gray-600">
                  {connectionStatus === 'connected' ? '已連接' : '未連接'}
                </span>
              </div>
              
              <Badge className={`${getTradingModeColor(tradingMode)} text-white`}>
                {getTradingModeText(tradingMode)}
              </Badge>
            </div>
          </div>

          {/* 中間：關鍵指標 */}
          <div className="flex items-center space-x-8">
            <div className="text-center">
              <div className="text-sm text-gray-500">總 NAV</div>
              <div className="text-lg font-bold text-gray-900">{formatCurrency(systemOverview.nav)}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">24h 收益</div>
              <div className={`text-lg font-bold ${systemOverview.nav24hPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {systemOverview.nav24hPct >= 0 ? '+' : ''}{systemOverview.nav24hPct.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">活躍持倉</div>
              <div className="text-lg font-bold text-gray-900">{systemOverview.activePositions}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">風險分數</div>
              <div className={`text-lg font-bold ${systemOverview.riskScore >= 80 ? 'text-emerald-600' : systemOverview.riskScore >= 60 ? 'text-amber-600' : 'text-rose-600'}`}>
                {systemOverview.riskScore.toFixed(0)}
              </div>
            </div>
          </div>

          {/* 右側：控制按鈕 */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setChatOpen(!chatOpen)}
              className={chatOpen ? 'bg-blue-50 border-blue-200' : ''}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Agent 對話
            </Button>

            {!workflowRunning ? (
              <Button
                onClick={handleOneClickStart}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-6"
              >
                <Play className="w-4 h-4 mr-2" />
                一鍵啟動
              </Button>
            ) : (
              <Button
                variant="destructive"
                className="px-6"
              >
                <Pause className="w-4 h-4 mr-2" />
                停止系統
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* 主要內容區域 */}
      <main className="flex h-[calc(100vh-80px)]">
        {/* 左側：主要監控面板 (70%) */}
        <div className="flex-1 p-6 space-y-6 overflow-y-auto">
          {/* BSC 和 Solana 池子並排顯示 */}
          <div className="grid grid-cols-2 gap-6">
            {/* BSC 池子監控 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">B</span>
                    </div>
                    <div>
                      <CardTitle className="text-lg font-bold">BSC 池子</CardTitle>
                      <div className="text-xs text-gray-500">PancakeSwap V3</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {bscPools.length} 個
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={loadRealPoolData}
                      disabled={poolsLoading}
                    >
                      <RefreshCw className={`w-4 h-4 ${poolsLoading ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  篩選條件: TVL &gt;= $10M | Fee/TVL &gt;= 5% | 24h費用 &gt; $5 | 包含 preferred tokens (BNB, USDC, USD1, USDT)
                  {lastUpdate && ` | 更新: ${lastUpdate.toLocaleTimeString()}`}
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto max-h-96 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-amber-50 sticky top-0">
                      <tr>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">池子</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">TVL</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">APR</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">24h費用</th>
                        <th className="px-3 py-2 text-center font-medium text-gray-700">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {bscPools.length > 0 ? bscPools.slice(0, 20).map((pool) => (
                        <tr key={pool.id} className="border-b border-gray-100 hover:bg-amber-50">
                          <td className="px-3 py-2">
                            <div>
                              <div className="font-medium text-sm">{pool.pair}</div>
                              <div className="text-xs text-gray-500">BSC</div>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-medium text-sm">{formatCurrency(pool.tvlUsd)}</div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-bold text-emerald-600 text-sm">
                              {pool.apr.toFixed(1)}%
                            </div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-medium text-sm">{formatCurrency(pool.fees24hUsd)}</div>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <Button
                              size="sm"
                              onClick={() => handleAddPosition(pool)}
                              className="text-xs px-2 py-1 bg-emerald-600 hover:bg-emerald-700"
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="5" className="px-3 py-8 text-center text-gray-500">
                            {poolsLoading ? '載入中...' : '暫無符合條件的池子'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Solana 池子監控 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">S</span>
                    </div>
                    <div>
                      <CardTitle className="text-lg font-bold">Solana 池子</CardTitle>
                      <div className="text-xs text-gray-500">Meteora DLMM v2</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {solanaPools.length} 個
                    </Badge>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  篩選條件: TVL &gt;= $10K | APR &gt;= 1% | 24h費用 &gt; $1 | 包含 preferred tokens (ETH, WBTC, WSOL, SOL, USDT, USDC)
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto max-h-96 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-purple-50 sticky top-0">
                      <tr>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">池子</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">TVL</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">APR</th>
                        <th className="px-3 py-2 text-right font-medium text-gray-700">24h費用</th>
                        <th className="px-3 py-2 text-center font-medium text-gray-700">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {solanaPools.length > 0 ? solanaPools.slice(0, 20).map((pool) => (
                        <tr key={pool.id} className="border-b border-gray-100 hover:bg-purple-50">
                          <td className="px-3 py-2">
                            <div>
                              <div className="font-medium text-sm">{pool.pair}</div>
                              <div className="text-xs text-gray-500">SOL</div>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-medium text-sm">{formatCurrency(pool.tvlUsd)}</div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-bold text-emerald-600 text-sm">
                              {pool.apr.toFixed(1)}%
                            </div>
                          </td>
                          <td className="px-3 py-2 text-right">
                            <div className="font-medium text-sm">{formatCurrency(pool.fees24hUsd)}</div>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <Button
                              size="sm"
                              onClick={() => handleAddPosition(pool)}
                              className="text-xs px-2 py-1 bg-emerald-600 hover:bg-emerald-700"
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="5" className="px-3 py-8 text-center text-gray-500">
                            {poolsLoading ? '載入中...' : '暫無符合條件的池子'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* LP 持倉管理 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-bold">LP 持倉管理</CardTitle>
                <Button
                  size="sm"
                  onClick={handleNewPosition}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  新增持倉
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <Shield className="w-6 h-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暫無 LP 持倉</h3>
                <p className="text-gray-500 mb-4">開始您的第一個 LP 策略</p>
                <Button
                  onClick={handleCreatePosition}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  創建 LP 持倉
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 風險監控 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-bold">風險監控</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">IL 風險</div>
                  <div className="text-lg font-bold text-gray-900">-1.9%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">VaR 95%</div>
                  <div className="text-lg font-bold text-gray-900">2.8%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">最大回撤</div>
                  <div className="text-lg font-bold text-gray-900">-3.2%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">夏普比率</div>
                  <div className="text-lg font-bold text-emerald-600">1.85</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右側：Agent 交互面板 (30%) */}
        {chatOpen && (
          <div className="w-96 border-l border-gray-200 bg-white flex flex-col">
            {/* Agent 對話區域 */}
            <div className="flex-1 flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">DyFlow Agent</div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                      <span className="text-xs text-gray-500">在線</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 消息列表 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                      <div
                        className={`rounded-lg px-3 py-2 text-sm ${
                          message.type === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      
                      <div className={`text-xs text-gray-500 mt-1 ${
                        message.type === 'user' ? 'text-right' : 'text-left'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      message.type === 'user' ? 'order-1 mr-2 bg-blue-100' : 'order-2 ml-2 bg-gray-200'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="w-3 h-3 text-blue-600" />
                      ) : (
                        <Bot className="w-3 h-3 text-gray-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 輸入區域 */}
              <div className="border-t p-4">
                <div className="flex items-center space-x-2">
                  <input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="輸入消息..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button 
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    size="sm"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* 快速指令 */}
                <div className="mt-2 flex flex-wrap gap-1">
                  {['掃描池子', '檢查持倉', '風險分析'].map((cmd) => (
                    <button
                      key={cmd}
                      onClick={async () => {
                        // 先設置輸入值
                        setInputValue(cmd)

                        // 等待狀態更新後再發送消息
                        setTimeout(async () => {
                          const userMessage = {
                            id: Date.now().toString(),
                            type: 'user',
                            content: cmd,
                            timestamp: new Date()
                          }

                          setChatMessages(prev => [...prev, userMessage])

                          try {
                            // 調用真正的 DyFlow CoreAgent API
                            const response = await fetch('http://localhost:8001/api/agent/chat', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify({
                                message: cmd,
                                timestamp: new Date().toISOString()
                              })
                            })

                            if (response.ok) {
                              const result = await response.json()
                              const agentResponse = {
                                id: (Date.now() + 1).toString(),
                                type: 'agent',
                                content: result.response || '收到您的指令，正在處理中...',
                                timestamp: new Date(),
                                agent: result.agent || 'DyFlow Agent',
                                mode: result.mode || 'unknown'
                              }
                              setChatMessages(prev => [...prev, agentResponse])
                            } else {
                              throw new Error(`API 響應失敗: ${response.status}`)
                            }
                          } catch (error) {
                            console.error('發送消息失敗:', error)
                            const errorMessage = {
                              id: (Date.now() + 1).toString(),
                              type: 'agent',
                              content: `❌ 抱歉，我暫時無法處理您的請求。\n\n錯誤信息: ${error.message}\n\n請檢查:\n• DyFlow Agent 服務是否運行在 localhost:8001\n• Ollama qwen2.5:3b 模型是否可用\n• 網絡連接是否正常`,
                              timestamp: new Date(),
                              agent: 'System Error',
                              mode: 'error'
                            }
                            setChatMessages(prev => [...prev, errorMessage])
                          }

                          // 清空輸入框
                          setInputValue('')
                        }, 100)
                      }}
                      className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {cmd}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
