/**
 * DyFlow v3.4 簡化版佈局
 * 重新設計為用戶友好的界面，使用真實數據
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Play,
  Pause,
  MessageCircle,
  TrendingUp,
  Shield,
  Zap,
  Eye,
  Plus,
  RefreshCw,
  Bot,
  User,
  Send
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'
import { realDataService } from '../../services/realDataService.js'

export const DyFlowV34Simple = () => {
  const [chatOpen, setChatOpen] = useState(true)
  const [chatMessages, setChatMessages] = useState([
    {
      id: '1',
      type: 'agent',
      content: '👋 您好！我是 DyFlow v3.4 智能助手。我可以幫您分析最佳 LP 機會、管理風險和執行交易策略。',
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [bscPools, setBscPools] = useState([])
  const [solanaPools, setSolanaPools] = useState([])
  const [poolsLoading, setPoolsLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState(new Date())

  const systemOverview = useAgnoStore(state => state.systemOverview)
  const tradingMode = useAgnoStore(state => state.tradingMode)
  const workflowRunning = useAgnoStore(state => state.workflowRunning)
  const connectionStatus = useAgnoStore(state => state.connectionStatus)
  const hotPools = useAgnoStore(state => state.hotPools)

  // 載入真實池子數據
  useEffect(() => {
    loadRealPoolData()

    // 每 30 秒自動更新真實數據
    const interval = setInterval(loadRealPoolData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadRealPoolData = async () => {
    setPoolsLoading(true)
    try {
      console.log('🔄 開始載入篩選後的真實池子數據...')
      console.log('📋 篩選條件: TVL >= $10M | Fee/TVL >= 5.00% | 24h Fees > $5')

      const [bscPoolsData, solanaPoolsData] = await Promise.all([
        realDataService.getBscPools(),
        realDataService.getSolanaPools()
      ])

      setBscPools(bscPoolsData)
      setSolanaPools(solanaPoolsData)
      setLastUpdate(new Date())

      console.log(`✅ 成功載入篩選後的池子:`)
      console.log(`   BSC 池子: ${bscPoolsData.length} 個`)
      console.log(`   Solana 池子: ${solanaPoolsData.length} 個`)
      console.log(`   總計: ${bscPoolsData.length + solanaPoolsData.length} 個`)

    } catch (error) {
      console.error('❌ 載入真實數據失敗:', error)
    } finally {
      setPoolsLoading(false)
    }
  }

  const formatCurrency = (value) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const getTradingModeColor = (mode) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500'
      case 'exit_only': return 'bg-amber-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTradingModeText = (mode) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, userMessage])
    setInputValue('')

    // 使用真實數據的 Agent 回應
    setTimeout(() => {
      const allRealPools = [...bscPools, ...solanaPools]
      const poolsToAnalyze = allRealPools.length > 0 ? allRealPools : hotPools
      const avgAPR = poolsToAnalyze.length > 0
        ? poolsToAnalyze.reduce((sum, p) => sum + p.apr, 0) / poolsToAnalyze.length
        : 0

      const topPool = poolsToAnalyze.length > 0
        ? poolsToAnalyze.reduce((max, p) => p.apr > max.apr ? p : max)
        : null

      let responseContent = `🤖 收到您的指令："${inputValue}"。正在分析當前市場狀況...\n\n`

      if (poolsToAnalyze.length > 0) {
        responseContent += `📊 篩選後發現 ${poolsToAnalyze.length} 個高質量池子\n`
        responseContent += `   • BSC: ${bscPools.length} 個\n`
        responseContent += `   • Solana: ${solanaPools.length} 個\n`
        responseContent += `   • 平均 APR: ${avgAPR.toFixed(1)}%\n\n`

        if (topPool) {
          responseContent += `🏆 最高收益池子: ${topPool.pair} (${topPool.chain.toUpperCase()})\n`
          responseContent += `   • APR: ${topPool.apr.toFixed(1)}%\n`
          responseContent += `   • TVL: ${formatCurrency(topPool.tvlUsd)}\n`
          responseContent += `   • 24h 費用: ${formatCurrency(topPool.fees24hUsd)}\n\n`
        }

        responseContent += `📋 篩選條件: TVL >= $10M | Fee/TVL >= 5% | 24h費用 > $5\n\n`
        responseContent += `需要我為您制定 LP 策略嗎？`
      } else {
        responseContent += `⏳ 正在載入篩選後的真實市場數據，請稍候...`
      }

      const agentResponse = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: responseContent,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, agentResponse])
    }, 1000)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部控制欄 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左側：品牌和狀態 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">DyFlow v3.4</h1>
                <p className="text-sm text-gray-500">24/7 自動化 LP 策略系統</p>
              </div>
            </div>

            {/* 系統狀態指示器 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-emerald-500' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium text-gray-600">
                  {connectionStatus === 'connected' ? '已連接' : '未連接'}
                </span>
              </div>
              
              <Badge className={`${getTradingModeColor(tradingMode)} text-white`}>
                {getTradingModeText(tradingMode)}
              </Badge>
            </div>
          </div>

          {/* 中間：關鍵指標 */}
          <div className="flex items-center space-x-8">
            <div className="text-center">
              <div className="text-sm text-gray-500">總 NAV</div>
              <div className="text-lg font-bold text-gray-900">{formatCurrency(systemOverview.nav)}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">24h 收益</div>
              <div className={`text-lg font-bold ${systemOverview.nav24hPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {systemOverview.nav24hPct >= 0 ? '+' : ''}{systemOverview.nav24hPct.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">活躍持倉</div>
              <div className="text-lg font-bold text-gray-900">{systemOverview.activePositions}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">風險分數</div>
              <div className={`text-lg font-bold ${systemOverview.riskScore >= 80 ? 'text-emerald-600' : systemOverview.riskScore >= 60 ? 'text-amber-600' : 'text-rose-600'}`}>
                {systemOverview.riskScore.toFixed(0)}
              </div>
            </div>
          </div>

          {/* 右側：控制按鈕 */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setChatOpen(!chatOpen)}
              className={chatOpen ? 'bg-blue-50 border-blue-200' : ''}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Agent 對話
            </Button>

            {!workflowRunning ? (
              <Button
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-6"
              >
                <Play className="w-4 h-4 mr-2" />
                一鍵啟動
              </Button>
            ) : (
              <Button
                variant="destructive"
                className="px-6"
              >
                <Pause className="w-4 h-4 mr-2" />
                停止系統
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* 主要內容區域 */}
      <main className="flex h-[calc(100vh-80px)]">
        {/* 左側：主要監控面板 (70%) */}
        <div className="flex-1 p-6 space-y-6 overflow-y-auto">
          {/* BSC 池子監控 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">B</span>
                  </div>
                  <CardTitle className="text-lg font-bold">BSC 池子 (PancakeSwap V3)</CardTitle>
                  <Badge variant="outline" className="text-xs">
                    {bscPools.length} 個篩選後池子
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {bscPools.length > 0 ? '真實數據' : '載入中'}
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={loadRealPoolData}
                    disabled={poolsLoading}
                  >
                    <RefreshCw className={`w-4 h-4 ${poolsLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                篩選條件: TVL >= $10M | Fee/TVL >= 5% | 24h費用 > $5
                {lastUpdate && ` | 更新: ${lastUpdate.toLocaleTimeString()}`}
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-amber-50">
                    <tr>
                      <th className="px-4 py-3 text-left font-medium text-gray-700">池子</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">TVL</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">APR</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">24h 費用</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">創建時間</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bscPools.length > 0 ? bscPools.map((pool) => (
                      <tr key={pool.id} className="border-b border-gray-100 hover:bg-amber-50">
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <Badge className="bg-amber-100 text-amber-800" variant="secondary">
                              BSC
                            </Badge>
                            <div>
                              <div className="font-medium">{pool.pair}</div>
                              <div className="text-xs text-gray-500">PancakeSwap V3</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-medium">{formatCurrency(pool.tvlUsd)}</div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-bold text-emerald-600">
                            {pool.apr.toFixed(1)}%
                          </div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-medium">{formatCurrency(pool.fees24hUsd)}</div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="text-xs text-gray-500">
                            {new Date(pool.createdAt).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" className="text-xs px-2 py-1">
                              <Eye className="w-3 h-3 mr-1" />
                              查看
                            </Button>
                            <Button size="sm" className="text-xs px-2 py-1 bg-emerald-600 hover:bg-emerald-700">
                              <Plus className="w-3 h-3 mr-1" />
                              添加
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="6" className="px-4 py-8 text-center text-gray-500">
                          {poolsLoading ? '正在載入 BSC 池子數據...' : '暫無符合篩選條件的 BSC 池子'}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Solana 池子監控 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">S</span>
                  </div>
                  <CardTitle className="text-lg font-bold">Solana 池子 (Meteora DLMM)</CardTitle>
                  <Badge variant="outline" className="text-xs">
                    {solanaPools.length} 個篩選後池子
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {solanaPools.length > 0 ? '真實數據' : '載入中'}
                  </Badge>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                篩選條件: TVL >= $10M | Fee/TVL >= 5% | 24h費用 > $5
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-purple-50">
                    <tr>
                      <th className="px-4 py-3 text-left font-medium text-gray-700">池子</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">TVL</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">APR</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">24h 費用</th>
                      <th className="px-4 py-3 text-right font-medium text-gray-700">創建時間</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {solanaPools.length > 0 ? solanaPools.map((pool) => (
                      <tr key={pool.id} className="border-b border-gray-100 hover:bg-purple-50">
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <Badge className="bg-purple-100 text-purple-800" variant="secondary">
                              SOL
                            </Badge>
                            <div>
                              <div className="font-medium">{pool.pair}</div>
                              <div className="text-xs text-gray-500">Meteora DLMM</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-medium">{formatCurrency(pool.tvlUsd)}</div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-bold text-emerald-600">
                            {pool.apr.toFixed(1)}%
                          </div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="font-medium">{formatCurrency(pool.fees24hUsd)}</div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="text-xs text-gray-500">
                            {new Date(pool.createdAt).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" className="text-xs px-2 py-1">
                              <Eye className="w-3 h-3 mr-1" />
                              查看
                            </Button>
                            <Button size="sm" className="text-xs px-2 py-1 bg-emerald-600 hover:bg-emerald-700">
                              <Plus className="w-3 h-3 mr-1" />
                              添加
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="6" className="px-4 py-8 text-center text-gray-500">
                          {poolsLoading ? '正在載入 Solana 池子數據...' : '暫無符合篩選條件的 Solana 池子'}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* LP 持倉管理 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-bold">LP 持倉管理</CardTitle>
                <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700">
                  <Plus className="w-4 h-4 mr-1" />
                  新增持倉
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <Shield className="w-6 h-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暫無 LP 持倉</h3>
                <p className="text-gray-500 mb-4">開始您的第一個 LP 策略</p>
                <Button className="bg-emerald-600 hover:bg-emerald-700">
                  <Plus className="w-4 h-4 mr-2" />
                  創建 LP 持倉
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 風險監控 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-bold">風險監控</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">IL 風險</div>
                  <div className="text-lg font-bold text-gray-900">-1.9%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">VaR 95%</div>
                  <div className="text-lg font-bold text-gray-900">2.8%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">最大回撤</div>
                  <div className="text-lg font-bold text-gray-900">-3.2%</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg text-center">
                  <div className="text-sm text-gray-500">夏普比率</div>
                  <div className="text-lg font-bold text-emerald-600">1.85</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右側：Agent 交互面板 (30%) */}
        {chatOpen && (
          <div className="w-96 border-l border-gray-200 bg-white flex flex-col">
            {/* Agent 對話區域 */}
            <div className="flex-1 flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">DyFlow Agent</div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                      <span className="text-xs text-gray-500">在線</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 消息列表 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                      <div
                        className={`rounded-lg px-3 py-2 text-sm ${
                          message.type === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      
                      <div className={`text-xs text-gray-500 mt-1 ${
                        message.type === 'user' ? 'text-right' : 'text-left'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      message.type === 'user' ? 'order-1 mr-2 bg-blue-100' : 'order-2 ml-2 bg-gray-200'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="w-3 h-3 text-blue-600" />
                      ) : (
                        <Bot className="w-3 h-3 text-gray-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 輸入區域 */}
              <div className="border-t p-4">
                <div className="flex items-center space-x-2">
                  <input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="輸入消息..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button 
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    size="sm"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* 快速指令 */}
                <div className="mt-2 flex flex-wrap gap-1">
                  {['掃描池子', '檢查持倉', '風險分析'].map((cmd) => (
                    <button
                      key={cmd}
                      onClick={() => {
                        setInputValue(cmd)
                        handleSendMessage()
                      }}
                      className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded border"
                    >
                      {cmd}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
