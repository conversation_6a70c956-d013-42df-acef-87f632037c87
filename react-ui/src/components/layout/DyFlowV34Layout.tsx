/**
 * DyFlow v3.4 主佈局
 * 重新設計為用戶友好的界面，符合 v3.4 PRD 規範
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Play, 
  Pause, 
  Settings, 
  MessageCircle,
  Activity,
  TrendingUp,
  Shield,
  Zap,
  Eye,
  AlertTriangle
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'
import { LaunchWizard } from '../LaunchWizard'
import { AgentChat } from '../agent/AgentChat'
import { AgentLogs } from '../agent/AgentLogs'
import { RealPoolMonitor } from '../pools/RealPoolMonitor'
import { RealLPPositions } from '../positions/RealLPPositions'
import { RealRiskMonitor } from '../risk/RealRiskMonitor'

export const DyFlowV34Layout: React.FC = () => {
  const [wizardOpen, setWizardOpen] = useState(false)
  const [chatOpen, setChatOpen] = useState(true)
  
  const systemOverview = useAgnoStore(state => state.systemOverview)
  const tradingMode = useAgnoStore(state => state.tradingMode)
  const workflowRunning = useAgnoStore(state => state.workflowRunning)
  const connectionStatus = useAgnoStore(state => state.connectionStatus)

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const getTradingModeColor = (mode: string) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500'
      case 'exit_only': return 'bg-amber-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTradingModeText = (mode: string) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  const getConnectionStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-emerald-500'
      case 'connecting': return 'text-amber-500'
      case 'disconnected': return 'text-gray-500'
      case 'error': return 'text-rose-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部控制欄 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左側：品牌和狀態 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">DyFlow v3.4</h1>
                <p className="text-sm text-gray-500">24/7 自動化 LP 策略系統</p>
              </div>
            </div>

            {/* 系統狀態指示器 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-emerald-500' : 'bg-gray-400'}`}></div>
                <span className={`text-sm font-medium ${getConnectionStatusColor(connectionStatus)}`}>
                  {connectionStatus === 'connected' ? '已連接' : '未連接'}
                </span>
              </div>
              
              <Badge className={`${getTradingModeColor(tradingMode)} text-white`}>
                {getTradingModeText(tradingMode)}
              </Badge>
            </div>
          </div>

          {/* 中間：關鍵指標 */}
          <div className="flex items-center space-x-8">
            <div className="text-center">
              <div className="text-sm text-gray-500">總 NAV</div>
              <div className="text-lg font-bold text-gray-900">{formatCurrency(systemOverview.nav)}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">24h 收益</div>
              <div className={`text-lg font-bold ${systemOverview.nav24hPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {systemOverview.nav24hPct >= 0 ? '+' : ''}{systemOverview.nav24hPct.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">活躍持倉</div>
              <div className="text-lg font-bold text-gray-900">{systemOverview.activePositions}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">風險分數</div>
              <div className={`text-lg font-bold ${systemOverview.riskScore >= 80 ? 'text-emerald-600' : systemOverview.riskScore >= 60 ? 'text-amber-600' : 'text-rose-600'}`}>
                {systemOverview.riskScore.toFixed(0)}
              </div>
            </div>
          </div>

          {/* 右側：控制按鈕 */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setChatOpen(!chatOpen)}
              className={chatOpen ? 'bg-blue-50 border-blue-200' : ''}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Agent 對話
            </Button>

            {!workflowRunning ? (
              <Button
                onClick={() => setWizardOpen(true)}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-6"
              >
                <Play className="w-4 h-4 mr-2" />
                一鍵啟動
              </Button>
            ) : (
              <Button
                variant="destructive"
                className="px-6"
              >
                <Pause className="w-4 h-4 mr-2" />
                停止系統
              </Button>
            )}

            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* 主要內容區域 */}
      <main className="flex h-[calc(100vh-80px)]">
        {/* 左側：主要監控面板 (70%) */}
        <div className="flex-1 p-6 space-y-6 overflow-y-auto">
          {/* 池子監控 */}
          <RealPoolMonitor />

          {/* LP 持倉管理 */}
          <RealLPPositions />

          {/* 風險監控 */}
          <RealRiskMonitor />
        </div>

        {/* 右側：Agent 交互面板 (30%) */}
        {chatOpen && (
          <div className="w-96 border-l border-gray-200 bg-white flex flex-col">
            {/* Agent 對話區域 */}
            <div className="flex-1 border-b border-gray-200">
              <AgentChat />
            </div>

            {/* Agent 日誌區域 */}
            <div className="h-64">
              <AgentLogs />
            </div>
          </div>
        )}
      </main>

      {/* LaunchWizard 模態框 */}
      <LaunchWizard 
        isOpen={wizardOpen} 
        onClose={() => setWizardOpen(false)} 
      />
    </div>
  )
}
