/**
 * DyFlow v3.4 真實數據服務 (JavaScript 版本)
 * 獲取真實的池子數據和 LP 持倉信息
 */

class RealDataService {
  constructor() {
    this.bscSubgraphUrl = 'https://gateway.thegraph.com/api/[API_KEY]/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ'
    this.meteoraApiUrl = 'https://dammv2-api.meteora.ag'
    this.coingeckoApiUrl = 'https://api.coingecko.com/api/v3'
    this.apiKey = '9731921233db132a98c2325878e6c153'

    this.cache = new Map()
    this.cacheTimeout = 30000 // 30 seconds

    // 來自 config/strategies.yaml 的 preferred_tokens
    this.preferredTokensBSC = [
      'BNB', 'USDC', 'USD1', 'USDT'
    ]
    this.preferredTokensSolana = [
      'ETH', 'WBTC', 'WSOL', 'USDT', 'USDC', 'SOL'
    ]
  }

  getFromCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    return null
  }

  setCache(key, data) {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  /**
   * 檢查池子是否包含 preferred tokens
   */
  isPreferredTokenPair(token0, token1, chain) {
    const preferredTokens = chain === 'bsc' ? this.preferredTokensBSC : this.preferredTokensSolana

    // 檢查是否至少有一個代幣在 preferred list 中
    const hasPreferredToken = preferredTokens.includes(token0) || preferredTokens.includes(token1)

    console.log(`🔍 檢查代幣對 ${token0}/${token1} (${chain}): ${hasPreferredToken ? '✅ 符合' : '❌ 不符合'} preferred tokens`)

    return hasPreferredToken
  }

  /**
   * 獲取 BSC PancakeSwap V3 池子數據
   * 篩選條件: TVL >= $10M | Fee/TVL >= 5.00% | 24h Fees > $5 | 包含 preferred tokens
   */
  async getBscPools(limit = 1000) {
    const cacheKey = `bsc_pools_filtered`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      // 使用正確的 API Key
      const subgraphUrl = this.bscSubgraphUrl.replace('[API_KEY]', this.apiKey)

      const query = `
        query GetPools($first: Int!) {
          pools(
            first: $first
            orderBy: totalValueLockedUSD
            orderDirection: desc
            where: {
              totalValueLockedUSD_gt: "10000000"
            }
          ) {
            id
            token0 {
              symbol
              name
              decimals
            }
            token1 {
              symbol
              name
              decimals
            }
            feeTier
            totalValueLockedUSD
            volumeUSD
            feesUSD
            sqrtPrice
            tick
            createdAtTimestamp
            liquidity
          }
        }
      `

      console.log('🔍 正在獲取 BSC 真實池子數據...')
      
      const response = await fetch(subgraphUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables: {
            first: limit
          }
        })
      })

      if (!response.ok) {
        console.error('BSC Subgraph HTTP error:', response.status, response.statusText)
        return this.getMockBscPools()
      }

      const result = await response.json()
      
      if (result.errors) {
        console.error('BSC Subgraph GraphQL errors:', result.errors)
        return this.getMockBscPools()
      }

      if (!result.data || !result.data.pools) {
        console.error('BSC Subgraph: No pools data returned')
        return this.getMockBscPools()
      }

      console.log(`✅ 成功獲取 ${result.data.pools.length} 個 BSC 池子`)

      const pools = result.data.pools
        .map((pool) => {
          const tvl = parseFloat(pool.totalValueLockedUSD) || 0
          const fees24h = parseFloat(pool.feesUSD) || 0
          const volume24h = parseFloat(pool.volumeUSD) || 0
          const apr = this.calculateAPR(fees24h, tvl)

          return {
            id: pool.id,
            chain: 'bsc',
            protocol: 'pancakeswap_v3',
            pair: `${pool.token0.symbol}/${pool.token1.symbol}`,
            token0: pool.token0.symbol,
            token1: pool.token1.symbol,
            address: pool.id,
            tvlUsd: tvl,
            apr: apr,
            fees24hUsd: fees24h,
            feeTier: parseInt(pool.feeTier) / 10000, // Convert to percentage
            volatility: this.estimateVolatility(volume24h, tvl),
            spread: parseInt(pool.feeTier) / 1000000, // Convert to decimal
            priceChange24h: (Math.random() - 0.5) * 10, // Mock price change for now
            volume24hUsd: volume24h,
            liquidityUsd: tvl,
            tick: parseInt(pool.tick) || 0,
            sqrtPrice: pool.sqrtPrice || '0',
            createdAt: pool.createdAtTimestamp ?
              new Date(parseInt(pool.createdAtTimestamp) * 1000).toISOString() :
              new Date().toISOString(),
            lastUpdated: new Date().toISOString()
          }
        })
        .filter((pool) => {
          // 應用篩選條件: TVL >= $10M | Fee/TVL >= 5.00% | 24h Fees > $5 | preferred tokens
          const meetsMinTvl = pool.tvlUsd >= 10000000 // $10M
          const meetsFeeRatio = pool.apr >= 5.0 // Fee/TVL >= 5%
          const meetsMinFees = pool.fees24hUsd > 5 // 24h Fees > $5
          const hasPreferredToken = this.isPreferredTokenPair(pool.token0, pool.token1, 'bsc')

          console.log(`🔍 池子 ${pool.pair}: TVL=${pool.tvlUsd/1000000}M, APR=${pool.apr.toFixed(1)}%, 費用=${pool.fees24hUsd}`)
          console.log(`   篩選結果: TVL✓=${meetsMinTvl}, APR✓=${meetsFeeRatio}, 費用✓=${meetsMinFees}, Token✓=${hasPreferredToken}`)

          return meetsMinTvl && meetsFeeRatio && meetsMinFees && hasPreferredToken
        })
        .sort((a, b) => b.apr - a.apr) // 按 APR 降序排列

      this.setCache(cacheKey, pools)
      return pools

    } catch (error) {
      console.error('❌ BSC 池子數據獲取失敗:', error)
      console.log('🔄 使用模擬數據作為備用')
      return this.getMockBscPools()
    }
  }

  /**
   * 獲取 Solana Meteora DLMM 池子數據
   * 篩選條件: TVL >= $1K | Fee/TVL >= 0.1% | 24h Fees > $0.1 | 包含 preferred tokens
   */
  async getSolanaPools(limit = 1000) {
    const cacheKey = `solana_pools_filtered`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      console.log('🔍 正在獲取 Solana 真實池子數據...')

      // 使用正確的 Meteora DLMM v2 API 端點
      const response = await fetch(`${this.meteoraApiUrl}/pools?limit=1000&order_by=tvl&order=desc`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        console.error('Meteora DLMM v2 API HTTP error:', response.status, response.statusText)
        return this.getMockSolanaPools()
      }

      const result = await response.json()

      if (!result || !result.data || !Array.isArray(result.data)) {
        console.error('Meteora DLMM v2 API: Invalid response format')
        return this.getMockSolanaPools()
      }

      console.log(`✅ 成功獲取 ${result.data.length} 個 Solana DLMM v2 池子`)

      const pools = result.data
        .filter((pool) => {
          // 基本數據檢查
          if (!pool.pool_address || !pool.token_a_symbol || !pool.token_b_symbol) return false

          // 使用正確的 DLMM v2 API 字段
          const tvl = parseFloat(pool.tvl) || 0
          const fees24h = parseFloat(pool.fee24h) || 0
          const apr = parseFloat(pool.apr) || 0

          // 調整篩選條件: TVL >= $10K | APR >= 1% | 24h Fees > $1 | preferred tokens
          const meetsMinTvl = tvl >= 10000 // $10K
          const meetsFeeRatio = apr >= 1.0 // APR >= 1%
          const meetsMinFees = fees24h > 1 // 24h Fees > $1
          const hasPreferredToken = this.isPreferredTokenPair(pool.token_a_symbol, pool.token_b_symbol, 'solana')

          console.log(`🔍 Solana 池子 ${pool.pool_name || `${pool.token_a_symbol}/${pool.token_b_symbol}`}: TVL=$${tvl.toFixed(2)}, APR=${apr.toFixed(1)}%, 費用=$${fees24h.toFixed(2)}`)
          console.log(`   篩選結果: TVL✓=${meetsMinTvl}, APR✓=${meetsFeeRatio}, 費用✓=${meetsMinFees}, Token✓=${hasPreferredToken}`)

          return meetsMinTvl && meetsFeeRatio && meetsMinFees && hasPreferredToken
        })
        .map((pool) => {
          const tvl = parseFloat(pool.tvl) || 0
          const fees24h = parseFloat(pool.fee24h) || 0
          const volume24h = parseFloat(pool.volume24h) || 0
          const apr = parseFloat(pool.apr) || 0

          // 使用 DLMM v2 API 的正確字段
          const pairName = pool.pool_name || `${pool.token_a_symbol}/${pool.token_b_symbol}`

          return {
            id: pool.pool_address,
            chain: 'solana',
            protocol: 'meteora_dlmm_v2',
            pair: pairName,
            token0: pool.token_a_symbol,
            token1: pool.token_b_symbol,
            address: pool.pool_address,
            tvlUsd: tvl,
            apr: apr,
            fees24hUsd: fees24h,
            feeTier: pool.base_fee ? parseFloat(pool.base_fee) / 100 : 0.001,
            volatility: this.estimateVolatility(volume24h, tvl),
            spread: pool.base_fee ? parseFloat(pool.base_fee) / 100 : 0.001,
            priceChange24h: (Math.random() - 0.5) * 10, // Mock price change
            volume24hUsd: volume24h,
            liquidityUsd: tvl,
            tick: 0, // DLMM doesn't use ticks
            sqrtPrice: pool.sqrt_price?.toString() || '0',
            createdAt: pool.created_at_slot_timestamp ?
              new Date(pool.created_at_slot_timestamp * 1000).toISOString() :
              new Date().toISOString(),
            lastUpdated: new Date().toISOString()
          }
        })
        .sort((a, b) => b.apr - a.apr) // 按 APR 降序排列

      this.setCache(cacheKey, pools)
      return pools

    } catch (error) {
      console.error('❌ Solana 池子數據獲取失敗:', error)
      console.log('🔄 使用模擬數據作為備用')
      return this.getMockSolanaPools()
    }
  }

  /**
   * 獲取用戶的真實 LP 持倉
   */
  async getUserLPPositions(userAddress) {
    // 這裡應該查詢用戶的實際 LP 持倉
    // 目前返回模擬數據
    return this.getMockLPPositions(userAddress)
  }

  /**
   * 計算 APR
   */
  calculateAPR(fees24h, tvl) {
    if (tvl === 0) return 0
    return (fees24h * 365 / tvl) * 100
  }

  /**
   * 估算波動率
   */
  estimateVolatility(volume24h, tvl) {
    if (tvl === 0) return 0
    const turnover = volume24h / tvl
    return Math.min(turnover * 0.1, 1) // 簡化的波動率估算
  }

  /**
   * Mock BSC 池子數據（當 API 失敗時使用）
   */
  getMockBscPools() {
    return [
      {
        id: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        chain: 'bsc',
        protocol: 'pancakeswap_v3',
        pair: 'BNB/USDT',
        token0: 'BNB',
        token1: 'USDT',
        address: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        tvlUsd: 15420000,
        apr: 125.5,
        fees24hUsd: 52800,
        feeTier: 0.0025,
        volatility: 0.25,
        spread: 0.0025,
        priceChange24h: 2.3,
        volume24hUsd: 8500000,
        liquidityUsd: 15420000,
        tick: 276324,
        sqrtPrice: '1234567890123456789',
        createdAt: '2024-01-15T10:30:00Z',
        lastUpdated: new Date().toISOString()
      },
      {
        id: '******************************************',
        chain: 'bsc',
        protocol: 'pancakeswap_v3',
        pair: 'ETH/USDC',
        token0: 'ETH',
        token1: 'USDC',
        address: '******************************************',
        tvlUsd: 8750000,
        apr: 89.2,
        fees24hUsd: 21400,
        feeTier: 0.0005,
        volatility: 0.18,
        spread: 0.0005,
        priceChange24h: -1.2,
        volume24hUsd: 4200000,
        liquidityUsd: 8750000,
        tick: 201234,
        sqrtPrice: '9876543210987654321',
        createdAt: '2024-01-20T14:15:00Z',
        lastUpdated: new Date().toISOString()
      }
    ]
  }

  /**
   * Mock Solana 池子數據（當 API 失敗時使用）
   */
  getMockSolanaPools() {
    return [
      {
        id: 'Hs97TCZeuYiJxooo3U73qEHXg3dKpRL9uYBH77j8XZLV',
        chain: 'solana',
        protocol: 'meteora_dlmm_v2',
        pair: 'SOL/USDC',
        token0: 'SOL',
        token1: 'USDC',
        address: 'Hs97TCZeuYiJxooo3U73qEHXg3dKpRL9uYBH77j8XZLV',
        tvlUsd: 12300000,
        apr: 185.2,
        fees24hUsd: 62400,
        feeTier: 0.002,
        volatility: 0.45,
        spread: 0.002,
        priceChange24h: 3.8,
        volume24hUsd: 15600000,
        liquidityUsd: 12300000,
        tick: 0,
        sqrtPrice: '150.25',
        createdAt: '2024-02-01T09:20:00Z',
        lastUpdated: new Date().toISOString()
      }
    ]
  }

  /**
   * Mock LP 持倉數據
   */
  getMockLPPositions(userAddress) {
    return [
      {
        id: 'pos_1',
        poolId: '0x36696169c63e42cd08ce11f5deebbcebae652050',
        chain: 'bsc',
        userAddress,
        tokenId: '12345',
        liquidity: '1000000000000000000',
        liquidityUsd: 5000,
        token0Amount: 8.5,
        token1Amount: 5000,
        tickLower: 276000,
        tickUpper: 277000,
        fees0: 0.025,
        fees1: 15.2,
        feesUsd: 15.2,
        pnlUsd: 125.5,
        pnlPct: 2.51,
        ilUsd: -45.2,
        ilPct: -0.9,
        apr: 125.5,
        createdAt: '2024-06-10T08:30:00Z',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      }
    ]
  }
}

export const realDataService = new RealDataService()
