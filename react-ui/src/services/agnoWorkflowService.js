/**
 * DyFlow v3.4 Agno Workflow Service
 * 連接到真實的 Agno Workflow API (port 8001)
 */

class AgnoWorkflowService {
  constructor() {
    this.baseUrl = 'http://localhost:8001'
    this.workflowId = null
    this.isRunning = false
    this.agents = [
      'SupervisorAgent',
      'HealthGuardAgent', 
      'MarketIntelAgent',
      'PortfolioManagerAgent',
      'StrategyAgent',
      'ExecutionAgent',
      'RiskSentinelAgent'
    ]
  }

  /**
   * 檢查 Agno Workflow 服務是否可用
   */
  async checkConnection() {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ Agno Workflow 服務連接成功:', data)
        return { connected: true, data }
      } else {
        console.warn('⚠️ Agno Workflow 服務響應異常:', response.status)
        return { connected: false, error: `HTTP ${response.status}` }
      }
    } catch (error) {
      console.warn('⚠️ Agno Workflow 服務連接失敗:', error.message)
      return { connected: false, error: error.message }
    }
  }

  /**
   * 啟動 DyFlow Workflow
   */
  async startWorkflow() {
    try {
      console.log('🚀 正在啟動 DyFlow Workflow...')
      
      // 首先檢查連接
      const connectionCheck = await this.checkConnection()
      if (!connectionCheck.connected) {
        throw new Error(`Agno Workflow 服務不可用: ${connectionCheck.error}`)
      }

      // 啟動 workflow
      const response = await fetch(`${this.baseUrl}/workflow/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          workflow_name: 'dyflow_v34',
          agents: this.agents,
          config: {
            market_intel_interval: 15, // 15秒掃描間隔
            risk_sentinel_interval: 10, // 10秒風險監控間隔
            fee_collection_time: '02:00', // UTC 02:00 費用收集
            preferred_tokens_bsc: ['BNB', 'USDC', 'USD1', 'USDT'],
            preferred_tokens_solana: ['ETH', 'WBTC', 'WSOL', 'SOL', 'USDT', 'USDC'],
            risk_limits: {
              il_cut: -8.0, // IL 熔斷線 -8%
              var_cut: 4.0, // VaR 95% 熔斷線 4%
              sigma_cut: 0.5 // 波動率熔斷線 50%
            }
          }
        })
      })

      if (response.ok) {
        const result = await response.json()
        this.workflowId = result.workflow_id
        this.isRunning = true
        
        console.log('✅ DyFlow Workflow 啟動成功:', result)
        return { success: true, workflowId: this.workflowId, data: result }
      } else {
        const error = await response.text()
        throw new Error(`Workflow 啟動失敗: ${error}`)
      }
    } catch (error) {
      console.error('❌ DyFlow Workflow 啟動失敗:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 停止 DyFlow Workflow
   */
  async stopWorkflow() {
    try {
      if (!this.workflowId) {
        throw new Error('沒有運行中的 Workflow')
      }

      const response = await fetch(`${this.baseUrl}/workflow/${this.workflowId}/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        this.isRunning = false
        this.workflowId = null
        
        console.log('✅ DyFlow Workflow 停止成功:', result)
        return { success: true, data: result }
      } else {
        const error = await response.text()
        throw new Error(`Workflow 停止失敗: ${error}`)
      }
    } catch (error) {
      console.error('❌ DyFlow Workflow 停止失敗:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 獲取 Workflow 狀態
   */
  async getWorkflowStatus() {
    try {
      if (!this.workflowId) {
        return { running: false, agents: [] }
      }

      const response = await fetch(`${this.baseUrl}/workflow/${this.workflowId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const status = await response.json()
        return { success: true, data: status }
      } else {
        throw new Error(`獲取狀態失敗: ${response.status}`)
      }
    } catch (error) {
      console.error('❌ 獲取 Workflow 狀態失敗:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 獲取 Agent 狀態
   */
  async getAgentStatus(agentName) {
    try {
      if (!this.workflowId) {
        throw new Error('沒有運行中的 Workflow')
      }

      const response = await fetch(`${this.baseUrl}/workflow/${this.workflowId}/agent/${agentName}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const status = await response.json()
        return { success: true, data: status }
      } else {
        throw new Error(`獲取 Agent 狀態失敗: ${response.status}`)
      }
    } catch (error) {
      console.error(`❌ 獲取 ${agentName} 狀態失敗:`, error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 發送指令到 Agent
   */
  async sendAgentCommand(agentName, command, params = {}) {
    try {
      if (!this.workflowId) {
        throw new Error('沒有運行中的 Workflow')
      }

      const response = await fetch(`${this.baseUrl}/workflow/${this.workflowId}/agent/${agentName}/command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          command,
          params
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log(`✅ ${agentName} 指令執行成功:`, result)
        return { success: true, data: result }
      } else {
        const error = await response.text()
        throw new Error(`Agent 指令執行失敗: ${error}`)
      }
    } catch (error) {
      console.error(`❌ ${agentName} 指令執行失敗:`, error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 模擬模式 - 當 Agno 服務不可用時使用
   */
  async simulateWorkflowStart() {
    console.log('🎭 使用模擬模式啟動 DyFlow Workflow...')
    
    this.workflowId = `sim_${Date.now()}`
    this.isRunning = true
    
    // 模擬啟動序列
    const phases = [
      '連接 Agno Workflow',
      '初始化 MarketIntel Agent', 
      '啟動 Strategy Agent',
      '準備 Execution Agent',
      '激活 RiskSentinel Agent'
    ]
    
    return {
      success: true,
      workflowId: this.workflowId,
      mode: 'simulation',
      phases,
      message: '模擬模式：DyFlow Workflow 已啟動'
    }
  }

  /**
   * 獲取當前狀態
   */
  getStatus() {
    return {
      workflowId: this.workflowId,
      isRunning: this.isRunning,
      agents: this.agents
    }
  }
}

// 創建單例實例
export const agnoWorkflowService = new AgnoWorkflowService()
