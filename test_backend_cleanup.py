#!/usr/bin/env python3
"""
Backend 清理驗證腳本
測試清理後的 Backend 架構是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """測試所有核心模塊的導入"""
    print("🧪 測試模塊導入...")
    
    try:
        # 測試 Utils
        from backend.utils import Config, DyFlowException, get_utc_timestamp
        print("✅ Utils 導入成功")
        
        # 測試 Events
        from backend.events.dyflow_events import EventBus, PoolEvent
        print("✅ Events 導入成功")
        
        # 測試 Tools
        from backend.tools.pool_scanner_tool import PoolScannerTool
        from backend.tools.meteora_dlmm_tool import MeteoraDLMMTool
        from backend.tools.pancake_subgraph_tool import PancakeSubgraphTool
        print("✅ Tools 導入成功")
        
        # 測試 Services
        from backend.services.real_data_service import real_data_service
        print("✅ Services 導入成功")
        
        # 測試 API
        from backend.api.agno_workflow_api import AgnoWorkflowAPI
        print("✅ API 導入成功")
        
        # 測試 Agents (簡化測試，避免初始化問題)
        import backend.agents.core_agent
        import backend.agents.supervisor_agent
        import backend.agents.health_guard_agent
        print("✅ Agents 導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 導入失敗: {e}")
        return False

def test_file_structure():
    """測試文件結構"""
    print("\n📁 測試文件結構...")

    backend_path = Path("backend")
    all_passed = True

    # 檢查核心目錄
    required_dirs = ["agents", "api", "tools", "utils", "events", "services", "workflows", "core"]
    for dir_name in required_dirs:
        dir_path = backend_path / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/ 目錄存在")
        else:
            print(f"❌ {dir_name}/ 目錄缺失")
            all_passed = False

    # 檢查核心文件
    required_files = [
        "agents/core_agent.py",
        "agents/supervisor_agent.py",
        "api/agno_workflow_api.py",
        "tools/pool_scanner_tool.py",
        "utils/config.py",
        "events/dyflow_events.py",
        "services/real_data_service.py",
        "workflows/dyflow_v34.yaml"
    ]

    for file_path in required_files:
        full_path = backend_path / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 缺失")
            all_passed = False

    return all_passed

def test_removed_files():
    """測試重複文件是否已移除"""
    print("\n🗑️ 測試重複文件移除...")

    backend_path = Path("backend")
    all_removed = True

    # 應該已移除的文件
    removed_files = [
        "tools/enhanced_pool_scanner.py",
        "tools/real_data_provider_tool.py",
        "tools/chain_scanner_tool.py",
        "tools/damm_scanner_tool.py",
        "workflows/dyflow_agno_complete.py",
        "workflows/lp_monitoring_workflow.py",
        "api/portfolio_api.py",
        "api/real_data_fetcher.py",
        "core/agno_scheduler.py",
        "core/dyflow_agno_scheduler.py",
        "utils/models_v3.py",
        "utils/database.py"
    ]

    for file_path in removed_files:
        full_path = backend_path / file_path
        if not full_path.exists():
            print(f"✅ {file_path} 已移除")
        else:
            print(f"❌ {file_path} 仍然存在")
            all_removed = False

    return all_removed

def count_files():
    """統計文件數量"""
    print("\n📊 文件統計...")
    
    backend_path = Path("backend")
    
    # 統計各目錄文件數量
    for subdir in ["agents", "api", "tools", "utils", "events", "services", "workflows", "core"]:
        dir_path = backend_path / subdir
        if dir_path.exists():
            py_files = list(dir_path.glob("*.py"))
            yaml_files = list(dir_path.glob("*.yaml"))
            total_files = len(py_files) + len(yaml_files)
            print(f"📁 {subdir}/: {total_files} 個文件 ({len(py_files)} .py, {len(yaml_files)} .yaml)")

def test_basic_functionality():
    """測試基本功能"""
    print("\n⚙️ 測試基本功能...")
    
    try:
        # 測試配置加載
        from backend.utils.config import Config
        config = Config()
        print("✅ 配置加載成功")
        
        # 測試時間戳生成
        from backend.utils.helpers import get_utc_timestamp
        timestamp = get_utc_timestamp()
        print(f"✅ 時間戳生成成功: {timestamp}")
        
        # 測試事件總線
        from backend.events.dyflow_events import EventBus, EventType
        event_bus = EventBus()
        print("✅ 事件總線創建成功")
        
        # 測試池子掃描工具創建
        from backend.tools.pool_scanner_tool import PoolScannerTool
        scanner = PoolScannerTool()
        print("✅ 池子掃描工具創建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧹 Backend 清理驗證測試")
    print("=" * 50)
    
    # 運行所有測試
    tests = [
        test_imports,
        test_file_structure, 
        test_removed_files,
        test_basic_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"⚠️ 測試 {test.__name__} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test.__name__} 異常: {e}")
    
    # 統計文件
    count_files()
    
    # 總結
    print("\n" + "=" * 50)
    print(f"🎯 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 Backend 清理驗證 **完全成功**！")
        print("✅ 所有重複代碼已移除")
        print("✅ 核心功能正常工作") 
        print("✅ 架構清潔且符合 PRD")
    else:
        print("⚠️ 部分測試失敗，需要進一步檢查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
