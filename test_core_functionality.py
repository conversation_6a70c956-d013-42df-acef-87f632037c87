#!/usr/bin/env python3
"""
DyFlow v3.4 核心功能測試
簡化版本，避免複雜的依賴鏈，專注於核心邏輯驗證
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# 載入環境變量
load_dotenv()

def test_environment_configuration():
    """測試環境配置"""
    print("🔧 測試環境配置")
    
    results = {}
    
    # 檢查必要的環境變量
    required_vars = [
        'BSC_PRIVATE_KEY', 'SOLANA_PRIVATE_KEY', 
        'BSC_RPC_URL', 'SOLANA_RPC_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變量: {missing_vars}")
        results['env_vars'] = False
    else:
        print("✅ 所有必要環境變量已配置")
        results['env_vars'] = True
    
    # 檢查私鑰格式
    bsc_key = os.getenv('BSC_PRIVATE_KEY')
    solana_key = os.getenv('SOLANA_PRIVATE_KEY')
    
    bsc_valid = bsc_key and bsc_key.startswith('0x') and len(bsc_key) == 66
    solana_valid = solana_key and len(solana_key) > 40  # 簡化驗證
    
    print(f"  BSC 私鑰格式: {'✅' if bsc_valid else '❌'}")
    print(f"  Solana 私鑰格式: {'✅' if solana_valid else '❌'}")
    
    results['private_keys'] = bsc_valid and solana_valid
    
    return results

def test_core_data_structures():
    """測試核心數據結構"""
    print("\n📊 測試核心數據結構")
    
    results = {}
    
    try:
        # 測試池子數據結構
        pool_data = {
            "pool_id": "0x1234567890123456789012345678901234567890",
            "chain": "bsc",
            "token0": "WBNB",
            "token1": "USDT",
            "tvl": 15000000.0,
            "fee_tvl_pct": 0.08,
            "sigma": 0.025,
            "spread": 0.0003,
            "current_price": 300.0,
            "timestamp": datetime.now().isoformat()
        }
        
        # 驗證池子數據
        required_fields = ["pool_id", "chain", "tvl", "fee_tvl_pct", "sigma"]
        pool_valid = all(field in pool_data for field in required_fields)
        
        print(f"  池子數據結構: {'✅' if pool_valid else '❌'}")
        results['pool_data'] = pool_valid
        
        # 測試策略計劃數據結構
        lp_plan = {
            "plan_id": f"lp_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "pool_id": pool_data["pool_id"],
            "strategy": "SPOT_BALANCED",
            "notional_usd": 10000.0,
            "ranges": [
                {
                    "tick_lower": -1000,
                    "tick_upper": 1000,
                    "amount_usd": 5000.0,
                    "weight": 0.5
                },
                {
                    "tick_lower": -2000,
                    "tick_upper": 2000,
                    "amount_usd": 5000.0,
                    "weight": 0.5
                }
            ],
            "risk_profile": {
                "il_fuse_threshold": -0.08,
                "var_threshold": 0.04,
                "max_slippage": 0.02
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # 驗證策略計劃
        plan_required_fields = ["plan_id", "pool_id", "strategy", "notional_usd", "ranges"]
        plan_valid = all(field in lp_plan for field in plan_required_fields)
        
        print(f"  策略計劃結構: {'✅' if plan_valid else '❌'}")
        results['lp_plan'] = plan_valid
        
        # 測試交易事件數據結構
        tx_event = {
            "position_id": f"pos_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "status": "completed",
            "chain": pool_data["chain"],
            "tx_hash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
            "gas_used": 250000,
            "gas_price": 5000000000,
            "timestamp": datetime.now().isoformat()
        }
        
        # 驗證交易事件
        tx_required_fields = ["position_id", "status", "chain", "tx_hash"]
        tx_valid = all(field in tx_event for field in tx_required_fields)
        
        print(f"  交易事件結構: {'✅' if tx_valid else '❌'}")
        results['tx_event'] = tx_valid
        
        return results
        
    except Exception as e:
        print(f"❌ 數據結構測試失敗: {e}")
        return {'pool_data': False, 'lp_plan': False, 'tx_event': False}

def test_strategy_logic():
    """測試策略邏輯"""
    print("\n🎯 測試策略邏輯")
    
    results = {}
    
    try:
        # 模擬策略選擇邏輯
        def select_strategy(pool_data):
            sigma = pool_data.get("sigma", 0.02)
            fee_tvl_pct = pool_data.get("fee_tvl_pct", 0.1)
            spread = pool_data.get("spread", 0.001)
            
            if sigma <= 0.01 and fee_tvl_pct < 0.2:
                return "SPOT_BALANCED"
            elif 0.01 < sigma <= 0.03:
                return "CURVE_BALANCED"
            elif spread < 0.0005:
                return "BID_ASK_BALANCED"
            elif sigma > 0.03 or fee_tvl_pct >= 0.4:
                return "SPOT_IMBALANCED_DAMM"
            else:
                return "SPOT_BALANCED"
        
        # 測試不同的池子條件
        test_pools = [
            {"sigma": 0.005, "fee_tvl_pct": 0.1, "spread": 0.001},  # 應該選 SPOT_BALANCED
            {"sigma": 0.02, "fee_tvl_pct": 0.15, "spread": 0.001},  # 應該選 CURVE_BALANCED
            {"sigma": 0.02, "fee_tvl_pct": 0.15, "spread": 0.0003}, # 應該選 BID_ASK_BALANCED
            {"sigma": 0.05, "fee_tvl_pct": 0.5, "spread": 0.001},   # 應該選 SPOT_IMBALANCED_DAMM
        ]
        
        expected_strategies = [
            "SPOT_BALANCED", "CURVE_BALANCED", "BID_ASK_BALANCED", "SPOT_IMBALANCED_DAMM"
        ]
        
        strategy_tests_passed = 0
        for i, pool in enumerate(test_pools):
            selected = select_strategy(pool)
            expected = expected_strategies[i]
            if selected == expected:
                strategy_tests_passed += 1
                print(f"  策略選擇 {i+1}: ✅ {selected}")
            else:
                print(f"  策略選擇 {i+1}: ❌ 期望 {expected}, 得到 {selected}")
        
        results['strategy_selection'] = strategy_tests_passed == len(test_pools)
        
        # 測試價格範圍計算
        def calculate_price_ranges(strategy, current_price, sigma, amount_usd):
            if strategy == "SPOT_BALANCED":
                # 對稱分布
                range_width = current_price * sigma * 2
                return [
                    {
                        "min_price": current_price - range_width,
                        "max_price": current_price + range_width,
                        "amount": amount_usd,
                        "weight": 1.0
                    }
                ]
            elif strategy == "CURVE_BALANCED":
                # 曲線分布
                range_width = current_price * sigma * 3
                return [
                    {
                        "min_price": current_price - range_width * 0.5,
                        "max_price": current_price + range_width * 0.5,
                        "amount": amount_usd * 0.6,
                        "weight": 0.6
                    },
                    {
                        "min_price": current_price - range_width,
                        "max_price": current_price + range_width,
                        "amount": amount_usd * 0.4,
                        "weight": 0.4
                    }
                ]
            else:
                # 其他策略的簡化實現
                return [
                    {
                        "min_price": current_price * 0.9,
                        "max_price": current_price * 1.1,
                        "amount": amount_usd,
                        "weight": 1.0
                    }
                ]
        
        # 測試價格範圍計算
        ranges = calculate_price_ranges("SPOT_BALANCED", 300.0, 0.02, 10000.0)
        ranges_valid = len(ranges) > 0 and all("min_price" in r and "max_price" in r for r in ranges)
        
        print(f"  價格範圍計算: {'✅' if ranges_valid else '❌'}")
        results['price_ranges'] = ranges_valid
        
        return results
        
    except Exception as e:
        print(f"❌ 策略邏輯測試失敗: {e}")
        return {'strategy_selection': False, 'price_ranges': False}

def test_risk_management():
    """測試風險管理邏輯"""
    print("\n🛡️ 測試風險管理邏輯")
    
    results = {}
    
    try:
        # 模擬風險計算
        def calculate_risk_metrics(position_data):
            # 模擬 IL 計算
            il_raw = position_data.get("price_change", 0) ** 2 * 0.25  # 簡化 IL 公式
            fee_accumulated = position_data.get("fee_accumulated", 0)
            notional = position_data.get("notional", 10000)
            
            il_net = il_raw - (fee_accumulated / notional)
            
            # 模擬 VaR 計算
            volatility = position_data.get("volatility", 0.02)
            var_95 = volatility * 1.645  # 95% VaR
            
            return {
                "il_raw": il_raw,
                "il_net": il_net,
                "var_95": var_95,
                "risk_score": max(abs(il_net), var_95)
            }
        
        # 測試不同風險情況
        test_positions = [
            {"price_change": 0.05, "fee_accumulated": 100, "notional": 10000, "volatility": 0.02},  # 正常
            {"price_change": 0.15, "fee_accumulated": 50, "notional": 10000, "volatility": 0.05},   # 高風險
            {"price_change": -0.1, "fee_accumulated": 200, "notional": 10000, "volatility": 0.01},  # IL 風險
        ]
        
        risk_tests_passed = 0
        for i, position in enumerate(test_positions):
            metrics = calculate_risk_metrics(position)
            
            # 檢查風險指標是否合理
            if all(isinstance(v, (int, float)) for v in metrics.values()):
                risk_tests_passed += 1
                print(f"  風險計算 {i+1}: ✅ IL_net={metrics['il_net']:.4f}, VaR={metrics['var_95']:.4f}")
            else:
                print(f"  風險計算 {i+1}: ❌ 計算結果無效")
        
        results['risk_calculation'] = risk_tests_passed == len(test_positions)
        
        # 測試風險觸發邏輯
        def check_risk_triggers(metrics, thresholds):
            il_breach = metrics["il_net"] < thresholds.get("il_fuse", -0.08)
            var_breach = metrics["var_95"] > thresholds.get("var_limit", 0.04)
            
            return {
                "il_breach": il_breach,
                "var_breach": var_breach,
                "should_exit": il_breach or var_breach
            }
        
        # 測試觸發條件
        high_risk_metrics = {"il_net": -0.10, "var_95": 0.06}
        triggers = check_risk_triggers(high_risk_metrics, {"il_fuse": -0.08, "var_limit": 0.04})
        
        trigger_logic_valid = triggers["should_exit"] == True
        print(f"  風險觸發邏輯: {'✅' if trigger_logic_valid else '❌'}")
        results['risk_triggers'] = trigger_logic_valid
        
        return results
        
    except Exception as e:
        print(f"❌ 風險管理測試失敗: {e}")
        return {'risk_calculation': False, 'risk_triggers': False}

def test_dca_exit_logic():
    """測試 DCA 退出邏輯"""
    print("\n📉 測試 DCA 退出邏輯")
    
    results = {}
    
    try:
        # 模擬 DCA 退出計劃
        def create_dca_exit_plan(position_value, trigger_type, num_batches=5):
            # 計算批次百分比 (遞減策略)
            weights = []
            for i in range(num_batches):
                weight = 1.0 / (2 ** i)  # 1, 0.5, 0.25, 0.125, ...
                weights.append(weight)
            
            # 正規化
            total_weight = sum(weights)
            percentages = [w / total_weight for w in weights]
            
            # 創建批次
            batches = []
            for i, percentage in enumerate(percentages):
                batch = {
                    "batch_id": f"batch_{i+1}",
                    "percentage": percentage,
                    "amount": position_value * percentage,
                    "status": "pending",
                    "max_slippage": 0.02 / num_batches  # 分散滑點
                }
                batches.append(batch)
            
            return {
                "plan_id": f"dca_exit_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "trigger": trigger_type,
                "total_batches": num_batches,
                "batches": batches,
                "total_value": position_value
            }
        
        # 測試 DCA 計劃創建
        exit_plan = create_dca_exit_plan(10000.0, "il_breach", 5)
        
        plan_valid = (
            len(exit_plan["batches"]) == 5 and
            abs(sum(b["percentage"] for b in exit_plan["batches"]) - 1.0) < 0.001 and
            abs(sum(b["amount"] for b in exit_plan["batches"]) - 10000.0) < 0.01
        )
        
        print(f"  DCA 計劃創建: {'✅' if plan_valid else '❌'}")
        results['dca_plan_creation'] = plan_valid
        
        # 測試批次執行邏輯
        def execute_batch(batch, current_price, slippage_tolerance):
            # 模擬批次執行
            actual_slippage = min(batch["max_slippage"], slippage_tolerance)
            execution_price = current_price * (1 - actual_slippage)
            
            return {
                "batch_id": batch["batch_id"],
                "executed_amount": batch["amount"],
                "execution_price": execution_price,
                "actual_slippage": actual_slippage,
                "status": "completed",
                "timestamp": datetime.now().isoformat()
            }
        
        # 測試批次執行
        first_batch = exit_plan["batches"][0]
        execution_result = execute_batch(first_batch, 300.0, 0.015)
        
        execution_valid = (
            execution_result["status"] == "completed" and
            execution_result["actual_slippage"] <= first_batch["max_slippage"]
        )
        
        print(f"  批次執行邏輯: {'✅' if execution_valid else '❌'}")
        results['batch_execution'] = execution_valid
        
        return results
        
    except Exception as e:
        print(f"❌ DCA 退出邏輯測試失敗: {e}")
        return {'dca_plan_creation': False, 'batch_execution': False}

async def main():
    """主測試函數"""
    print("🚀 DyFlow v3.4 核心功能測試")
    print("=" * 50)
    
    all_results = {}
    
    # 測試 1: 環境配置
    all_results.update(test_environment_configuration())
    
    # 測試 2: 核心數據結構
    all_results.update(test_core_data_structures())
    
    # 測試 3: 策略邏輯
    all_results.update(test_strategy_logic())
    
    # 測試 4: 風險管理
    all_results.update(test_risk_management())
    
    # 測試 5: DCA 退出邏輯
    all_results.update(test_dca_exit_logic())
    
    # 總結結果
    print("\n" + "=" * 50)
    print("📊 核心功能測試結果總結")
    print("=" * 50)
    
    passed = 0
    total = len(all_results)
    
    for test_name, result in all_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    print(f"📈 成功率: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:  # 80% 通過率
        print("🎉 核心功能測試基本通過！")
        return True
    else:
        print("⚠️ 部分核心功能需要修復")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
