#!/usr/bin/env python3
"""
測試 Agno Framework 初始化
驗證 Workflow 和 Agent 創建
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.append(str(Path(__file__).parent))

try:
    from backend.workflows.dyflow_agno_workflow import DyFlowAgnoWorkflow, AGNO_AVAILABLE
    print("✅ DyFlowAgnoWorkflow 導入成功")
except ImportError as e:
    print(f"❌ DyFlowAgnoWorkflow 導入失敗: {e}")
    sys.exit(1)

def test_agno_availability():
    """測試 Agno Framework 可用性"""
    print("\n=== 測試 Agno Framework 可用性 ===")
    
    if AGNO_AVAILABLE:
        print("✅ Agno Framework 可用")
        try:
            from agno.agent import Agent
            from agno.team import Team
            from agno.workflow import Workflow
            from agno.models.ollama import Ollama
            print("✅ Agno 核心組件導入成功")
            return True
        except ImportError as e:
            print(f"❌ Agno 組件導入失敗: {e}")
            return False
    else:
        print("❌ Agno Framework 不可用")
        return False

def test_workflow_creation():
    """測試 Workflow 創建"""
    print("\n=== 測試 Workflow 創建 ===")
    
    try:
        # 創建 workflow
        workflow = DyFlowAgnoWorkflow(session_id="test-session")
        print("✅ DyFlowAgnoWorkflow 創建成功")
        
        # 檢查基本屬性
        print(f"  Session ID: {workflow.session_id}")
        print(f"  Description: {workflow.description}")
        print(f"  Current Phase: {workflow.current_phase}")
        
        return workflow
    except Exception as e:
        print(f"❌ Workflow 創建失敗: {e}")
        return None

def test_agents_initialization(workflow):
    """測試 Agents 初始化"""
    print("\n=== 測試 Agents 初始化 ===")
    
    if not workflow:
        print("❌ Workflow 不存在，跳過 Agents 測試")
        return False
    
    try:
        # 檢查 agents 是否存在
        agents = [
            ('supervisor_agent', 'SupervisorAgent'),
            ('health_agent', 'HealthGuardAgent'),
            ('market_intel_agent', 'MarketIntelAgent'),
            ('strategy_agent', 'StrategyAgent'),
            ('execution_agent', 'ExecutionAgent'),
            ('risk_agent', 'RiskSentinelAgent')
        ]
        
        for attr_name, agent_name in agents:
            if hasattr(workflow, attr_name):
                agent = getattr(workflow, attr_name)
                if agent:
                    print(f"✅ {agent_name} 初始化成功")
                else:
                    print(f"❌ {agent_name} 為 None")
            else:
                print(f"❌ {agent_name} 屬性不存在")
        
        return True
    except Exception as e:
        print(f"❌ Agents 初始化檢查失敗: {e}")
        return False

def test_team_creation(workflow):
    """測試 Team 創建"""
    print("\n=== 測試 Team 創建 ===")
    
    if not workflow:
        print("❌ Workflow 不存在，跳過 Team 測試")
        return False
    
    try:
        if hasattr(workflow, 'dyflow_team') and workflow.dyflow_team:
            team = workflow.dyflow_team
            print("✅ DyFlow Team 創建成功")
            print(f"  Team Name: {team.name}")
            print(f"  Team Mode: {team.mode}")
            print(f"  Members Count: {len(team.members)}")
            return True
        else:
            print("❌ DyFlow Team 不存在")
            return False
    except Exception as e:
        print(f"❌ Team 創建檢查失敗: {e}")
        return False

def test_ollama_connection():
    """測試 Ollama 連接"""
    print("\n=== 測試 Ollama 連接 ===")
    
    try:
        if AGNO_AVAILABLE:
            from agno.models.ollama import Ollama
            
            # 創建 Ollama 模型實例
            model = Ollama(id="qwen2.5:3b", host="http://localhost:11434")
            print("✅ Ollama 模型實例創建成功")
            print(f"  Model ID: {model.id}")
            print(f"  Host: {model.host}")
            return True
        else:
            print("❌ Agno Framework 不可用，無法測試 Ollama")
            return False
    except Exception as e:
        print(f"❌ Ollama 連接測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始 Agno Framework 初始化測試")
    
    results = {}
    
    # 測試 1: Agno 可用性
    results['agno_available'] = test_agno_availability()
    
    # 測試 2: Workflow 創建
    workflow = test_workflow_creation()
    results['workflow_creation'] = workflow is not None
    
    # 測試 3: Agents 初始化
    results['agents_initialization'] = test_agents_initialization(workflow)
    
    # 測試 4: Team 創建
    results['team_creation'] = test_team_creation(workflow)
    
    # 測試 5: Ollama 連接
    results['ollama_connection'] = test_ollama_connection()
    
    # 總結結果
    print("\n" + "="*50)
    print("📊 測試結果總結")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有 Agno Framework 測試通過！")
        return True
    else:
        print("⚠️ 部分測試失敗，需要檢查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
