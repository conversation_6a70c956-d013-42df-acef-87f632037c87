#!/usr/bin/env python3
"""
DyFlow v3.4 統一啟動腳本
整合所有功能：Agno Workflow API + WebSocket 池子掃描 + React UI
"""

import asyncio
import subprocess
import sys
import os
import time
import signal
from pathlib import Path
from typing import List, Optional

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class DyFlowUnifiedLauncher:
    """DyFlow v3.4 統一啟動器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = False
        
    def start_agno_workflow_api(self):
        """啟動 Agno Workflow API (包含 WebSocket 池子掃描)"""
        print("🚀 啟動 Agno Workflow API...")
        
        cmd = [
            sys.executable,
            "backend/api/agno_workflow_api.py"
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes.append(process)
        print("✅ Agno Workflow API 已啟動 (PID: {})".format(process.pid))
        return process
    
    def start_react_ui(self):
        """啟動 React UI"""
        print("🌐 啟動 React UI...")
        
        react_dir = project_root / "frontend"
        
        # 檢查是否需要安裝依賴
        if not (react_dir / "node_modules").exists():
            print("📦 安裝 React UI 依賴...")
            install_process = subprocess.run(
                ["npm", "install"],
                cwd=react_dir,
                capture_output=True,
                text=True
            )
            
            if install_process.returncode != 0:
                print(f"❌ React UI 依賴安裝失敗: {install_process.stderr}")
                return None
        
        # 啟動開發服務器
        cmd = ["npm", "run", "dev"]
        
        process = subprocess.Popen(
            cmd,
            cwd=react_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes.append(process)
        print("✅ React UI 已啟動 (PID: {})".format(process.pid))
        return process
    
    def check_prerequisites(self):
        """檢查前置條件"""
        print("🔍 檢查前置條件...")
        
        # 檢查 Ollama
        try:
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if "qwen2.5:3b" not in result.stdout:
                print("⚠️  警告: 未找到 qwen2.5:3b 模型")
                print("   請運行: ollama pull qwen2.5:3b")
            else:
                print("✅ Ollama qwen2.5:3b 模型已就緒")
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Ollama 未安裝或未運行")
            print("   請安裝 Ollama: https://ollama.ai/")
            return False
        
        # 檢查 Node.js
        try:
            result = subprocess.run(
                ["node", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            print(f"✅ Node.js {result.stdout.strip()}")
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Node.js 未安裝")
            print("   請安裝 Node.js: https://nodejs.org/")
            return False
        
        return True
    
    def setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            print(f"\n⏹️  收到信號 {signum}，正在停止所有服務...")
            self.stop_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def stop_all(self):
        """停止所有進程"""
        print("🛑 停止所有服務...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ 進程 {process.pid} 已停止")
            except subprocess.TimeoutExpired:
                print(f"⚠️  強制終止進程 {process.pid}")
                process.kill()
            except Exception as e:
                print(f"❌ 停止進程 {process.pid} 失敗: {e}")
        
        self.processes.clear()
        self.running = False
    
    def monitor_processes(self):
        """監控進程狀態"""
        while self.running:
            for i, process in enumerate(self.processes[:]):
                if process.poll() is not None:
                    print(f"⚠️  進程 {process.pid} 已退出 (返回碼: {process.returncode})")
                    self.processes.remove(process)
            
            time.sleep(2)
    
    def start_all(self):
        """啟動所有服務"""
        print("🚀 DyFlow v3.4 統一啟動器")
        print("=" * 50)
        
        # 檢查前置條件
        if not self.check_prerequisites():
            print("❌ 前置條件檢查失敗，退出")
            return False
        
        # 設置信號處理器
        self.setup_signal_handlers()
        
        try:
            # 啟動 Agno Workflow API
            api_process = self.start_agno_workflow_api()
            if not api_process:
                print("❌ Agno Workflow API 啟動失敗")
                return False
            
            # 等待 API 啟動
            print("⏳ 等待 API 啟動...")
            time.sleep(3)
            
            # 啟動 React UI
            ui_process = self.start_react_ui()
            if not ui_process:
                print("❌ React UI 啟動失敗")
                return False
            
            # 等待 UI 啟動
            print("⏳ 等待 UI 啟動...")
            time.sleep(5)
            
            self.running = True
            
            print("\n" + "=" * 50)
            print("🎉 DyFlow v3.4 已成功啟動！")
            print("=" * 50)
            print("📊 Agno Workflow API: http://localhost:8001")
            print("🌐 React UI: http://localhost:3000")
            print("📡 WebSocket: ws://localhost:8001/ws")
            print("=" * 50)
            print("💡 功能特性:")
            print("   ✅ 7 個 Agno Agents (根據 PRD v3.3)")
            print("   ✅ 8-phase 啟動序列")
            print("   ✅ WebSocket 實時池子掃描")
            print("   ✅ CoreAgent 聊天對話")
            print("   ✅ 真實 API 數據整合")
            print("=" * 50)
            print("按 Ctrl+C 停止所有服務")
            
            # 監控進程
            self.monitor_processes()
            
            return True
            
        except Exception as e:
            print(f"❌ 啟動失敗: {e}")
            self.stop_all()
            return False

def main():
    """主函數"""
    launcher = DyFlowUnifiedLauncher()
    
    try:
        success = launcher.start_all()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號")
    except Exception as e:
        print(f"❌ 意外錯誤: {e}")
        sys.exit(1)
    finally:
        launcher.stop_all()
    
    print("👋 DyFlow v3.4 已停止")

if __name__ == "__main__":
    main()
