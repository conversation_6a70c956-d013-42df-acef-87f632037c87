/**
 * DyFlow v3.4 Event Contract (Avro)
 * PRD Section 5 - Event Contract
 * All events persisted via JetStream; retention=7 d.
 */

@namespace("com.dyflow.events")
protocol DyFlow {

  /**
   * Pool Event - 池子事件
   * 由 MarketIntelAgent 發出，包含池子基本信息和風險指標
   */
  record PoolEvent {
    string pool_id;           // 池子唯一標識
    string chain;             // 鏈名稱 (bsc/solana)
    double tvl_usd;           // 總鎖定價值 (USD)
    double fee_tvl_pct;       // 費用/TVL 百分比
    double sigma;             // 波動率 (σ)
    double spread;            // 價差
    long ts;                  // 時間戳 (Unix timestamp)
  }

  /**
   * Risk Profile - 風險配置
   * 每筆 LP 建倉隨身攜帶的動態風控參數集
   */
  record RiskProfile {
    double il_cut;            // IL 熔斷閾值 (默認 -8%)
    double var_cut;           // VaR 熔斷閾值
    double sigma_cut;         // 波動率熔斷閾值
    long holding_window;      // 持倉窗口 (秒)
    string exit_asset;        // 退出資產
  }

  /**
   * LP Plan - LP 計劃
   * 由 StrategyAgent 生成，包含策略和風險配置
   */
  record LPPlan {
    string plan_id;           // 計劃唯一標識
    string pool_id;           // 目標池子ID
    string strategy;          // 策略類型 (SPOT_BALANCED/CURVE_BALANCED/BID_ASK_BALANCED/SPOT_IMBALANCED_DAMM)
    double k;                 // 策略參數
    double notional_usd;      // 名義金額 (USD)
    RiskProfile risk_profile; // 風險配置
    long ts;                  // 時間戳
  }

  /**
   * Transaction Event - 交易事件
   * 由 ExecutionAgent 發出，記錄交易狀態和結果
   */
  record TxEvent {
    string position_id;       // 倉位唯一標識
    string status;            // 交易狀態 (pending/confirmed/failed)
    string chain;             // 鏈名稱
    string exit_asset;        // 退出資產
    double qty;               // 數量
    string tx_hash;           // 交易哈希
    long ts;                  // 時間戳
  }

  /**
   * Exit Request - 退出請求
   * 由 RiskSentinelAgent 發出，觸發倉位退出
   */
  record ExitRequest {
    string position_id;       // 倉位唯一標識
    string exit_asset;        // 退出資產
    string reason;            // 退出原因 (il_breach/var_breach/timeout)
    long ts;                  // 時間戳
  }

  /**
   * Agent Task State - Agent 任務狀態
   * 用於 Agent Timeline 顯示
   */
  record AgentTaskState {
    string agent_name;        // Agent 名稱
    string state;             // 狀態 (idle/RUN/ACK/NACK)
    string task;              // 當前任務描述
    int phase;                // 所屬階段 (0-8)
    long ts;                  // 時間戳
  }

  /**
   * System Health - 系統健康狀態
   * 由 HealthGuardAgent 發出
   */
  record SystemHealth {
    string component;         // 組件名稱
    double score;             // 健康分數 (0-1)
    double latency_ms;        // 延遲 (毫秒)
    string status;            // 狀態 (healthy/degraded/failed)
    long ts;                  // 時間戳
  }

  /**
   * Launch Proposal - 啟動提案
   * 由 OnboardingAgent 生成，用戶確認後啟動系統
   */
  record LaunchProposal {
    string proposal_id;       // 提案唯一標識
    array<PoolEvent> hot_pools; // 熱門池子列表
    double total_capital_usd; // 總資本 (USD)
    array<LPPlan> suggested_plans; // 建議的 LP 計劃
    long ts;                  // 時間戳
  }

  /**
   * Launch Confirmed - 啟動確認
   * 用戶確認啟動提案後的事件
   */
  record LaunchConfirmed {
    string proposal_id;       // 提案ID
    string user_id;           // 用戶ID
    array<string> approved_plans; // 批准的計劃ID列表
    long ts;                  // 時間戳
  }

  /**
   * Risk Update - 風險更新
   * 由 RiskSentinelAgent 定期發出
   */
  record RiskUpdate {
    string position_id;       // 倉位ID
    double il_raw;            // 原始 IL
    double il_net;            // 淨 IL
    double var_95;            // 95% VaR
    double sigma_1m;          // 1分鐘波動率
    string risk_level;        // 風險等級 (low/medium/high/critical)
    long ts;                  // 時間戳
  }

  /**
   * Fee Collection - 費用收集
   * 由 FeeCollectorAgent 發出
   */
  record FeeCollection {
    string position_id;       // 倉位ID
    double fee_amount_usd;    // 收集的費用 (USD)
    string fee_token;         // 費用代幣
    string collection_type;   // 收集類型 (auto_harvest/manual)
    long ts;                  // 時間戳
  }

}
