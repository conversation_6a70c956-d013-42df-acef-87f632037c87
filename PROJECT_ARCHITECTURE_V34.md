# DyFlow v3.4 項目架構文檔

## 📁 項目結構 (整理後)

```
dyflow_new/
├── 🚀 start_dyflow_v34_unified.py     # 統一啟動腳本
├── 📋 PROJECT_ARCHITECTURE_V34.md     # 本文檔
├── 📋 PROJECT_STRUCTURE.md            # 項目結構說明
├── 📋 README.md                       # 項目說明
├── 📋 REAL_API_INTEGRATION_SUMMARY.md # API 整合說明
│
├── 📂 src/                            # 核心源代碼
│   ├── 🤖 agents/                     # 7 個 DyFlow Agents
│   │   ├── base_agent.py
│   │   ├── execution_agent.py
│   │   ├── health_guard_agent.py
│   │   ├── market_intel_agent.py
│   │   ├── portfolio_agent.py
│   │   ├── risk_sentinel_agno.py
│   │   └── strategy_agent.py
│   │
│   ├── 🔧 tools/                      # Agent 工具集
│   │   ├── enhanced_pool_scanner.py   # 池子掃描工具
│   │   ├── real_data_provider_tool.py # 真實數據提供工具
│   │   ├── supabase_db_tool.py        # 數據庫工具
│   │   └── ...
│   │
│   ├── 📊 services/                   # 核心服務
│   │   └── real_data_service.py       # 真實數據服務
│   │
│   ├── 🔄 workflows/                  # Agno 工作流
│   │   ├── dyflow_agno_workflow.py
│   │   └── lp_monitoring_workflow.py
│   │
│   └── 🛠️ utils/                      # 工具類
│       ├── config.py
│       ├── logger.py
│       └── models.py
│
├── 📂 web_ui/                         # 後端 API
│   ├── 🌐 agno_workflow_api.py        # 主 API (含 WebSocket 池子掃描)
│   ├── real_data_fetcher.py           # 數據獲取器
│   └── requirements.txt
│
├── 📂 react-ui/                       # 前端 React UI
│   ├── 📂 src/
│   │   ├── 📂 components/
│   │   │   └── layout/
│   │   │       └── DyFlowV34Simple.jsx # 主界面組件
│   │   ├── 📂 contexts/
│   │   │   └── WebSocketContext.jsx    # WebSocket 上下文
│   │   ├── 📂 lib/
│   │   │   └── wsHub.ts               # WebSocket Hub
│   │   └── 📂 store/
│   │       └── useAgno.ts             # Zustand 狀態管理
│   │
│   ├── package.json
│   └── vite.config.js
│
├── 📂 config/                         # 配置文件
│   ├── config.yaml
│   └── networks.yaml
│
├── 📂 workflow/                       # 工作流配置
│   └── dyflow_v34.yaml
│
├── 📂 scripts/                        # 腳本工具
│   ├── websocket_server.py            # 獨立 WebSocket 服務器
│   └── start_dyflow_v33.py           # v3.3 啟動腳本
│
└── 📂 logs/                          # 日誌文件
```

## 🏗️ 架構設計

### 1. 核心組件

#### 🤖 Agno Agents (7個)
根據 DyFlow v3.3 PRD 規範：
- **SupervisorAgent** (Phase 0): 系統初始化和生命週期管理
- **HealthGuardAgent** (Phase 1): 系統健康監控和驗證
- **MarketIntelAgent** (Phase 4): 市場數據收集和池子掃描
- **PortfolioManagerAgent** (Phase 5): 投資組合管理和 NAV 計算
- **StrategyAgent** (Phase 6): LP 策略生成和優化
- **ExecutionAgent** (Phase 7): 交易執行和監控
- **RiskSentinelAgent** (Phase 8): 風險監控和投資組合保護

#### 🔄 8-Phase 啟動序列
1. **Phase 0**: System Initialization (SupervisorAgent)
2. **Phase 1**: Health Check (HealthGuardAgent)
3. **Phase 2**: UI Startup (WebUI)
4. **Phase 3**: Wallet Test (WalletProbe)
5. **Phase 4**: Market Intelligence (MarketIntelAgent)
6. **Phase 5**: Portfolio Management (PortfolioManagerAgent)
7. **Phase 6**: Strategy Generation (StrategyAgent)
8. **Phase 7**: Transaction Execution (ExecutionAgent)
9. **Phase 8**: Risk Monitoring (RiskSentinelAgent)

### 2. 數據流架構

#### 📡 WebSocket 實時通訊
```
Real APIs → real_data_service → agno_workflow_api → WebSocket → React UI
    ↓              ↓                    ↓              ↓           ↓
PancakeSwap    池子數據緩存        實時廣播        wsHub      Zustand Store
Meteora        30秒更新循環        事件驅動        自動重連    狀態管理
CoinGecko      格式化轉換          多客戶端        錯誤處理    UI 更新
```

#### 🔄 事件驅動架構 (DyFlow v3.3)
```
MarketIntel → bus.pool → Strategy → bus.lpplan → Execution → bus.tx → RiskSentinel
     ↓                      ↓                       ↓                      ↓
  池子掃描              LP 計劃生成              交易執行              風險監控
  15秒間隔              風險映射                LP 建立              10秒監控
  實時推送              策略選擇                交易確認              IL/VaR 檢查
```

### 3. 技術棧

#### 後端
- **Agno Framework**: Agent 協調和工作流管理
- **FastAPI**: REST API 和 WebSocket 服務
- **Ollama qwen2.5:3b**: 本地 LLM 模型
- **Structlog**: 結構化日誌
- **Asyncio**: 異步任務處理

#### 前端
- **React 18**: 用戶界面框架
- **Vite**: 構建工具
- **Tailwind CSS**: 樣式框架
- **Zustand**: 狀態管理
- **WebSocket**: 實時通訊

#### 數據源
- **PancakeSwap V3 Subgraph**: BSC 池子數據
- **Meteora DLMM v2 API**: Solana 池子數據
- **CoinGecko API**: 代幣價格數據

## 🚀 啟動方式

### 統一啟動 (推薦)
```bash
python start_dyflow_v34_unified.py
```

### 分別啟動
```bash
# 1. 啟動後端 API
python web_ui/agno_workflow_api.py

# 2. 啟動前端 UI
cd react-ui && npm run dev
```

## 🔧 配置要求

### 前置條件
1. **Ollama**: 安裝並運行 qwen2.5:3b 模型
2. **Node.js**: v18+ 用於 React UI
3. **Python**: 3.9+ 用於後端服務

### 環境變量
```bash
# API 配置
DYFLOW_API_HOST=localhost
DYFLOW_API_PORT=8001

# WebSocket 配置
DYFLOW_WS_URL=ws://localhost:8001/ws

# 數據源配置
PANCAKESWAP_API_KEY=your_api_key
METEORA_API_URL=https://dammv2-api.meteora.ag
COINGECKO_API_URL=https://api.coingecko.com/api/v3
```

## 📊 功能特性

### ✅ 已實現
- 7 個 Agno Agents 協調
- 8-phase 啟動序列
- WebSocket 實時池子掃描
- CoreAgent 聊天對話
- 真實 API 數據整合
- React UI 實時更新
- 事件驅動架構

### 🔄 進行中
- LP 策略執行
- 風險管理系統
- 交易執行引擎

### 📋 待實現
- Supabase 數據持久化
- 多鏈錢包整合
- 高級風險指標

## 🧹 代碼整理說明

### 已清理的文件
- 移除所有 `test_*.py` 測試文件
- 清理 `backup/` 目錄
- 移除過時的文檔和數據文件
- 整合重複的功能代碼

### 保留的核心文件
- `src/` 目錄下的所有核心代碼
- `react-ui/` 前端界面
- `web_ui/` 後端 API
- `config/` 配置文件
- `workflow/` 工作流配置

### 整合的功能
- WebSocket 池子掃描整合到主 API
- 統一的啟動腳本
- 事件驅動架構優化
- 真實數據源整合

## 📝 開發指南

### 添加新 Agent
1. 在 `src/agents/` 創建新 Agent 類
2. 繼承 `base_agent.py`
3. 在 `agno_workflow_api.py` 註冊
4. 更新工作流配置

### 添加新工具
1. 在 `src/tools/` 創建工具類
2. 實現標準工具接口
3. 在相應 Agent 中引用

### 修改 UI 組件
1. 編輯 `react-ui/src/components/`
2. 使用 Zustand store 管理狀態
3. 通過 WebSocket 接收實時數據

這個架構確保了 DyFlow v3.4 符合 PRD 規範，同時保持代碼的清潔和可維護性。
