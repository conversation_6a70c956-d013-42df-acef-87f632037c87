#!/usr/bin/env python3
"""
測試 DyFlow v3.3 新增功能
驗證按照規範添加的 4 個優先功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_execution_agent_enhancements():
    """測試 ExecutionAgent 的 swap_to_exit_asset 功能"""
    print("🧪 測試 ExecutionAgent Patch")
    print("=" * 50)
    
    try:
        from src.agents.execution_agent import ExecutionAgent
        
        agent = ExecutionAgent()
        
        # 檢查 swap_to_exit_asset 方法是否存在
        if hasattr(agent, 'swap_to_exit_asset'):
            print("✅ swap_to_exit_asset 方法已實現")
        else:
            print("❌ swap_to_exit_asset 方法缺失")
            return False
        
        # 檢查 handle_exit_request 是否包含完整流程
        if hasattr(agent, 'handle_exit_request'):
            print("✅ handle_exit_request 方法已實現")
        else:
            print("❌ handle_exit_request 方法缺失")
            return False
        
        # 檢查 Jupiter 和 PancakeSwap 兌換方法
        if hasattr(agent, '_swap_solana_jupiter') and hasattr(agent, '_swap_bsc_pancake'):
            print("✅ Jupiter v6 API 和 PancakeSwap multicall 支持已實現")
        else:
            print("❌ 兌換方法實現不完整")
            return False
        
        print("✅ ExecutionAgent Patch 完成")
        return True
        
    except Exception as e:
        print(f"❌ ExecutionAgent 測試失敗: {e}")
        return False

def test_risk_sentinel_il_net():
    """測試 RiskSentinelAgent 的 IL_net 公式"""
    print("\n🧪 測試 RiskSentinelAgent IL_net 公式")
    print("=" * 50)
    
    try:
        from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent

        agent = RiskSentinelAgnoAgent({})
        
        # 檢查 _calculate_il_net 方法
        if hasattr(agent, '_calculate_il_net'):
            print("✅ _calculate_il_net 方法已實現")
            
            # 測試 IL_net 計算
            test_position = {
                "il_raw": -0.05,  # -5% 原始 IL
                "fee_accrued_usd": 100,  # $100 累積手續費
                "notional_usd": 5000  # $5000 名義本金
            }
            
            il_net = agent._calculate_il_net(test_position)
            expected_il_net = -0.05 - (100 / 5000)  # -5% - 2% = -7%
            
            if abs(il_net - expected_il_net) < 0.001:
                print(f"✅ IL_net 計算正確: {il_net:.3f} (期望: {expected_il_net:.3f})")
            else:
                print(f"❌ IL_net 計算錯誤: {il_net:.3f} (期望: {expected_il_net:.3f})")
                return False
        else:
            print("❌ _calculate_il_net 方法缺失")
            return False
        
        print("✅ RiskSentinelAgent IL_net 公式實現完成")
        return True
        
    except Exception as e:
        print(f"❌ RiskSentinelAgent 測試失敗: {e}")
        return False

def test_fee_collector_scheduling():
    """測試 FeeCollector 排程機制"""
    print("\n🧪 測試 FeeCollector 排程機制")
    print("=" * 50)
    
    try:
        from src.tools.fee_collector_tool import FeeCollectorTool
        
        config = {
            'schedule': '0 2 * * *',  # UTC 02:00
            'wallet_signer': {},
            'dex_router': {}
        }
        
        tool = FeeCollectorTool(config)
        
        # 檢查排程相關方法
        required_methods = [
            'start_scheduled_collection',
            '_should_collect_now',
            '_update_fee_accruals',
            '_calculate_position_fee_accrual',
            '_update_position_fee_accrual'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(tool, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺失方法: {missing_methods}")
            return False
        else:
            print("✅ 所有排程方法已實現")
        
        # 檢查配置
        if tool.collection_schedule == '0 2 * * *':
            print("✅ UTC 02:00 排程配置正確")
        else:
            print(f"❌ 排程配置錯誤: {tool.collection_schedule}")
            return False
        
        print("✅ FeeCollector 10 min + UTC 02:00 排程機制完成")
        return True
        
    except Exception as e:
        print(f"❌ FeeCollector 測試失敗: {e}")
        return False

def test_ui_toast_notifications():
    """測試 UI Toast 通知功能"""
    print("\n🧪 測試 UI Toast 通知")
    print("=" * 50)
    
    try:
        # 檢查 Toast 組件文件
        toast_file = "react-ui/src/components/ui/toast.tsx"
        if os.path.exists(toast_file):
            print("✅ Toast 組件文件已創建")
        else:
            print("❌ Toast 組件文件缺失")
            return False
        
        # 檢查 wsHub 中的 Toast 功能
        wshub_file = "react-ui/src/lib/wsHub.ts"
        if os.path.exists(wshub_file):
            with open(wshub_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'showExitRequestToast' in content:
                print("✅ wsHub.ts 中的 Toast 功能已實現")
            else:
                print("❌ wsHub.ts 中缺少 Toast 功能")
                return False
        else:
            print("❌ wsHub.ts 文件不存在")
            return False
        
        # 檢查 App.jsx 中的 ToastContainer
        app_file = "react-ui/src/App.jsx"
        if os.path.exists(app_file):
            with open(app_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'ToastContainer' in content:
                print("✅ App.jsx 中的 ToastContainer 已添加")
            else:
                print("❌ App.jsx 中缺少 ToastContainer")
                return False
        else:
            print("❌ App.jsx 文件不存在")
            return False
        
        print("✅ UI Toast 通知功能完成")
        return True
        
    except Exception as e:
        print(f"❌ UI Toast 測試失敗: {e}")
        return False

def test_supervisor_rest_toggle():
    """測試 Supervisor REST Toggle"""
    print("\n🧪 測試 Supervisor REST Toggle")
    print("=" * 50)
    
    try:
        # 檢查事件格式
        from src.events.dyflow_events import GlobalsDiffEvent, TradingMode, create_globals_diff
        
        # 測試 GlobalsDiff 事件創建
        event = create_globals_diff(TradingMode.ACTIVE)
        
        if event.trading_mode == TradingMode.ACTIVE:
            print("✅ GlobalsDiff 事件格式正確")
        else:
            print("❌ GlobalsDiff 事件格式錯誤")
            return False
        
        # 檢查 wsHub 中的 globalsDiff 處理
        wshub_file = "react-ui/src/lib/wsHub.ts"
        if os.path.exists(wshub_file):
            with open(wshub_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'globalsDiff' in content and 'setTradingMode' in content:
                print("✅ wsHub.ts 中的 globalsDiff 處理已實現")
            else:
                print("❌ wsHub.ts 中缺少 globalsDiff 處理")
                return False
        
        # 檢查 API 端點 (通過檢查相關文件)
        api_files = [
            "web_ui/agno_workflow_api.py",
            "dyflow_real_data_backend.py"
        ]
        
        api_implemented = False
        for api_file in api_files:
            if os.path.exists(api_file):
                with open(api_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if '/api/trading_mode' in content or 'set_trading_mode' in content:
                    print(f"✅ {api_file} 中的 trading_mode API 已實現")
                    api_implemented = True
                    break
        
        if not api_implemented:
            print("❌ trading_mode API 端點未找到")
            return False
        
        print("✅ Supervisor REST Toggle 功能完成")
        return True
        
    except Exception as e:
        print(f"❌ Supervisor REST Toggle 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 DyFlow v3.3 新增功能測試")
    print("=" * 60)
    print("測試範圍: 按照規範添加的 4 個優先功能")
    print("1. ExecutionAgent Patch (swap_to_exit_asset)")
    print("2. RiskSentinelAgent IL_net 公式")
    print("3. FeeCollector 排程機制")
    print("4. UI Toast 通知")
    print("5. Supervisor REST Toggle")
    print("=" * 60)
    
    # 執行所有測試
    test_results = []
    
    test_results.append(("ExecutionAgent Patch", test_execution_agent_enhancements()))
    test_results.append(("RiskSentinel IL_net", test_risk_sentinel_il_net()))
    test_results.append(("FeeCollector 排程", test_fee_collector_scheduling()))
    test_results.append(("UI Toast 通知", test_ui_toast_notifications()))
    test_results.append(("Supervisor REST Toggle", test_supervisor_rest_toggle()))
    
    # 統計結果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<25} {status}")
    
    print("-" * 60)
    print(f"總計: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 恭喜！所有新增功能都已正確實現！")
        print("✅ DyFlow v3.3 端到端流程規範 100% 符合")
        print("🚀 你的編碼同事可以直接使用這些功能進行開發")
        return True
    else:
        print(f"\n⚠️ 還有 {total - passed} 個功能需要完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
