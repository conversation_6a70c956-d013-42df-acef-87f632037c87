#!/usr/bin/env python3
"""
DyFlow v3.4 PRD 合規性測試
驗證所有 v3.4 PRD 要求的功能實現
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_workflow_v34_config():
    """測試 workflow/dyflow_v34.yaml 配置"""
    print("🧪 測試 DyFlow v3.4 Workflow 配置")
    print("=" * 60)
    
    config_file = Path("workflow/dyflow_v34.yaml")
    
    if not config_file.exists():
        print("❌ workflow/dyflow_v34.yaml 文件不存在")
        return False
    
    try:
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 檢查必需的配置項
        required_sections = [
            "description",
            "globals",
            "kpi_targets", 
            "nodes",
            "event_topics",
            "security",
            "websocket",
            "api_endpoints"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in config:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺失配置段: {missing_sections}")
            return False
        
        # 檢查 OnboardingAgent 節點
        nodes = config.get("nodes", [])
        onboarding_node = next((n for n in nodes if n.get("id") == "onboarding"), None)
        
        if not onboarding_node:
            print("❌ 缺少 OnboardingAgent 節點")
            return False
        
        if onboarding_node.get("class") != "OnboardingAgent":
            print("❌ OnboardingAgent 類名不正確")
            return False
        
        # 檢查狀態機配置
        exec_node = next((n for n in nodes if n.get("id") == "exec"), None)
        risk_node = next((n for n in nodes if n.get("id") == "risk"), None)
        
        if exec_node and "state_machine" not in exec_node:
            print("❌ ExecutionAgent 缺少狀態機配置")
            return False
        
        if risk_node and "state_machine" not in risk_node:
            print("❌ RiskSentinelAgent 缺少狀態機配置")
            return False
        
        # 檢查 API 端點
        api_endpoints = config.get("api_endpoints", [])
        required_endpoints = ["/quickscan", "/confirm", "/trading_mode"]
        
        existing_endpoints = [ep.get("path") for ep in api_endpoints]
        missing_endpoints = [ep for ep in required_endpoints if ep not in existing_endpoints]
        
        if missing_endpoints:
            print(f"❌ 缺少 API 端點: {missing_endpoints}")
            return False
        
        print("✅ workflow/dyflow_v34.yaml 配置完整且符合 PRD 規範")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失敗: {e}")
        return False

def test_onboarding_agent():
    """測試 OnboardingAgent 實現"""
    print("\n🧪 測試 OnboardingAgent 實現")
    print("=" * 60)
    
    try:
        from src.agents.onboarding_agent import OnboardingAgent, QuickScanResult, LaunchProposal
        
        # 檢查類是否正確實現
        agent = OnboardingAgent()
        
        required_methods = [
            "trigger_quick_scan",
            "launch_confirmed",
            "_scan_chain_pools",
            "_build_launch_proposal"
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(agent, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ OnboardingAgent 缺少方法: {missing_methods}")
            return False
        
        # 檢查數據類
        try:
            scan_result = QuickScanResult(
                chain="sol",
                pools_found=10,
                top_pools=[],
                scan_duration=1.5,
                timestamp=datetime.now().isoformat()
            )
            print("✅ QuickScanResult 數據類正確")
        except Exception as e:
            print(f"❌ QuickScanResult 數據類錯誤: {e}")
            return False
        
        try:
            proposal = LaunchProposal(
                proposal_id="test_proposal",
                total_pools=20,
                recommended_pools=[],
                estimated_apy=25.5,
                risk_score=65.0,
                capital_requirement=25000.0,
                chains=["bsc", "sol"],
                timestamp=datetime.now().isoformat()
            )
            print("✅ LaunchProposal 數據類正確")
        except Exception as e:
            print(f"❌ LaunchProposal 數據類錯誤: {e}")
            return False
        
        print("✅ OnboardingAgent 實現完整且符合 PRD 規範")
        return True
        
    except ImportError as e:
        print(f"❌ OnboardingAgent 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ OnboardingAgent 測試失敗: {e}")
        return False

def test_avro_schemas():
    """測試 Avro Schema 定義"""
    print("\n🧪 測試 Avro Schema 定義")
    print("=" * 60)
    
    schema_file = Path("schemas/dyflow_events.avsc")
    
    if not schema_file.exists():
        print("❌ schemas/dyflow_events.avsc 文件不存在")
        return False
    
    try:
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # 檢查命名空間
        if schema.get("namespace") != "dyflow.events":
            print("❌ Schema 命名空間不正確")
            return False
        
        # 檢查必需的事件類型
        required_types = [
            "PoolEvent",
            "RiskProfile", 
            "LPPlan",
            "TxEvent",
            "ExitRequest",
            "LaunchEvent",
            "QuickScanResult",
            "LaunchProposal",
            "GlobalsDiff",
            "TaskStateChanged"
        ]
        
        schema_types = [t.get("name") for t in schema.get("types", [])]
        missing_types = [t for t in required_types if t not in schema_types]
        
        if missing_types:
            print(f"❌ 缺少 Schema 類型: {missing_types}")
            return False
        
        print("✅ Avro Schema 定義完整且符合 PRD 規範")
        return True
        
    except Exception as e:
        print(f"❌ Schema 文件解析失敗: {e}")
        return False

def test_state_machines():
    """測試狀態機實現"""
    print("\n🧪 測試狀態機實現")
    print("=" * 60)
    
    try:
        # 測試 ExecutionAgent 狀態機
        from src.agents.execution_agent import ExecutionAgent
        
        exec_agent = ExecutionAgent()
        
        # 檢查狀態機屬性
        required_states = ["IDLE", "SIGN", "BROADCAST", "CONFIRMED", "FAILED", "EXIT_SWAP"]
        
        missing_states = []
        for state in required_states:
            if not hasattr(exec_agent.State, state):
                missing_states.append(state)
        
        if missing_states:
            print(f"❌ ExecutionAgent 缺少狀態: {missing_states}")
            return False
        
        # 檢查狀態機方法
        required_methods = ["transition_state", "can_transition"]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(exec_agent, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ ExecutionAgent 缺少狀態機方法: {missing_methods}")
            return False
        
        print("✅ ExecutionAgent 狀態機實現正確")
        
        # 測試 RiskSentinelAgent 狀態機
        from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
        
        risk_agent = RiskSentinelAgnoAgent({})
        
        # 檢查狀態機屬性
        required_states = ["WATCHING", "BREACH", "EMIT_EXIT"]
        
        missing_states = []
        for state in required_states:
            if not hasattr(risk_agent.State, state):
                missing_states.append(state)
        
        if missing_states:
            print(f"❌ RiskSentinelAgent 缺少狀態: {missing_states}")
            return False
        
        print("✅ RiskSentinelAgent 狀態機實現正確")
        return True
        
    except Exception as e:
        print(f"❌ 狀態機測試失敗: {e}")
        return False

def test_launch_wizard():
    """測試 LaunchWizard 前端組件"""
    print("\n🧪 測試 LaunchWizard 前端組件")
    print("=" * 60)
    
    wizard_file = Path("react-ui/src/components/LaunchWizard/index.tsx")
    
    if not wizard_file.exists():
        print("❌ LaunchWizard 組件文件不存在")
        return False
    
    try:
        with open(wizard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查必需的功能
        required_features = [
            "QuickScanResult",
            "LaunchProposal", 
            "四步驟",
            "handleQuickScan",
            "handleConfirmLaunch",
            "/api/quickscan",
            "/api/confirm"
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ LaunchWizard 缺少功能: {missing_features}")
            return False
        
        print("✅ LaunchWizard 組件實現完整")
        return True
        
    except Exception as e:
        print(f"❌ LaunchWizard 測試失敗: {e}")
        return False

def test_zustand_store():
    """測試 Zustand Store 更新"""
    print("\n🧪 測試 Zustand Store 更新")
    print("=" * 60)
    
    store_file = Path("react-ui/src/store/useAgno.ts")
    
    if not store_file.exists():
        print("❌ useAgno.ts 文件不存在")
        return False
    
    try:
        with open(store_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查 wizardState 相關功能
        required_features = [
            "wizardState",
            "setWizardState",
            "openWizard",
            "closeWizard",
            "setWizardStep",
            "setWizardScanResults",
            "setWizardProposal"
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ Zustand Store 缺少 wizardState 功能: {missing_features}")
            return False
        
        print("✅ Zustand Store wizardState 功能完整")
        return True
        
    except Exception as e:
        print(f"❌ Zustand Store 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 DyFlow v3.4 PRD 合規性測試")
    print("=" * 80)
    print("測試範圍: 所有 v3.4 PRD 要求的功能實現")
    print("1. workflow/dyflow_v34.yaml 配置")
    print("2. OnboardingAgent 實現")
    print("3. Avro Schema 定義")
    print("4. 狀態機實現")
    print("5. LaunchWizard 前端組件")
    print("6. Zustand Store 更新")
    print("=" * 80)
    
    # 執行所有測試
    test_results = []
    
    test_results.append(("Workflow v3.4 配置", test_workflow_v34_config()))
    test_results.append(("OnboardingAgent 實現", test_onboarding_agent()))
    test_results.append(("Avro Schema 定義", test_avro_schemas()))
    test_results.append(("狀態機實現", test_state_machines()))
    test_results.append(("LaunchWizard 組件", test_launch_wizard()))
    test_results.append(("Zustand Store 更新", test_zustand_store()))
    
    # 統計結果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print("\n" + "=" * 80)
    print("📊 DyFlow v3.4 PRD 合規性測試結果")
    print("=" * 80)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<30} {status}")
    
    print("-" * 80)
    print(f"總計: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 恭喜！DyFlow v3.4 完全符合 PRD 規範！")
        print("✅ 所有 v3.4 功能都已正確實現")
        print("🚀 系統已準備好投入生產使用")
        print("\n📋 v3.4 新功能總結:")
        print("  • OnboardingAgent 快速掃描和啟動流程")
        print("  • LaunchWizard 四步驟 UI 引導")
        print("  • ExecutionAgent 和 RiskSentinel 狀態機")
        print("  • Avro Schema 標準化事件格式")
        print("  • 完整的 API 端點支持")
        print("  • 前端 Zustand Store 集成")
        return True
    else:
        print(f"\n⚠️ 還有 {total - passed} 個功能需要完善")
        print("請根據上述測試結果進行修復")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
